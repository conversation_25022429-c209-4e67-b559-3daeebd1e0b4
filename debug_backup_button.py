#!/usr/bin/env python3
"""
Debug del pulsante backup manuale
"""

import subprocess
import time
from pathlib import Path

def check_server_running():
    """Verifica se il server FastAPI è in esecuzione"""
    print("1. VERIFICA SERVER FASTAPI")
    print("-" * 30)
    
    try:
        # Verifica se il processo uvicorn è attivo
        result = subprocess.run(
            ["powershell", "-Command", "Get-Process | Where-Object {$_.ProcessName -like '*python*' -or $_.ProcessName -like '*uvicorn*'}"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0 and result.stdout.strip():
            print("✓ Processi Python/Uvicorn trovati:")
            print(result.stdout)
            return True
        else:
            print("✗ Nessun processo Python/Uvicorn trovato")
            return False
            
    except Exception as e:
        print(f"✗ Errore verifica server: {e}")
        return False

def check_backup_directory():
    """Verifica directory backup"""
    print("\n2. VERIFICA DIRECTORY BACKUP")
    print("-" * 30)
    
    backup_dir = Path("backups")
    
    if backup_dir.exists():
        print(f"✓ Directory backup esiste: {backup_dir.absolute()}")
        
        # Lista file nella directory
        files = list(backup_dir.glob("*"))
        print(f"✓ File nella directory: {len(files)}")
        
        for file in files:
            stat = file.stat()
            print(f"  - {file.name} ({stat.st_size} bytes, {time.ctime(stat.st_mtime)})")
        
        return True
    else:
        print("✗ Directory backup non esiste")
        return False

def test_backup_manager_direct():
    """Test diretto del backup manager"""
    print("\n3. TEST BACKUP MANAGER DIRETTO")
    print("-" * 35)
    
    try:
        from backup_manager import BackupManager
        
        print("✓ Import BackupManager riuscito")
        
        # Inizializza backup manager
        db_url = "postgresql://re77:271077@localhost:5432/AGENTE"
        backup_manager = BackupManager(db_url)
        
        print("✓ BackupManager inizializzato")
        
        # Test creazione backup
        print("⏳ Creazione backup di test...")
        backup_path = backup_manager.create_backup(format_type="custom")
        
        if backup_path:
            print(f"✓ Backup creato: {backup_path}")
            
            # Verifica file
            backup_file = Path(backup_path)
            if backup_file.exists():
                file_size = backup_file.stat().st_size
                print(f"✓ File verificato: {file_size} bytes")
                return True
            else:
                print("✗ File backup non trovato")
                return False
        else:
            print("✗ Backup fallito")
            return False
            
    except ImportError as e:
        print(f"✗ Errore import BackupManager: {e}")
        return False
    except Exception as e:
        print(f"✗ Errore backup manager: {e}")
        return False

def test_endpoint_availability():
    """Test disponibilità endpoint"""
    print("\n4. TEST ENDPOINT DISPONIBILITÀ")
    print("-" * 35)
    
    try:
        import urllib.request
        import urllib.error
        
        # Test endpoint backup manuale
        url = "http://localhost:8002/admin/backup/manual"
        
        try:
            # Crea richiesta POST semplice
            req = urllib.request.Request(url, method='POST')
            req.add_header('Content-Type', 'application/json')
            
            with urllib.request.urlopen(req, timeout=5) as response:
                status = response.getcode()
                print(f"✓ Endpoint raggiungibile: {status}")
                return True
                
        except urllib.error.HTTPError as e:
            if e.code == 401:
                print("✓ Endpoint raggiungibile (401 = non autenticato, normale)")
                return True
            elif e.code == 403:
                print("✓ Endpoint raggiungibile (403 = non autorizzato, normale)")
                return True
            else:
                print(f"? Endpoint risponde con errore: {e.code}")
                return False
                
        except urllib.error.URLError as e:
            print(f"✗ Endpoint non raggiungibile: {e}")
            return False
            
    except Exception as e:
        print(f"✗ Errore test endpoint: {e}")
        return False

def check_javascript_function():
    """Verifica funzione JavaScript"""
    print("\n5. VERIFICA FUNZIONE JAVASCRIPT")
    print("-" * 35)
    
    js_file = Path("static/js/config-management.js")
    
    if js_file.exists():
        print("✓ File JavaScript esiste")
        
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Cerca la funzione createManualBackup
        if 'async function createManualBackup()' in content:
            print("✓ Funzione createManualBackup trovata (async)")
        elif 'function createManualBackup()' in content:
            print("✓ Funzione createManualBackup trovata")
        else:
            print("✗ Funzione createManualBackup non trovata")
            return False
        
        # Cerca chiamata fetch
        if '/admin/backup/manual' in content:
            print("✓ Chiamata endpoint backup trovata")
        else:
            print("✗ Chiamata endpoint backup non trovata")
            return False
        
        # Cerca gestione errori
        if 'catch (error)' in content:
            print("✓ Gestione errori presente")
        else:
            print("? Gestione errori non trovata")
        
        return True
    else:
        print("✗ File JavaScript non trovato")
        return False

def main():
    print("DEBUG PULSANTE BACKUP MANUALE")
    print("=" * 50)
    
    results = []
    
    # Test 1: Server running
    results.append(("Server FastAPI", check_server_running()))
    
    # Test 2: Directory backup
    results.append(("Directory backup", check_backup_directory()))
    
    # Test 3: Backup manager diretto
    results.append(("Backup manager", test_backup_manager_direct()))
    
    # Test 4: Endpoint disponibilità
    results.append(("Endpoint API", test_endpoint_availability()))
    
    # Test 5: JavaScript function
    results.append(("Funzione JS", check_javascript_function()))
    
    # Risultati finali
    print("\n" + "=" * 50)
    print("RISULTATI DEBUG")
    print("=" * 50)
    
    all_ok = True
    for test_name, result in results:
        status = "✓ OK" if result else "✗ FAIL"
        print(f"{test_name:20}: {status}")
        if not result:
            all_ok = False
    
    print(f"\n" + "=" * 50)
    if all_ok:
        print("✓ TUTTI I TEST SUPERATI")
        print("Il pulsante backup dovrebbe funzionare!")
        print("\nSe non funziona ancora:")
        print("1. Verifica che sei loggato come admin")
        print("2. Apri console browser (F12) per errori JS")
        print("3. Controlla log server per errori backend")
    else:
        print("✗ ALCUNI TEST FALLITI")
        print("Il pulsante backup ha problemi!")
        print("\nAzioni da intraprendere:")
        print("1. Risolvi i test falliti")
        print("2. Riavvia il server se necessario")
        print("3. Verifica configurazione database")

if __name__ == "__main__":
    main()
