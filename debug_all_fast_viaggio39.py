#!/usr/bin/env python3
"""
Debug specifico per il campo all_fast del viaggio 39
"""

import sys
import os

# Aggiungi il percorso del progetto
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_all_fast_viaggio_39():
    """Test specifico per il campo all_fast del viaggio 39"""
    
    print("🔍 Debug campo all_fast per viaggio 39")
    print("=" * 60)
    
    try:
        from database import get_db
        from sqlalchemy import text
        
        db = next(get_db())
        viaggio_id = 39
        
        print(f"📊 Test per viaggio ID: {viaggio_id}")
        
        # 1. Verifica esistenza viaggio
        print("\n1️⃣ Verifica esistenza viaggio...")
        viaggio_check = db.execute(text("""
            SELECT id, viaggio, nave_id, visibile FROM "VIAGGIO" WHERE id = :viaggio_id
        """), {"viaggio_id": viaggio_id}).fetchone()
        
        if viaggio_check:
            print(f"   ✅ Viaggio trovato: {viaggio_check}")
            viaggio_nome = viaggio_check[1]
        else:
            print("   ❌ Viaggio non trovato")
            return False
        
        # 2. Verifica dati orari completi
        print("\n2️⃣ Verifica dati orari completi...")
        orari_data = db.execute(text("""
            SELECT
                id, viaggio_id, porto_arrivo, sbe, pilota_arrivo, all_fast,
                tug_arrivo, draft, soc, porto_di_destinazione, pilota_partenza,
                tug_partenza, foc, fo, "do", lo
            FROM "ORARI" WHERE viaggio_id = :viaggio_id
        """), {"viaggio_id": viaggio_id}).fetchone()
        
        if orari_data:
            print(f"   ✅ Orari trovati per viaggio {viaggio_id}")
            print(f"   📋 Dettagli orari:")
            print(f"      ID: {orari_data[0]}")
            print(f"      Viaggio ID: {orari_data[1]}")
            print(f"      Porto Arrivo: {orari_data[2]}")
            print(f"      SBE: {orari_data[3]}")
            print(f"      Pilota Arrivo: {orari_data[4]}")
            print(f"      ALL FAST: {orari_data[5]} ⭐")
            print(f"      Tug Arrivo: {orari_data[6]}")
            print(f"      Draft: {orari_data[7]}")
            print(f"      SOC: {orari_data[8]}")
            print(f"      Porto Destinazione: {orari_data[9]}")
            print(f"      Pilota Partenza: {orari_data[10]}")
            print(f"      Tug Partenza: {orari_data[11]}")
            print(f"      FOC: {orari_data[12]}")
            print(f"      FO: {orari_data[13]}")
            print(f"      DO: {orari_data[14]}")
            print(f"      LO: {orari_data[15]}")
            
            # Verifica specifica del campo all_fast
            all_fast_value = orari_data[5]
            if all_fast_value:
                print(f"\n   🎯 CAMPO ALL_FAST PRESENTE!")
                print(f"      Valore: {all_fast_value}")
                print(f"      Tipo: {type(all_fast_value)}")
                
                # Formatta per il template HTML
                if hasattr(all_fast_value, 'strftime'):
                    formatted_value = all_fast_value.strftime('%Y-%m-%dT%H:%M')
                    print(f"      Formato HTML: {formatted_value}")
                else:
                    print(f"      ⚠️ Valore non è un datetime: {all_fast_value}")
            else:
                print(f"\n   ❌ CAMPO ALL_FAST VUOTO O NULL!")
                print(f"      Valore: {all_fast_value}")
                
        else:
            print("   ❌ Nessun orario trovato per questo viaggio")
            return False
        
        # 3. Test della query usata dall'endpoint
        print("\n3️⃣ Test query endpoint...")
        endpoint_data = db.execute(text("""
            SELECT id, viaggio_id, porto_arrivo, sbe, pilota_arrivo, all_fast,
                   tug_arrivo, draft, soc, porto_di_destinazione, pilota_partenza,
                   tug_partenza, foc, fo, "do", lo
            FROM "ORARI" WHERE viaggio_id = :viaggio_id
        """), {"viaggio_id": viaggio_id}).fetchone()
        
        if endpoint_data:
            # Simula la logica dell'endpoint
            orari_dict = {
                "id": endpoint_data[0],
                "viaggio_id": endpoint_data[1],
                "porto_arrivo": endpoint_data[2],
                "sbe": endpoint_data[3].strftime('%Y-%m-%dT%H:%M') if endpoint_data[3] else None,
                "pilota_arrivo": endpoint_data[4].strftime('%Y-%m-%dT%H:%M') if endpoint_data[4] else None,
                "all_fast": endpoint_data[5].strftime('%Y-%m-%dT%H:%M') if endpoint_data[5] else None,
                "tug_arrivo": endpoint_data[6] if endpoint_data[6] is not None else 0,
                "draft": float(endpoint_data[7]) if endpoint_data[7] else None,
                "soc": endpoint_data[8].strftime('%Y-%m-%dT%H:%M') if endpoint_data[8] else None,
                "porto_di_destinazione": endpoint_data[9],
                "pilota_partenza": endpoint_data[10].strftime('%Y-%m-%dT%H:%M') if endpoint_data[10] else None,
                "tug_partenza": endpoint_data[11] if endpoint_data[11] is not None else 0,
                "foc": endpoint_data[12].strftime('%Y-%m-%dT%H:%M') if endpoint_data[12] else None,
                "fo": float(endpoint_data[13]) if endpoint_data[13] else None,
                "do": float(endpoint_data[14]) if endpoint_data[14] else None,
                "lo": float(endpoint_data[15]) if endpoint_data[15] else None
            }
            
            print(f"   ✅ Dati formattati per template:")
            print(f"      all_fast: '{orari_dict['all_fast']}'")
            
            # Verifica se il valore sarebbe visibile nel template
            if orari_dict['all_fast']:
                print(f"   🎉 IL CAMPO ALL_FAST DOVREBBE ESSERE VISIBILE!")
                print(f"      Template value: {{ orari.all_fast if orari and orari.all_fast else '' }}")
                print(f"      Risultato: '{orari_dict['all_fast']}'")
            else:
                print(f"   ❌ Il campo all_fast risulterebbe vuoto nel template")
                
        # 4. Test struttura tabella ORARI
        print("\n4️⃣ Verifica struttura tabella ORARI...")
        columns_info = db.execute(text("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns
            WHERE table_name = 'ORARI' AND column_name = 'all_fast'
        """)).fetchone()
        
        if columns_info:
            print(f"   ✅ Colonna all_fast trovata:")
            print(f"      Nome: {columns_info[0]}")
            print(f"      Tipo: {columns_info[1]}")
            print(f"      Nullable: {columns_info[2]}")
        else:
            print(f"   ❌ Colonna all_fast non trovata nella tabella ORARI!")
            
        return True
        
    except Exception as e:
        print(f"❌ Errore durante il test: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_template_rendering():
    """Test simulazione rendering template"""
    
    print("\n" + "=" * 60)
    print("🎨 Test simulazione rendering template")
    print("=" * 60)
    
    # Simula i dati che arriverebbero al template
    mock_orari = {
        "all_fast": "2024-12-23T14:30"  # Valore di esempio
    }
    
    # Simula la logica del template Jinja2
    template_condition = "orari and orari.all_fast"
    template_value = mock_orari.get("all_fast", "")
    
    print(f"📋 Dati mock orari: {mock_orari}")
    print(f"🔍 Condizione template: {template_condition}")
    print(f"📝 Valore template: '{template_value}'")
    
    # Test condizioni
    orari_exists = bool(mock_orari)
    all_fast_exists = bool(mock_orari.get("all_fast"))
    
    print(f"\n✅ Test condizioni:")
    print(f"   orari exists: {orari_exists}")
    print(f"   orari.all_fast exists: {all_fast_exists}")
    print(f"   Condizione completa: {orari_exists and all_fast_exists}")
    
    if orari_exists and all_fast_exists:
        print(f"🎉 Il campo dovrebbe essere visualizzato con valore: '{template_value}'")
    else:
        print(f"❌ Il campo risulterebbe vuoto")

if __name__ == "__main__":
    print("🚀 Debug campo all_fast viaggio 39")
    print("=" * 60)
    
    # Test 1: Database
    db_ok = test_all_fast_viaggio_39()
    
    # Test 2: Template
    test_template_rendering()
    
    print("\n" + "=" * 60)
    print("📊 Risultati:")
    print(f"  Database: {'✅ OK' if db_ok else '❌ ERRORE'}")
    
    if db_ok:
        print("\n💡 Suggerimenti:")
        print("1. Verifica che il browser non abbia cache del template")
        print("2. Controlla la console del browser per errori JavaScript")
        print("3. Verifica che ORARI_ESISTENTI contenga il campo all_fast")
        print("4. Ricarica la pagina con Ctrl+F5 per forzare il refresh")
    
    print("\n🔗 URL di test: http://localhost:8000/operativo/sof/viaggio/39")
