#!/usr/bin/env python3
"""
Script automatico per azzerare tutti i valori Agemar_GioiaTauro nella tabella NAVI
"""

from sqlalchemy import text
from database import SessionLocal
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def reset_agemar_values():
    """Azzera tutti i valori Agemar_GioiaTauro"""
    db = SessionLocal()
    
    try:
        # Prima controlla quanti valori ci sono
        result = db.execute(text('''
            SELECT COUNT(*) 
            FROM "NAVI" 
            WHERE "Agemar_GioiaTauro" IS NOT NULL AND "Agemar_GioiaTauro" != 0
        '''))
        
        count_before = result.scalar()
        
        logger.info(f"🔍 Valori Agemar_GioiaTauro da azzerare: {count_before}")
        
        if count_before == 0:
            logger.info("✅ Non ci sono valori da azzerare. Tutti i campi sono già vuoti.")
            return 0
        
        logger.info("🔄 Inizio azzeramento valori Agemar_GioiaTauro...")
        
        # Azzera tutti i valori impostandoli a NULL
        result = db.execute(text('''
            UPDATE "NAVI"
            SET "Agemar_GioiaTauro" = NULL
            WHERE "Agemar_GioiaTauro" IS NOT NULL
        '''))

        # In SQLAlchemy 2.0+, rowcount is accessed differently
        rows_affected = getattr(result, 'rowcount', 0)
        
        # Commit delle modifiche
        db.commit()
        
        logger.info(f"✅ Azzeramento completato!")
        logger.info(f"📊 Righe modificate: {rows_affected}")
        
        # Verifica finale
        result = db.execute(text('''
            SELECT COUNT(*) 
            FROM "NAVI" 
            WHERE "Agemar_GioiaTauro" IS NOT NULL AND "Agemar_GioiaTauro" != 0
        '''))
        
        count_after = result.scalar()
        
        if count_after == 0:
            logger.info(f"✅ VERIFICA RIUSCITA! Tutti i valori Agemar_GioiaTauro sono stati azzerati.")
        else:
            logger.warning(f"⚠️ ATTENZIONE: {count_after} valori sono ancora presenti!")
        
        return rows_affected
        
    except Exception as e:
        db.rollback()
        logger.error(f"❌ Errore durante l'azzeramento: {e}")
        return 0
        
    finally:
        db.close()

def main():
    """Funzione principale"""
    print("🗑️ AZZERAMENTO AUTOMATICO AGEMAR GIOIA TAURO")
    print("=" * 60)
    
    # Esegui l'azzeramento
    rows_affected = reset_agemar_values()
    
    if rows_affected > 0:
        print(f"\n🎉 AZZERAMENTO COMPLETATO!")
        print(f"✅ {rows_affected} valori Agemar_GioiaTauro sono stati azzerati")
        print(f"📊 Tutti i campi Agemar_GioiaTauro sono ora vuoti (NULL)")
    elif rows_affected == 0:
        print(f"\n✅ NESSUNA MODIFICA NECESSARIA")
        print(f"📊 Tutti i campi Agemar_GioiaTauro erano già vuoti")
    else:
        print(f"\n❌ AZZERAMENTO FALLITO")

if __name__ == "__main__":
    main()
