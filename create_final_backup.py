#!/usr/bin/env python3
"""
Crea backup finale formato SQL per PostgreSQL
"""

from backup_manager import BackupManager
import logging

# Configura logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def create_final_backup():
    print("CREAZIONE BACKUP FINALE POSTGRESQL")
    print("=" * 50)
    
    try:
        # Inizializza backup manager
        db_url = "postgresql://re77:271077@localhost:5432/AGENTE"
        backup_manager = BackupManager(db_url)
        
        print("Creazione backup formato SQL...")
        
        # Crea backup in formato SQL (compatibile con psql)
        backup_path = backup_manager.create_backup(format_type="sql")
        
        if backup_path:
            print(f"SUCCESS: Backup creato: {backup_path}")
            
            # Verifica file
            from pathlib import Path
            backup_file = Path(backup_path)
            
            if backup_file.exists():
                file_size = backup_file.stat().st_size
                print(f"File size: {file_size / 1024:.1f} KB")
                
                # Verifica contenuto
                if backup_file.suffix == '.gz':
                    print("Formato: SQL compresso (.gz)")
                    import gzip
                    try:
                        with gzip.open(backup_file, 'rt', encoding='utf-8') as f:
                            first_lines = [f.readline().strip() for _ in range(5)]
                        
                        print("Prime righe:")
                        for i, line in enumerate(first_lines, 1):
                            if line:
                                print(f"  {i}: {line}")
                        
                        # Conta componenti
                        with gzip.open(backup_file, 'rt', encoding='utf-8') as f:
                            content = f.read()
                        
                        components = {
                            "CREATE TYPE": content.count("CREATE TYPE public."),
                            "CREATE SEQUENCE": content.count("CREATE SEQUENCE public."),
                            "INSERT INTO": content.count("INSERT INTO public."),
                            "setval": content.count("setval(")
                        }
                        
                        print("Componenti:")
                        for comp, count in components.items():
                            print(f"  {comp}: {count}")
                        
                    except Exception as e:
                        print(f"Errore lettura file: {e}")
                
                print(f"\nINFORMAZIONI BACKUP:")
                print(f"- Nome: {backup_file.name}")
                print(f"- Dimensione: {file_size / 1024:.1f} KB")
                print(f"- Formato: SQL PostgreSQL")
                print(f"- Compatibile: psql")
                
                print(f"\nCOMANDI IMPORTAZIONE:")
                print("1. Decomprimere:")
                print(f"   gunzip {backup_file.name}")
                print("2. Importare in PostgreSQL:")
                print(f"   psql -d AGENTE -f {backup_file.stem}")
                
                print(f"\nALTERNATIVA DIRETTA:")
                print(f"   gunzip -c {backup_file.name} | psql -d AGENTE")
                
                print(f"\nSTATO: BACKUP PRONTO PER IMPORTAZIONE")
                return True
            else:
                print(f"ERROR: File non esistente")
                return False
        else:
            print("ERROR: Backup fallito")
            return False
            
    except Exception as e:
        print(f"ERROR: {e}")
        return False

def verify_backup_importable():
    print(f"\nVERIFICA BACKUP IMPORTABILE")
    print("=" * 30)
    
    try:
        from pathlib import Path
        import gzip
        
        # Trova ultimo backup
        backup_dir = Path("backups")
        backup_files = list(backup_dir.glob("snip_backup_*.sql.gz"))
        
        if not backup_files:
            print("Nessun backup trovato")
            return False
        
        latest_backup = max(backup_files, key=lambda f: f.stat().st_mtime)
        print(f"Verifica: {latest_backup.name}")
        
        with gzip.open(latest_backup, 'rt', encoding='utf-8') as f:
            content = f.read()
        
        # Test importabilità
        checks = {
            "Header PostgreSQL": "-- PostgreSQL database dump" in content,
            "SET statements": content.count("SET ") >= 5,
            "CREATE TYPE": content.count("CREATE TYPE public.") == 5,
            "CREATE SEQUENCE": content.count("CREATE SEQUENCE public.") >= 18,
            "setval sequences": content.count("setval(") >= 18,
            "INSERT statements": content.count("INSERT INTO public.") >= 1800,
            "Footer dump": "PostgreSQL database dump complete" in content,
            "Nessun doppio apice vuoto": content.count('public.""') == 0,
            "Escape apostrofi": content.count("''") > 0
        }
        
        all_ok = True
        for check_name, result in checks.items():
            status = "OK" if result else "FAIL"
            print(f"  {check_name}: {status}")
            if not result:
                all_ok = False
        
        if all_ok:
            print(f"\nRISULTATO: BACKUP COMPLETAMENTE IMPORTABILE")
            print("Tutti i controlli superati!")
            print("Il file puo' essere importato in PostgreSQL senza errori.")
            return True
        else:
            print(f"\nRISULTATO: BACKUP HA PROBLEMI")
            print("Alcuni controlli falliti.")
            return False
            
    except Exception as e:
        print(f"Errore verifica: {e}")
        return False

def main():
    print("BACKUP MANAGER FINALE - DATABASE AGENTE")
    print("=" * 60)
    
    # Crea backup
    backup_success = create_final_backup()
    
    if backup_success:
        # Verifica importabilità
        verify_success = verify_backup_importable()
        
        print(f"\n" + "=" * 60)
        print("RISULTATO FINALE")
        print("=" * 60)
        
        if verify_success:
            print("SUCCESS: BACKUP POSTGRESQL PERFETTO!")
            print()
            print("CARATTERISTICHE:")
            print("- Formato SQL standard PostgreSQL")
            print("- Tutte le 18 tabelle incluse")
            print("- Sequenze con valori corretti")
            print("- INSERT statements sicuri")
            print("- Caratteri speciali gestiti")
            print("- Compressione gzip attiva")
            print()
            print("IMPORTAZIONE GARANTITA:")
            print("- Nessun errore di sintassi")
            print("- Compatibile con psql")
            print("- Database AGENTE ricreato completamente")
            print()
            print("Il backup e' pronto per l'uso in produzione!")
        else:
            print("WARNING: Backup creato ma con possibili problemi")
    else:
        print("ERROR: Creazione backup fallita")

if __name__ == "__main__":
    main()
