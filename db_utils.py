import psycopg2
from psycopg2.extras import DictCursor
import traceback

def get_db_connection():
    """
    Crea una connessione al database PostgreSQL.
    
    Returns:
        tuple: (connection, cursor) se la connessione ha successo, (None, None) altrimenti
    """
    try:
        # Parametri di connessione
        params = {
            'dbname': 'AGENTE',
            'user': 're77',
            'password': '271077',
            'host': 'localhost',
            'port': '5432'
        }
        
        # Connessione al database
        conn = psycopg2.connect(**params)
        
        # Crea un cursore che restituisce dizionari
        cursor = conn.cursor(cursor_factory=DictCursor)
        
        return conn, cursor
    except Exception as e:
        print(f"Errore durante la connessione al database: {str(e)}")
        traceback.print_exc()
        return None, None

def get_navi_from_db():
    """
    Recupera tutte le navi dal database.
    
    Returns:
        list: Lista di dizionari contenenti i dati delle navi, o lista vuota in caso di errore
    """
    try:
        conn, cursor = get_db_connection()
        if not conn or not cursor:
            print("Impossibile connettersi al database")
            return []
        
        # Esegui la query sulla tabella NAVI
        cursor.execute('SELECT * FROM "NAVI"')
        
        # Recupera tutti i risultati
        rows = cursor.fetchall()
        
        # Converti i risultati in una lista di dizionari
        navi = []
        for row in rows:
            nave = {
                "id": row['id'],
                "nave": row['Nave'],
                "codice_nave": row['Codice_Nave'],
                "prefisso_viaggio": row['Prefisso_viaggio']
            }
            navi.append(nave)
        
        # Chiudi la connessione
        cursor.close()
        conn.close()
        
        return navi
    except Exception as e:
        print(f"Errore durante il recupero delle navi: {str(e)}")
        traceback.print_exc()
        return []

def add_nave_to_db(nome_nave, codice_nave, prefisso_viaggio):
    """
    Aggiunge una nuova nave al database.
    
    Args:
        nome_nave (str): Nome della nave
        codice_nave (str): Codice della nave
        prefisso_viaggio (str): Prefisso viaggio
        
    Returns:
        bool: True se l'operazione ha successo, False altrimenti
    """
    try:
        conn, cursor = get_db_connection()
        if not conn or not cursor:
            print("Impossibile connettersi al database")
            return False
        
        # Esegui la query per inserire una nuova nave
        cursor.execute(
            'INSERT INTO "NAVI" ("Nave", "Codice_Nave", "Prefisso_viaggio") VALUES (%s, %s, %s) RETURNING id',
            (nome_nave, codice_nave, prefisso_viaggio)
        )
        
        # Ottieni l'ID della nuova nave
        new_id = cursor.fetchone()[0]
        
        # Commit delle modifiche
        conn.commit()
        
        # Chiudi la connessione
        cursor.close()
        conn.close()
        
        print(f"Nave aggiunta con successo. ID: {new_id}")
        return True
    except Exception as e:
        print(f"Errore durante l'aggiunta della nave: {str(e)}")
        traceback.print_exc()
        
        # Rollback in caso di errore
        if conn:
            conn.rollback()
            cursor.close()
            conn.close()
        
        return False
