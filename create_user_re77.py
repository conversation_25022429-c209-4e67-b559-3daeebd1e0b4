#!/usr/bin/env python3

import psycopg2
import sys

def create_user_re77():
    print("🔧 Creazione utente re77 in PostgreSQL...")
    
    try:
        # Connessione come postgres admin
        print("🔗 Connessione come postgres...")
        conn = psycopg2.connect(
            host='localhost',
            database='postgres',
            user='postgres',
            password='271077',
            connect_timeout=5
        )
        conn.autocommit = True
        cursor = conn.cursor()
        
        print("✅ Connesso come postgres")
        
        # Verifica se l'utente re77 esiste già
        cursor.execute("SELECT 1 FROM pg_roles WHERE rolname='re77'")
        user_exists = cursor.fetchone()
        
        if user_exists:
            print("ℹ️  Utente re77 esiste già")
        else:
            print("➕ Creazione utente re77...")
            cursor.execute("CREATE USER re77 WITH PASSWORD '271077'")
            print("✅ Utente re77 creato")
        
        # Assegna privilegi sul database AGENTE
        print("🔑 Assegnazione privilegi...")
        cursor.execute("GRANT ALL PRIVILEGES ON DATABASE \"AGENTE\" TO re77")
        print("✅ Privilegi assegnati su database AGENTE")
        
        conn.close()
        
        # Test connessione con re77
        print("\n🧪 Test connessione con re77...")
        conn_test = psycopg2.connect(
            host='localhost',
            database='AGENTE',
            user='re77',
            password='271077',
            connect_timeout=5
        )
        
        cursor_test = conn_test.cursor()
        
        # Lista tabelle
        cursor_test.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name;
        """)
        tables = cursor_test.fetchall()
        
        print(f"✅ Connessione re77 riuscita!")
        print(f"📋 Tabelle trovate: {len(tables)}")
        for table in tables:
            print(f"  - {table[0]}")
        
        conn_test.close()
        return True
        
    except psycopg2.OperationalError as e:
        print(f"❌ Errore connessione: {e}")
        return False
    except Exception as e:
        print(f"❌ Errore: {e}")
        return False

if __name__ == "__main__":
    success = create_user_re77()
    sys.exit(0 if success else 1)
