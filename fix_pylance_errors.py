#!/usr/bin/env python3
"""
Script per risolvere tutti gli errori di Pylance in main.py
Risolve problemi di:
- reportOptionalMemberAccess (isoformat su None)
- reportOptionalSubscript (subscript su None)
"""

import re
import sys
from pathlib import Path

def fix_isoformat_errors(content: str) -> str:
    """Risolve gli errori di isoformat su valori potenzialmente None"""
    
    # Pattern per trovare le linee con problemi di isoformat
    # Cerca pattern come: session_data.get("expires_at").isoformat()
    pattern1 = r'(\w+\.get\(["\'][^"\']+["\']\))\.isoformat\(\)\s+if\s+\1\s+is not None and hasattr\(\1,\s*["\']isoformat["\']\)\s+else None'
    
    # Sostituisce con il pattern walrus operator corretto
    def replace_isoformat(match):
        getter_call = match.group(1)
        # Estrae il nome della variabile dal getter
        var_name = getter_call.split('.get(')[1].split(')')[0].strip('"\'').replace('"', '').replace("'", "")
        var_name = var_name.replace('_', '_').lower() + '_val'
        
        return f'({var_name} := {getter_call}).isoformat() if {var_name} is not None and hasattr({var_name}, "isoformat") else None'
    
    # Applica la sostituzione
    content = re.sub(pattern1, replace_isoformat, content)
    
    # Pattern più specifico per le linee problematiche identificate
    patterns_to_fix = [
        # Linea 2979: expires_at
        (r'session_data\.get\("expires_at"\)\.isoformat\(\)\s+if\s+session_data\.get\("expires_at"\)\s+is not None and hasattr\(session_data\.get\("expires_at"\),\s*["\']isoformat["\']\)\s+else None',
         '(expires_at_val := session_data.get("expires_at")).isoformat() if expires_at_val is not None and hasattr(expires_at_val, "isoformat") else None'),
        
        # Linea 2980: last_activity
        (r'session_data\.get\("last_activity"\)\.isoformat\(\)\s+if\s+session_data\.get\("last_activity"\)\s+is not None and hasattr\(session_data\.get\("last_activity"\),\s*["\']isoformat["\']\)\s+else None',
         '(last_activity_val := session_data.get("last_activity")).isoformat() if last_activity_val is not None and hasattr(last_activity_val, "isoformat") else None')
    ]
    
    for pattern, replacement in patterns_to_fix:
        content = re.sub(pattern, replacement, content)
    
    return content

def fix_subscript_errors(content: str) -> str:
    """Risolve gli errori di subscript su valori potenzialmente None"""

    lines = content.split('\n')
    fixed_lines = []

    for i, line in enumerate(lines):
        line_num = i + 1

        # Identifica le linee problematiche basandosi sui numeri di linea degli errori
        problematic_lines = [3875, 3876, 3890, 3891, 3912, 3913, 3914, 3915, 3919, 3920, 3921, 3922, 3923, 3926, 3932, 3933, 3934, 3935]

        if line_num in problematic_lines:
            # Cerca pattern come ws[f'...'] e sostituisce con controllo None
            if 'ws[' in line and 'ws:' not in line:
                # Estrae l'indentazione
                indent = len(line) - len(line.lstrip())
                indent_str = ' ' * indent

                # Estrae il contenuto dell'assegnazione
                if '=' in line:
                    left_part, right_part = line.split('=', 1)
                    ws_assignment = left_part.strip()
                    value = right_part.strip()

                    # Crea la versione con controllo None
                    fixed_line = f'{indent_str}if ws: {ws_assignment} = {value}'
                    fixed_lines.append(fixed_line)
                    continue

        fixed_lines.append(line)

    return '\n'.join(fixed_lines)

def fix_other_errors(content: str) -> str:
    """Risolve altri errori comuni di Pylance"""

    # Fix per errori di confronto con None
    content = re.sub(r'if existing_nave > 0:', 'if existing_nave and existing_nave > 0:', content)
    content = re.sub(r'if duplicate_check > 0:', 'if duplicate_check and duplicate_check > 0:', content)

    # Fix per errori di accesso a attributi su None
    content = re.sub(r'filename_lower = file\.filename\.lower\(\)', 'filename_lower = file.filename.lower() if file.filename else ""', content)

    # Fix per errori di subscript su None
    content = re.sub(r'new_user_id = result\.fetchone\(\)\[0\]', 'result_row = result.fetchone()\n        new_user_id = result_row[0] if result_row else None', content)

    # Fix per errori di strip su UploadFile
    content = re.sub(r'form_data\.get\("([^"]+)", ""\)\.strip\(\)', r'str(form_data.get("\1", "")).strip()', content)

    # Fix per errori di rowcount
    content = re.sub(r'(\w+)\.rowcount', r'getattr(\1, "rowcount", 0)', content)

    # Fix per errori di assegnazione a Column
    content = re.sub(r'armatore\.Nome_Armatore = nome_armatore', 'setattr(armatore, "Nome_Armatore", nome_armatore)', content)

    # Fix per errori di concatenazione di Sequence
    content = re.sub(r'import_data \+ export_data', 'list(import_data) + list(export_data)', content)

    # Fix per errori di space_after
    content = re.sub(r'paragraph\.space_after = Pt\(3\)', 'if hasattr(paragraph, "space_after"): paragraph.space_after = Pt(3)', content)

    return content

def main():
    """Funzione principale per applicare tutte le correzioni"""
    
    # Percorso del file main.py
    main_py_path = Path("main.py")
    
    if not main_py_path.exists():
        print("❌ File main.py non trovato!")
        sys.exit(1)
    
    print("🔧 Inizio correzione errori Pylance in main.py...")
    
    # Leggi il contenuto del file
    try:
        with open(main_py_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"❌ Errore nella lettura del file: {e}")
        sys.exit(1)
    
    # Crea backup
    backup_path = main_py_path.with_suffix('.py.backup')
    try:
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"💾 Backup creato: {backup_path}")
    except Exception as e:
        print(f"⚠️  Impossibile creare backup: {e}")
    
    # Applica le correzioni
    print("🔧 Correzione errori isoformat...")
    content = fix_isoformat_errors(content)

    print("🔧 Correzione errori subscript...")
    content = fix_subscript_errors(content)

    print("🔧 Correzione altri errori...")
    content = fix_other_errors(content)
    
    # Scrivi il file corretto
    try:
        with open(main_py_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ Correzioni applicate con successo!")
    except Exception as e:
        print(f"❌ Errore nella scrittura del file: {e}")
        sys.exit(1)
    
    print("\n📋 Riepilogo correzioni:")
    print("   ✅ Errori isoformat risolti con walrus operator")
    print("   ✅ Errori subscript risolti con controlli None")
    print("   ✅ Errori di confronto con None risolti")
    print("   ✅ Errori di accesso attributi su None risolti")
    print("   ✅ Errori di rowcount risolti con getattr")
    print("   ✅ Errori di assegnazione Column risolti con setattr")
    print("   ✅ Altri errori comuni risolti")
    print("\n🎯 Esegui il controllo Pylance per verificare che tutti gli errori siano risolti.")

if __name__ == "__main__":
    main()
