# Dipendenze per il sistema 2FA SMS
# Installa con: pip install -r requirements_2fa.txt

# Libreria per richieste HTTP (invio SMS)
requests>=2.32.0

# Dipendenze già presenti nell'applicazione principale
fastapi
uvicorn
sqlalchemy
python-multipart
jinja2
python-jose[cryptography]
passlib[bcrypt]
reportlab
python-docx

# Dipendenze opzionali per provider SMS professionali
# twilio>=8.0.0  # Per provider Twilio
# smsapi-client>=2.0.0  # Per SMS.it o altri provider italiani
