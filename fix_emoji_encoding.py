#!/usr/bin/env python3
"""
Script per correggere gli emoji nel codice che causano errori di codifica su Windows
"""

import re
import os

def fix_emoji_in_file(file_path):
    """Corregge gli emoji in un file sostituendoli con equivalenti ASCII"""
    
    # Mappatura emoji -> equivalenti ASCII
    emoji_replacements = {
        '🔍': '[DEBUG]',
        '📍': '[LOC]',
        '✅': '[OK]',
        '⚠️': '[WARN]',
        '❌': '[ERROR]',
        '🚀': '[ROCKET]',
        '🔥': '[FIRE]',
        '🚨': '[ALERT]',
        '💥': '[BOOM]',
        '🎯': '[TARGET]',
        '📊': '[CHART]',
        '📈': '[GRAPH]',
        '📉': '[DOWN]',
        '📋': '[LIST]',
        '📝': '[NOTE]',
        '📄': '[DOC]',
        '📁': '[FOLDER]',
        '📂': '[OPEN_FOLDER]',
        '🔧': '[TOOL]',
        '⚙️': '[GEAR]',
        '🛠️': '[TOOLS]',
        '🔒': '[LOCK]',
        '🔓': '[UNLOCK]',
        '🔑': '[KEY]',
        '🌟': '[STAR]',
        '💡': '[IDEA]',
        '🎉': '[PARTY]',
        '🎊': '[CONFETTI]',
        '🏆': '[TROPHY]',
        '🥇': '[GOLD]',
        '🥈': '[SILVER]',
        '🥉': '[BRONZE]',
        '🚢': '[SHIP]',
        '⚓': '[ANCHOR]',
        '🛳️': '[CRUISE]',
        '🏢': '[BUILDING]',
        '🏭': '[FACTORY]',
        '🏗️': '[CONSTRUCTION]',
        '📅': '[CALENDAR]',
        '📆': '[DATE]',
        '⏰': '[CLOCK]',
        '⏱️': '[TIMER]',
        '⏲️': '[STOPWATCH]',
        '🔄': '[REFRESH]',
        '🔃': '[RELOAD]',
        '🔁': '[REPEAT]',
        '🔂': '[REPEAT_ONE]',
        '▶️': '[PLAY]',
        '⏸️': '[PAUSE]',
        '⏹️': '[STOP]',
        '⏭️': '[NEXT]',
        '⏮️': '[PREV]',
        '⏯️': '[PLAY_PAUSE]',
        '🔊': '[VOLUME]',
        '🔉': '[VOLUME_DOWN]',
        '🔈': '[VOLUME_LOW]',
        '🔇': '[MUTE]',
        '📢': '[SPEAKER]',
        '📣': '[MEGAPHONE]',
        '📯': '[HORN]',
        '🔔': '[BELL]',
        '🔕': '[NO_BELL]',
        '📞': '[PHONE]',
        '📱': '[MOBILE]',
        '💻': '[LAPTOP]',
        '🖥️': '[DESKTOP]',
        '🖨️': '[PRINTER]',
        '⌨️': '[KEYBOARD]',
        '🖱️': '[MOUSE]',
        '🖲️': '[TRACKBALL]',
        '💾': '[FLOPPY]',
        '💿': '[CD]',
        '📀': '[DVD]',
        '🧮': '[ABACUS]',
        '🎮': '[GAME]',
        '🕹️': '[JOYSTICK]',
        '🎲': '[DICE]',
        '🎯': '[DART]',
        '🎪': '[CIRCUS]',
        '🎭': '[THEATER]',
        '🎨': '[ART]',
        '🎬': '[MOVIE]',
        '🎤': '[MIC]',
        '🎧': '[HEADPHONES]',
        '🎵': '[MUSIC]',
        '🎶': '[NOTES]',
        '🎼': '[SCORE]',
        '🎹': '[PIANO]',
        '🥁': '[DRUM]',
        '🎷': '[SAX]',
        '🎺': '[TRUMPET]',
        '🎸': '[GUITAR]',
        '🎻': '[VIOLIN]'
    }
    
    try:
        # Leggi il file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = 0
        
        # Sostituisci ogni emoji
        for emoji, replacement in emoji_replacements.items():
            if emoji in content:
                count = content.count(emoji)
                content = content.replace(emoji, replacement)
                changes_made += count
                print(f"  Sostituito {count}x '{emoji}' -> '{replacement}'")
        
        # Se sono state fatte modifiche, salva il file
        if changes_made > 0:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ File aggiornato: {changes_made} sostituzioni totali")
            return True
        else:
            print("  Nessun emoji trovato")
            return False
            
    except Exception as e:
        print(f"✗ Errore durante la correzione di {file_path}: {e}")
        return False

def main():
    """Funzione principale"""
    print("🔧 Script di correzione emoji per problemi di codifica")
    print("=" * 60)
    
    # File da correggere
    files_to_fix = [
        'main.py',
        'admin_routes.py',
        'session_manager.py',
        'models.py',
        'database.py'
    ]
    
    total_files_fixed = 0
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            print(f"\n📄 Correzione file: {file_path}")
            if fix_emoji_in_file(file_path):
                total_files_fixed += 1
        else:
            print(f"\n⚠️ File non trovato: {file_path}")
    
    print("\n" + "=" * 60)
    print(f"✅ Correzione completata!")
    print(f"📊 File corretti: {total_files_fixed}/{len(files_to_fix)}")
    print("\n📝 Note:")
    print("- Gli emoji sono stati sostituiti con equivalenti ASCII")
    print("- Questo risolve i problemi di codifica 'charmap' su Windows")
    print("- I log e i print ora dovrebbero funzionare correttamente")
    print("- La funzionalità del codice rimane invariata")

if __name__ == "__main__":
    main()
