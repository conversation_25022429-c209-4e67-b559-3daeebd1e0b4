#!/usr/bin/env python3
"""
Script per inizializzare le tabelle VIAGGIO e ORARI nel database
"""

from database import SessionLocal, engine
from models import Base, Viaggio, Orari
from sqlalchemy import text

def init_viaggi_tables():
    """Inizializza le tabelle per la gestione dei viaggi"""

    # Crea una sessione
    db = SessionLocal()

    try:
        print("Inizializzazione tabelle VIAGGIO e ORARI...")

        # Crea tutte le tabelle definite nei modelli
        Base.metadata.create_all(bind=engine)

        print("✅ Tabelle VIAGGIO e ORARI create con successo!")

        # Verifica che le tabelle siano state create (PostgreSQL)
        result = db.execute(text("""
            SELECT table_name FROM information_schema.tables
            WHERE table_schema = 'public' AND table_name IN ('VIAGGIO', 'ORARI')
            ORDER BY table_name
        """))

        tabelle_create = [row[0] for row in result.fetchall()]
        print(f"📋 Tabelle create: {', '.join(tabelle_create)}")

        if 'VIAGGIO' in tabelle_create and 'ORARI' in tabelle_create:
            print("✅ Tutte le tabelle necessarie sono state create correttamente!")
        else:
            print("⚠️ Alcune tabelle potrebbero non essere state create correttamente")

    except Exception as e:
        print(f"❌ Errore durante l'inizializzazione delle tabelle: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    init_viaggi_tables()
