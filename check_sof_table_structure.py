#!/usr/bin/env python3

import psycopg2

def check_sof_table_structure():
    print("🔍 CONTROLLO STRUTTURA TABELLA SOF_DOCUMENTS")
    print("=" * 60)
    
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='AGENTE',
            user='re77',
            password='271077',
            connect_timeout=3
        )
        cursor = conn.cursor()
        
        # 1. Struttura tabella
        print("\n1️⃣ STRUTTURA TABELLA:")
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'SOF_DOCUMENTS' AND table_schema = 'public'
            ORDER BY ordinal_position;
        """)
        columns = cursor.fetchall()
        
        for col in columns:
            name, data_type, nullable, default = col
            nullable_str = 'NULL' if nullable == 'YES' else 'NOT NULL'
            default_str = f' DEFAULT {default}' if default else ''
            print(f"  {name}: {data_type} {nullable_str}{default_str}")
        
        # 2. Vincoli e indici
        print("\n2️⃣ VINCOLI E INDICI:")
        cursor.execute("""
            SELECT 
                tc.constraint_name,
                tc.constraint_type,
                kcu.column_name
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu 
                ON tc.constraint_name = kcu.constraint_name
            WHERE tc.table_name = 'SOF_DOCUMENTS' 
                AND tc.table_schema = 'public'
            ORDER BY tc.constraint_type, tc.constraint_name;
        """)
        constraints = cursor.fetchall()
        
        for constraint in constraints:
            name, type_, column = constraint
            print(f"  {type_}: {name} su colonna {column}")
        
        # 3. Dati esistenti
        print("\n3️⃣ DATI ESISTENTI:")
        cursor.execute("""
            SELECT viaggio_id, filename, file_size, 
                   CASE WHEN created_at IS NOT NULL THEN 'SI' ELSE 'NO' END as has_created_at
            FROM "SOF_DOCUMENTS"
            ORDER BY viaggio_id;
        """)
        records = cursor.fetchall()
        
        print(f"  Totale record: {len(records)}")
        for record in records:
            viaggio_id, filename, file_size, has_created_at = record
            print(f"  - Viaggio {viaggio_id}: {filename} ({file_size} bytes, created_at: {has_created_at})")
        
        # 4. Controlla specificamente viaggio 35
        print("\n4️⃣ DETTAGLI VIAGGIO 35:")
        cursor.execute("""
            SELECT * FROM "SOF_DOCUMENTS" WHERE viaggio_id = 35
        """)
        viaggio_35 = cursor.fetchall()
        
        if viaggio_35:
            print(f"  ✅ Trovato record per viaggio 35:")
            for record in viaggio_35:
                print(f"    {record}")
        else:
            print("  ❌ Nessun record trovato per viaggio 35")
        
        # 5. Test DELETE
        print("\n5️⃣ TEST DELETE:")
        cursor.execute("BEGIN")
        try:
            cursor.execute("DELETE FROM \"SOF_DOCUMENTS\" WHERE viaggio_id = 35")
            deleted_count = cursor.rowcount
            print(f"  ✅ DELETE riuscito: {deleted_count} record eliminati")
            cursor.execute("ROLLBACK")  # Non committiamo
        except Exception as e:
            print(f"  ❌ DELETE fallito: {e}")
            cursor.execute("ROLLBACK")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Errore: {e}")
        return False

if __name__ == "__main__":
    check_sof_table_structure()
