# 📦 GUIDA BACKUP AUTOMATICO SNIP

## 🎯 Sistema Implementato

Il sistema di backup automatico è **completamente implementato** e funzionante. Invia automaticamente il file .sql del database AGENTE all'Email Admin all'orario configurato.

## ✅ Funzionalità Implementate

### 🔧 Componenti Attivi
- ✅ **BackupManager** con scheduler automatico
- ✅ **Cron Job** che controlla ogni minuto
- ✅ **Invio Email** con file .sql allegato
- ✅ **Configurazioni** salvate nel database
- ✅ **Compressione** opzionale dei backup
- ✅ **Retention** automatica file vecchi

### 📧 Sistema Email
- ✅ **Email Admin** = campo "Email Admin" nelle configurazioni
- ✅ **Allegato .sql** del database AGENTE completo
- ✅ **Supporto SMTP** con autenticazione
- ✅ **Notifiche** di successo/errore

## 🛠️ Come Configurare

### 1. Configurazione Email Admin
```
1. Vai su /dashboard/amministrazione
2. Clicca su "Configurazione Email"
3. Compila il campo "Email Admin"
4. Esempio: <EMAIL>
5. Configura anche SMTP per l'invio
6. Clicca "Salva Tutto"
```

### 2. Configurazione Orario Backup
```
1. Vai su /dashboard/amministrazione
2. Clicca su "Configurazione Database"
3. Imposta "Orario Backup" a 12:45
4. Imposta "Frequenza Backup" a daily
5. Configura retention giorni (es. 30)
6. Clicca "Salva Tutto"
```

### 3. Configurazione SMTP
```
Per Gmail:
- Host SMTP: smtp.gmail.com
- Porta: 587
- Username: <EMAIL>
- Password: Password per le app (NON password normale)
- SSL/TLS: Abilitato

Per Outlook:
- Host SMTP: smtp-mail.outlook.com
- Porta: 587
- Username: <EMAIL>
- Password: Password normale
- SSL/TLS: Abilitato
```

## ⏰ Funzionamento Automatico

### Scheduler Attivo
```
- Il sistema controlla ogni minuto se è ora di backup
- Alle 12:45 (o orario configurato) esegue automaticamente:
  1. Dump completo database AGENTE
  2. Compressione file (se abilitata)
  3. Invio email con allegato
  4. Log dell'operazione
  5. Pulizia file vecchi
```

### Email Ricevuta
```
Oggetto: 📦 Backup Database SNIP - 19/06/2025 12:45

Contenuto:
- Dettagli backup (data, ora, dimensione)
- Configurazioni utilizzate
- File .sql allegato
- Istruzioni per il ripristino

Allegato:
- snip_backup_20250619_1245.sql (10-20 MB)
- Contiene dump completo database AGENTE
- Utilizzabile per ripristino completo
```

## 🔍 Verifica Funzionamento

### 1. Controllo Configurazioni
```
1. Email Admin configurata ✓
2. Orario Backup impostato ✓
3. SMTP funzionante ✓
4. Servizio backup attivo ✓
```

### 2. Test Email
```
1. Vai su Configurazione Email
2. Clicca "Test Email"
3. Verifica ricezione email
4. Se funziona, il backup funzionerà
```

### 3. Log Sistema
```
Controlla log server per:
- [SCHEDULE] Backup programmati: giornalieri alle 12:45
- [EMAIL] Backup inviato con allegato a: <EMAIL>
- [EMAIL] File allegato: backup.sql (15.7 MB)
```

## 🚨 Risoluzione Problemi

### Email Non Arriva
```
Cause possibili:
❌ Email Admin non configurata
❌ SMTP non funzionante
❌ Password Gmail errata (serve password per le app)
❌ Firewall blocca SMTP

Soluzioni:
✅ Verificare campo Email Admin
✅ Testare configurazioni SMTP
✅ Usare password per le app Gmail
✅ Controllare firewall/antivirus
```

### Backup Non Eseguito
```
Cause possibili:
❌ Servizio backup non attivo
❌ Orario non configurato
❌ Errori database

Soluzioni:
✅ Riavviare server SNIP
✅ Verificare orario backup
✅ Controllare log errori
```

### File Allegato Corrotto
```
Cause possibili:
❌ Errore durante dump database
❌ Problemi compressione
❌ Limite dimensione email

Soluzioni:
✅ Disabilitare compressione
✅ Verificare spazio disco
✅ Controllare limiti provider email
```

## 📋 Configurazioni Consigliate

### Ambiente Produzione
```
Email Admin: <EMAIL>
Orario Backup: 02:00 (notte)
Frequenza: daily
Retention: 30 giorni
Compressione: Abilitata
Provider Email: Gmail Business
```

### Ambiente Test
```
Email Admin: <EMAIL>
Orario Backup: 12:45 (test immediato)
Frequenza: daily
Retention: 7 giorni
Compressione: Disabilitata
Provider Email: Gmail personale
```

## 🎯 Risultato Finale

Una volta configurato correttamente:

✅ **Email automatica** ogni giorno alle 12:45  
✅ **File .sql completo** del database AGENTE allegato  
✅ **Ripristino database** possibile in caso di emergenza  
✅ **Backup offline** sicuro via email  
✅ **Notifiche** di successo/errore  
✅ **Gestione automatica** senza intervento manuale  

## 🚀 Avvio Immediato

Per iniziare subito:

1. **Configura Email Admin** nel dashboard
2. **Imposta orario 12:45** per backup
3. **Testa invio email** per verificare SMTP
4. **Attendi le 12:45** per il primo backup automatico

**Il sistema è pronto e funzionante!** 🎉

---

*Sistema di Backup Automatico SNIP*  
*Michele Autuori Srl - shipping and forwarding agency*
