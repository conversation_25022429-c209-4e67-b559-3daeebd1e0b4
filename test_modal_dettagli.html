<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Modal Dettagli SOF Archiviato</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>🧪 Test Modal Dettagli SOF Archiviato</h1>
        
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Scopo del Test:</strong> Verificare che il modal "Dettagli SOF Archiviato" mostri tutti i dati correttamente.
        </div>

        <!-- P<PERSON><PERSON><PERSON> Test -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-database me-2"></i>Test Endpoint API</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Filename Test:</label>
                            <input type="text" class="form-control" id="testFilename" 
                                   value="CATANIA_06062025_ID34" placeholder="Es: CATANIA_06062025_ID34">
                        </div>
                        <button class="btn btn-primary" onclick="testAPIEndpoint()">
                            <i class="fas fa-play me-1"></i>Test API Endpoint
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-eye me-2"></i>Test Modal Completo</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-success" onclick="testModalCompleto()">
                            <i class="fas fa-modal me-1"></i>Test Modal Completo
                        </button>
                        <button class="btn btn-warning mt-2" onclick="testConDatiMock()">
                            <i class="fas fa-code me-1"></i>Test con Dati Mock
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Risultati Test -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clipboard-list me-2"></i>Risultati Test</h5>
            </div>
            <div class="card-body">
                <div id="testResults">
                    <p class="text-muted">I risultati dei test appariranno qui...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Test -->
    <div class="modal fade" id="modalDettagliSOF" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalDettagliTitle">
                        <i class="fas fa-info-circle me-2"></i>
                        Test Modal Dettagli SOF
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="modalDettagliContent">
                        <!-- Contenuto dinamico -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Chiudi</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Funzioni di test
        async function testAPIEndpoint() {
            const filename = document.getElementById('testFilename').value;
            const resultsDiv = document.getElementById('testResults');
            
            resultsDiv.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-spinner fa-spin me-2"></i>
                    Testing API endpoint: /api/sof/archiviati/${filename}/dettagli
                </div>
            `;

            try {
                const response = await fetch(`/api/sof/archiviati/${encodeURIComponent(filename)}/dettagli`);
                const data = await response.json();
                
                let html = `<h6>📡 Risposta API (Status: ${response.status})</h6>`;
                
                if (response.ok && data.success) {
                    html += `
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>✅ API Funziona!</strong> Dati ricevuti correttamente.
                        </div>
                        <h6>📊 Struttura Dati Ricevuti:</h6>
                        <pre class="bg-light p-3 rounded"><code>${JSON.stringify(data, null, 2)}</code></pre>
                        
                        <h6>🔍 Analisi Dati:</h6>
                        <ul class="list-group">
                            <li class="list-group-item">
                                <strong>Viaggio:</strong> ${JSON.stringify(data.data.viaggio)}
                            </li>
                            <li class="list-group-item">
                                <strong>Orari:</strong> ${data.data.orari ? data.data.orari.length : 0} record
                            </li>
                            <li class="list-group-item">
                                <strong>Import:</strong> ${data.data.import_data ? data.data.import_data.length : 0} record
                            </li>
                            <li class="list-group-item">
                                <strong>Export:</strong> ${data.data.export_data ? data.data.export_data.length : 0} record
                            </li>
                            <li class="list-group-item">
                                <strong>Atlas Mapping:</strong> ${data.data.atlas_mapping ? Object.keys(data.data.atlas_mapping).length : 0} porti
                            </li>
                        </ul>
                    `;
                } else {
                    html += `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>❌ Errore API:</strong> ${data.message || 'Errore sconosciuto'}
                        </div>
                    `;
                }
                
                resultsDiv.innerHTML = html;
                
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>❌ Errore di Rete:</strong> ${error.message}
                    </div>
                `;
            }
        }

        async function testModalCompleto() {
            const filename = document.getElementById('testFilename').value;
            
            try {
                // Simula la chiamata della funzione visualizzaDettagli
                const response = await fetch(`/api/sof/archiviati/${encodeURIComponent(filename)}/dettagli`);
                const data = await response.json();
                
                if (data.success) {
                    // Apri modal
                    const modal = new bootstrap.Modal(document.getElementById('modalDettagliSOF'));
                    modal.show();
                    
                    // Genera HTML usando la funzione del sistema
                    const modalContent = document.getElementById('modalDettagliContent');
                    modalContent.innerHTML = generaHTMLDettagliTest(data.data);
                    
                    // Aggiorna risultati
                    document.getElementById('testResults').innerHTML = `
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>✅ Modal Aperto!</strong> Controlla il modal per vedere i dati visualizzati.
                        </div>
                    `;
                } else {
                    throw new Error(data.message || 'Errore API');
                }
                
            } catch (error) {
                document.getElementById('testResults').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>❌ Errore:</strong> ${error.message}
                    </div>
                `;
            }
        }

        function testConDatiMock() {
            // Dati mock per test
            const mockData = {
                metadata: {
                    archiviato_il: "2025-06-18T10:30:00",
                    archiviato_da: "Sistema Test",
                    filename: "TEST_MOCK_ID123",
                    viaggio_id: 123
                },
                viaggio: {
                    id: 123,
                    codice_viaggio: "CTA001",
                    data_arrivo: "2025-06-15T08:00:00",
                    data_partenza: "2025-06-16T18:00:00",
                    nome_nave: "CATANIA EXPRESS",
                    prefisso_viaggio: "CTA",
                    agemar: "AG001",
                    porto_gestione: "Catania"
                },
                orari: [{
                    porto_arrivo: "CTA",
                    sbe: "2025-06-15T08:00:00",
                    pilota_arrivo: "2025-06-15T08:15:00",
                    all_fast: "2025-06-15T08:30:00",
                    tug_arrivo: 2,
                    draft: 8.5,
                    soc: "2025-06-16T17:00:00",
                    porto_di_destinazione: "GEN",
                    pilota_partenza: "2025-06-16T17:30:00",
                    tug_partenza: 1,
                    foc: "2025-06-16T18:00:00",
                    fo: 150.5,
                    do: 25.0,
                    lo: 5.2
                }],
                import_data: [
                    { id: 1, pol: "CTA", pod: "GEN", qt: 100, type: "Container", created_at: "2025-06-15T10:00:00" },
                    { id: 2, pol: "CTA", pod: "GEN", qt: 50, type: "Trailer", created_at: "2025-06-15T11:00:00" }
                ],
                export_data: [
                    { id: 1, pol: "GEN", pod: "CTA", qt: 75, type: "Container", created_at: "2025-06-16T14:00:00" }
                ],
                atlas_mapping: {
                    "CTA": "Catania",
                    "GEN": "Genova",
                    "PAL": "Palermo"
                }
            };
            
            // Apri modal con dati mock
            const modal = new bootstrap.Modal(document.getElementById('modalDettagliSOF'));
            modal.show();
            
            const modalContent = document.getElementById('modalDettagliContent');
            modalContent.innerHTML = generaHTMLDettagliTest(mockData);
            
            document.getElementById('testResults').innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-code me-2"></i>
                    <strong>🧪 Test con Dati Mock</strong> - Modal aperto con dati di esempio per verificare la visualizzazione.
                </div>
            `;
        }
    </script>
    
    <!-- Include le funzioni JavaScript del sistema -->
    <script src="/static/js/sof-archiviati.js"></script>
    
    <script>
        // Funzione di test che usa la logica del sistema
        function generaHTMLDettagliTest(data) {
            // Usa la stessa logica del sistema ma con debug aggiuntivo
            console.log('🧪 TEST - Dati ricevuti:', data);
            
            const viaggio = data.viaggio || {};
            const metadata = data.metadata || {};
            
            // Debug dei campi viaggio
            console.log('🚢 Viaggio completo:', viaggio);
            console.log('📋 Metadata completo:', metadata);
            
            return generaHTMLDettagli(data);
        }
    </script>
</body>
</html>
