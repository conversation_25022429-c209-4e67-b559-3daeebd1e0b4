#!/usr/bin/env python3
"""
Script di debug per identificare l'errore nel download SOF DOCX
"""

import sys
import os
import traceback
from datetime import datetime

# Aggiungi il percorso corrente al path
sys.path.append('.')

def test_database_connection():
    """Test connessione database"""
    try:
        from database import get_db
        db = next(get_db())
        print("✅ Connessione database OK")
        
        # Test query semplice
        from sqlalchemy import text
        result = db.execute(text("SELECT 1")).fetchone()
        print(f"✅ Query test OK: {result}")
        
        db.close()
        return True
    except Exception as e:
        print(f"❌ Errore connessione database: {e}")
        traceback.print_exc()
        return False

def test_sof_documents_table():
    """Test struttura tabella SOF_DOCUMENTS"""
    try:
        from database import get_db
        from sqlalchemy import text
        
        db = next(get_db())
        
        # Verifica struttura tabella
        try:
            result = db.execute(text("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'SOF_DOCUMENTS'
            """)).fetchall()
            
            print("📋 Struttura tabella SOF_DOCUMENTS:")
            for row in result:
                print(f"   - {row[0]}: {row[1]}")
                
        except Exception as e:
            print(f"⚠️ Impossibile ottenere struttura tabella: {e}")
            
            # Prova query diretta
            try:
                result = db.execute(text("SELECT * FROM \"SOF_DOCUMENTS\" LIMIT 1")).fetchone()
                print("✅ Tabella SOF_DOCUMENTS accessibile")
                if result:
                    print(f"   Numero colonne: {len(result)}")
                else:
                    print("   Tabella vuota")
            except Exception as e2:
                print(f"❌ Errore accesso tabella SOF_DOCUMENTS: {e2}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Errore test tabella SOF_DOCUMENTS: {e}")
        traceback.print_exc()
        return False

def test_timestamp_columns():
    """Test aggiunta colonne timestamp"""
    try:
        from database import get_db
        from main import ensure_timestamp_columns
        
        db = next(get_db())
        
        print("🔧 Test aggiunta colonne timestamp...")
        ensure_timestamp_columns(db)
        print("✅ Funzione ensure_timestamp_columns completata")
        
        # Verifica colonne aggiunte
        from sqlalchemy import text
        tables = ["ORARI", "IMPORT", "EXPORT"]
        for table in tables:
            try:
                result = db.execute(text(f'SELECT updated_at FROM "{table}" LIMIT 1'))
                print(f"✅ Colonna updated_at verificata per {table}")
            except Exception as e:
                print(f"❌ Colonna updated_at non trovata per {table}: {e}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Errore test colonne timestamp: {e}")
        traceback.print_exc()
        return False

def test_sof_generation():
    """Test generazione SOF base"""
    try:
        from main import generate_sof_docx
        
        print("📄 Test generazione SOF DOCX...")
        
        # Dati di test
        viaggio_id = 999
        viaggio_nome = "TEST_VIAGGIO"
        nave_nome = "TEST_NAVE"
        orari_data = ["Porto A", "2024-01-15T08:00", "2024-01-15T09:00", "2024-01-15T10:00", 
                     "2024-01-15T11:00", "8.5", "2024-01-15T12:00", "Porto B", 
                     "2024-01-15T13:00", "2024-01-15T14:00", "2024-01-15T15:00", 
                     "100", "50", "25"]
        import_data = [["PORTO1", "PORTO2", 10, "NCAR"]]
        export_data = [["PORTO2", "PORTO3", 5, "UVAN"]]
        
        docx_path = generate_sof_docx(viaggio_id, viaggio_nome, nave_nome, 
                                     orari_data, import_data, export_data)
        
        if os.path.exists(docx_path):
            file_size = os.path.getsize(docx_path)
            print(f"✅ SOF generato con successo: {docx_path}")
            print(f"   Dimensione: {file_size} bytes")
            
            # Cleanup
            os.remove(docx_path)
            print("✅ File di test rimosso")
            return True
        else:
            print("❌ File SOF non generato")
            return False
            
    except Exception as e:
        print(f"❌ Errore generazione SOF: {e}")
        traceback.print_exc()
        return False

def test_viaggio_query():
    """Test query viaggio"""
    try:
        from database import get_db
        from sqlalchemy import text
        
        db = next(get_db())
        
        # Trova un viaggio esistente
        result = db.execute(text("""
            SELECT id, viaggio, visibile FROM "VIAGGIO" LIMIT 5
        """)).fetchall()
        
        print("📋 Viaggi disponibili per test:")
        for row in result:
            print(f"   ID: {row[0]}, Nome: {row[1]}, Visibile: {row[2]}")
        
        if result:
            viaggio_id = result[0][0]
            print(f"\n🧪 Test con viaggio ID: {viaggio_id}")
            
            # Test query orari (con colonne quotate per parole riservate)
            orari = db.execute(text("""
                SELECT porto_arrivo, sbe, pilota_arrivo, all_fast, tug_arrivo, draft, soc,
                       porto_di_destinazione, pilota_partenza, tug_partenza, foc, fo, "do", lo
                FROM "ORARI" WHERE viaggio_id = :viaggio_id
            """), {"viaggio_id": viaggio_id}).fetchone()
            
            print(f"   Orari trovati: {'✅' if orari else '❌'}")
            
            # Test query import
            import_data = db.execute(text("""
                SELECT pol, pod, qt, type FROM "IMPORT" WHERE viaggio_id = :viaggio_id
            """), {"viaggio_id": viaggio_id}).fetchall()
            
            print(f"   Import records: {len(import_data)}")
            
            # Test query export
            export_data = db.execute(text("""
                SELECT pol, pod, qt, type FROM "EXPORT" WHERE viaggio_id = :viaggio_id
            """), {"viaggio_id": viaggio_id}).fetchall()
            
            print(f"   Export records: {len(export_data)}")
            
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Errore test query viaggio: {e}")
        traceback.print_exc()
        return False

def main():
    """Esegue tutti i test di debug"""
    print("🔍 INIZIO DEBUG SOF DOWNLOAD")
    print("=" * 50)
    print(f"🕐 Timestamp: {datetime.now()}")
    print()
    
    tests = [
        ("Connessione Database", test_database_connection),
        ("Tabella SOF_DOCUMENTS", test_sof_documents_table),
        ("Colonne Timestamp", test_timestamp_columns),
        ("Query Viaggi", test_viaggio_query),
        ("Generazione SOF", test_sof_generation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"🧪 {test_name}")
        print("-" * 30)
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"{'✅ PASS' if result else '❌ FAIL'}")
        except Exception as e:
            print(f"❌ ERRORE: {e}")
            results.append((test_name, False))
        print()
    
    print("📊 RIEPILOGO RISULTATI")
    print("=" * 50)
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\nTotale: {passed}/{total} test passati")
    
    if passed == total:
        print("🎉 Tutti i test sono passati! Il problema potrebbe essere altrove.")
    else:
        print("⚠️ Alcuni test sono falliti. Controlla gli errori sopra.")

if __name__ == "__main__":
    main()
