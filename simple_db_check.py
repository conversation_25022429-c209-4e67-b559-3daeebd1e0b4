#!/usr/bin/env python3

# Script completo per controllare la struttura del database
# e identificare i campi mancanti

def check_database():
    try:
        import psycopg2
        print("✓ psycopg2 importato correttamente")

        # Connessione al database
        conn = psycopg2.connect(
            host='localhost',
            database='AGENTE',
            user='re77',
            password='271077'
        )
        print("✓ Connessione al database riuscita")

        cursor = conn.cursor()

        # Lista tabelle
        cursor.execute("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
            ORDER BY table_name;
        """)
        tables = cursor.fetchall()

        print(f"\n=== TABELLE TROVATE ({len(tables)}) ===")
        table_names = []
        for table in tables:
            table_name = table[0].upper()
            table_names.append(table_name)
            print(f"- {table_name}")

        # Tabelle previste dal modello
        expected_tables = [
            'AGENTE', 'PORTI_GESTIONE', 'CODICI_ATLAS', 'NAVI', 'ARMATORE',
            'VIAGGIO', 'ORARI', 'IMPORT', 'EXPORT', 'SYSTEM_CONFIG',
            'AUDIT_LOG', 'USER_SESSIONS', 'DEPARTMENT_NOTIFICATIONS',
            'USER_NOTIFICATION_READ', 'SYSTEM_STATS'
        ]

        print(f"\n=== TABELLE MANCANTI ===")
        missing_tables = [t for t in expected_tables if t not in table_names]
        if missing_tables:
            for table in missing_tables:
                print(f"❌ {table}")
        else:
            print("✓ Tutte le tabelle principali sono presenti")

        # Controlla struttura delle tabelle principali
        tables_to_check = ['AGENTE', 'NAVI', 'VIAGGIO', 'ORARI', 'IMPORT', 'EXPORT']

        for table_name in tables_to_check:
            if table_name in table_names:
                print(f"\n=== STRUTTURA TABELLA {table_name} ===")
                cursor.execute("""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns
                    WHERE table_name = %s AND table_schema = 'public'
                    ORDER BY ordinal_position;
                """, (table_name,))
                columns = cursor.fetchall()

                for col in columns:
                    name, data_type, nullable, default = col
                    nullable_str = 'NULL' if nullable == 'YES' else 'NOT NULL'
                    default_str = f' DEFAULT {default}' if default else ''
                    print(f"  {name}: {data_type} {nullable_str}{default_str}")

                # Conta righe
                cursor.execute(f'SELECT COUNT(*) FROM "{table_name}"')
                count = cursor.fetchone()[0]
                print(f"  Righe presenti: {count}")

        conn.close()
        print("\n✓ Controllo completato con successo")

    except ImportError as e:
        print(f"❌ Errore import: {e}")
        print("Installa psycopg2 con: pip install psycopg2-binary")
    except Exception as e:
        print(f"❌ Errore: {e}")

if __name__ == "__main__":
    check_database()
