<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Archiviazione Completa</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Archiviazione Completa</h1>
        
        <div class="test-section info">
            <h3>📋 Test delle Funzioni JavaScript</h3>
            <p>Questo test verifica che tutte le funzioni di archiviazione siano caricate correttamente.</p>
            <button onclick="testFunzioniJavaScript()">🔧 Test Funzioni JavaScript</button>
        </div>

        <div class="test-section info">
            <h3>🌐 Test API Endpoint</h3>
            <p>Testa la connessione agli endpoint di archiviazione.</p>
            <button onclick="testAPIEndpoint()">📡 Test API Endpoint</button>
        </div>

        <div class="test-section info">
            <h3>🗂️ Test Archiviazione Simulata</h3>
            <p>Simula il processo di archiviazione per periodo.</p>
            <button onclick="testArchiviazioneSimulata()">🚀 Test Archiviazione</button>
        </div>

        <div class="test-section">
            <h3>📊 Log Test</h3>
            <div id="logArea" class="log">Pronto per i test...\n</div>
            <button onclick="clearLog()">🧹 Pulisci Log</button>
        </div>
    </div>

    <script>
        // Carica il file JavaScript di gestione archivio
        const script = document.createElement('script');
        script.src = 'http://127.0.0.1:8003/static/js/gestione-archivio.js';
        script.onload = function() {
            addLog('✅ File gestione-archivio.js caricato con successo');
        };
        script.onerror = function() {
            addLog('❌ Errore caricamento gestione-archivio.js');
        };
        document.head.appendChild(script);

        function addLog(message) {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.textContent += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logArea').textContent = 'Log pulito...\n';
        }

        function testFunzioniJavaScript() {
            addLog('\n🔧 === TEST FUNZIONI JAVASCRIPT ===');
            
            const funzioniRichieste = [
                'aggiornaCampiPeriodo',
                'validaCampiPeriodo', 
                'selezionaTutti',
                'deselezionaTutti',
                'testConnessioneAPI',
                'archiviaPerPeriodo',
                'archiviaSelezionati'
            ];
            
            let funzioniOK = 0;
            let funzioniMancanti = [];
            
            funzioniRichieste.forEach(nome => {
                if (typeof window[nome] === 'function') {
                    addLog(`✅ ${nome}: function`);
                    funzioniOK++;
                } else {
                    addLog(`❌ ${nome}: ${typeof window[nome]}`);
                    funzioniMancanti.push(nome);
                }
            });
            
            addLog(`\n📊 Risultato: ${funzioniOK}/${funzioniRichieste.length} funzioni caricate`);
            
            if (funzioniMancanti.length === 0) {
                addLog('🎉 Tutte le funzioni JavaScript sono disponibili!');
            } else {
                addLog(`⚠️ Funzioni mancanti: ${funzioniMancanti.join(', ')}`);
            }
        }

        async function testAPIEndpoint() {
            addLog('\n📡 === TEST API ENDPOINT ===');
            
            try {
                addLog('🧪 Test endpoint di test...');
                
                const response = await fetch('http://127.0.0.1:8003/api/operativo/sof/archivia-json/test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        periodo_tipo: 'anno',
                        periodo_valore: '2024'
                    })
                });
                
                addLog(`📊 Status: ${response.status}`);
                const result = await response.json();
                addLog(`📋 Response: ${JSON.stringify(result, null, 2)}`);
                
                if (response.status === 200 && result.success) {
                    addLog('✅ Endpoint di test funziona correttamente!');
                } else {
                    addLog(`⚠️ Endpoint risponde ma con errori: ${result.message}`);
                }
                
            } catch (error) {
                addLog(`❌ Errore test API: ${error.message}`);
            }
        }

        async function testArchiviazioneSimulata() {
            addLog('\n🚀 === TEST ARCHIVIAZIONE SIMULATA ===');
            
            // Crea elementi DOM simulati per il test
            createMockElements();
            
            try {
                // Test aggiornaCampiPeriodo
                if (typeof window.aggiornaCampiPeriodo === 'function') {
                    addLog('🔧 Test aggiornaCampiPeriodo...');
                    
                    // Simula selezione anno
                    document.getElementById('tipoPeriodo').value = 'anno';
                    window.aggiornaCampiPeriodo();
                    
                    // Verifica che i campi siano stati creati
                    const annoSelect = document.getElementById('annoSelezionato');
                    if (annoSelect) {
                        addLog('✅ Campi anno generati correttamente');
                        
                        // Test validazione
                        annoSelect.value = '2024';
                        if (typeof window.validaCampiPeriodo === 'function') {
                            window.validaCampiPeriodo();
                            addLog('✅ Validazione campi funziona');
                        }
                    } else {
                        addLog('❌ Campi anno non generati');
                    }
                } else {
                    addLog('❌ Funzione aggiornaCampiPeriodo non disponibile');
                }
                
                // Test selezione viaggi
                if (typeof window.selezionaTutti === 'function') {
                    addLog('🔧 Test selezione viaggi...');
                    window.selezionaTutti();
                    addLog('✅ Funzione selezionaTutti eseguita');
                }
                
                addLog('🎉 Test archiviazione simulata completato!');
                
            } catch (error) {
                addLog(`❌ Errore durante test simulato: ${error.message}`);
            }
        }

        function createMockElements() {
            // Crea elementi DOM necessari per il test
            if (!document.getElementById('tipoPeriodo')) {
                const select = document.createElement('select');
                select.id = 'tipoPeriodo';
                select.style.display = 'none';
                document.body.appendChild(select);
            }
            
            if (!document.getElementById('campiPeriodo')) {
                const div = document.createElement('div');
                div.id = 'campiPeriodo';
                div.style.display = 'none';
                document.body.appendChild(div);
            }
            
            if (!document.getElementById('btnArchiviaPeriodo')) {
                const btn = document.createElement('button');
                btn.id = 'btnArchiviaPeriodo';
                btn.style.display = 'none';
                document.body.appendChild(btn);
            }
            
            addLog('🔧 Elementi DOM mock creati per il test');
        }

        // Auto-test all'avvio
        setTimeout(() => {
            addLog('\n🚀 Avvio test automatici...');
            testFunzioniJavaScript();
        }, 1000);
    </script>
</body>
</html>
