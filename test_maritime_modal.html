<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Tema <PERSON> - Modal Archiviazione di Massa SOF</title>
    
    <!-- Bootstrap 5.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* ===== TEMA MARITTIMO BASE ===== */
        body.theme-maritime {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
            color: #212529;
            font-family: 'Inter', sans-serif;
            min-height: 100vh;
        }

        /* ===== NAVBAR MARITTIMO ===== */
        .snip-navbar {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
            backdrop-filter: blur(10px);
            border-bottom: 3px solid #ffd700;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .navbar-brand {
            color: #ffd700 !important;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .navbar-nav .nav-link {
            color: #ffffff !important;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: #ffd700 !important;
            text-shadow: 0 0 10px rgba(255,215,0,0.6);
        }

        /* ===== CARD MARITTIMO ===== */
        .card {
            background: rgba(255, 255, 255, 0.95) !important;
            color: #212529 !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }

        .card-header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
            color: #ffffff !important;
            border-bottom: 2px solid #ffd700 !important;
            border-radius: 15px 15px 0 0 !important;
        }

        .card-title {
            color: #ffffff !important;
            font-weight: 700;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
        }

        .card-title i {
            color: #ffd700 !important;
            text-shadow: 0 0 10px rgba(255,215,0,0.6);
        }

        /* ===== BADGE MARITTIMI ===== */
        .badge {
            font-weight: 600 !important;
            letter-spacing: 0.5px !important;
            border-radius: 8px !important;
            padding: 0.5rem 0.75rem !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2) !important;
            transition: all 0.3s ease !important;
        }

        .badge.bg-primary {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
            color: #ffffff !important;
            border: 1px solid rgba(255,215,0,0.3) !important;
        }

        .badge.bg-success {
            background: linear-gradient(135deg, #059669 0%, #10b981 100%) !important;
            color: #ffffff !important;
            border: 1px solid rgba(255,215,0,0.2) !important;
        }

        .badge.bg-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%) !important;
            color: #1f2937 !important;
            border: 1px solid rgba(30,60,114,0.2) !important;
            font-weight: 700 !important;
        }

        .badge.bg-info {
            background: linear-gradient(135deg, #0ea5e9 0%, #38bdf8 100%) !important;
            color: #ffffff !important;
            border: 1px solid rgba(255,215,0,0.2) !important;
        }

        .badge.bg-danger {
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%) !important;
            color: #ffffff !important;
            border: 1px solid rgba(255,215,0,0.2) !important;
        }

        .badge:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
        }

        /* ===== BOTTONI MARITTIMI ===== */
        .btn-primary {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
            border: 2px solid #ffd700 !important;
            color: #ffffff !important;
            font-weight: 600 !important;
            border-radius: 10px !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
            box-shadow: 0 4px 12px rgba(30,60,114,0.3) !important;
            transition: all 0.3s ease !important;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2a5298 0%, #1e40af 100%) !important;
            border-color: #ffed4e !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(30,60,114,0.4) !important;
        }

        .btn-outline-primary {
            border: 2px solid #1e3c72 !important;
            color: #1e3c72 !important;
            font-weight: 600 !important;
            border-radius: 10px !important;
            transition: all 0.3s ease !important;
        }

        .btn-outline-primary:hover {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
            border-color: #ffd700 !important;
            color: #ffffff !important;
            transform: translateY(-2px) !important;
        }

        /* ===== ANIMAZIONI ===== */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        /* ===== MODAL MARITTIMO MIGLIORATO ===== */
        .modal-content {
            background: linear-gradient(135deg, rgba(30,60,114,0.95) 0%, rgba(42,82,152,0.95) 100%) !important;
            color: #ffffff !important;
            border: 2px solid rgba(255,215,0,0.4) !important;
            border-radius: 20px !important;
            box-shadow: 0 20px 60px rgba(0,0,0,0.4) !important;
            backdrop-filter: blur(15px) !important;
            overflow: hidden !important;
        }

        .modal-header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #1e40af 100%) !important;
            color: #ffffff !important;
            border-bottom: 3px solid #ffd700 !important;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3) !important;
            position: relative !important;
        }

        .modal-header::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%) !important;
            backdrop-filter: blur(10px) !important;
            pointer-events: none !important;
        }

        .modal-title {
            color: #ffffff !important;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8) !important;
            font-weight: 700 !important;
            position: relative !important;
            z-index: 2 !important;
        }

        .modal-title i {
            color: #ffd700 !important;
            text-shadow: 0 0 10px rgba(255,215,0,0.6) !important;
            margin-right: 8px !important;
        }

        .btn-close {
            filter: invert(1) brightness(1.2) !important;
            opacity: 0.9 !important;
            position: relative !important;
            z-index: 2 !important;
            transition: all 0.3s ease !important;
        }

        .btn-close:hover {
            opacity: 1 !important;
            transform: scale(1.1) !important;
            filter: invert(1) brightness(1.5) drop-shadow(0 0 5px rgba(255,215,0,0.8)) !important;
        }

        .modal-body {
            background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%) !important;
            color: #212529 !important;
            backdrop-filter: blur(10px) !important;
            position: relative !important;
        }

        .modal-body::before {
            content: '' !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            background: linear-gradient(135deg, rgba(30,60,114,0.05) 0%, rgba(42,82,152,0.05) 100%) !important;
            pointer-events: none !important;
        }

        .modal-body > * {
            position: relative !important;
            z-index: 1 !important;
        }

        .modal-footer {
            background: linear-gradient(135deg, rgba(30,60,114,0.8) 0%, rgba(42,82,152,0.8) 100%) !important;
            border-top: 2px solid rgba(255,215,0,0.4) !important;
            backdrop-filter: blur(10px) !important;
        }

        /* Form controls nel modal */
        .modal-body .form-select {
            background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%) !important;
            border: 2px solid rgba(30,60,114,0.3) !important;
            color: #212529 !important;
            border-radius: 10px !important;
            padding: 0.75rem 1rem !important;
            font-weight: 500 !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
        }

        .modal-body .form-select:focus {
            border-color: #ffd700 !important;
            box-shadow: 0 0 0 0.2rem rgba(255,215,0,0.25), 0 4px 12px rgba(30,60,114,0.2) !important;
            background: rgba(255,255,255,1) !important;
        }

        .modal-body .form-label {
            color: #1e3c72 !important;
            font-weight: 600 !important;
            margin-bottom: 0.5rem !important;
        }

        .modal-body .form-label i {
            color: #ffd700 !important;
            margin-right: 0.5rem !important;
            text-shadow: 0 0 5px rgba(255,215,0,0.3) !important;
        }

        /* Tabella nel modal */
        .modal-body .table {
            background: rgba(255,255,255,0.9) !important;
            border-radius: 10px !important;
            overflow: hidden !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
        }

        .modal-body .table thead th {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%) !important;
            color: #ffffff !important;
            border: none !important;
            font-weight: 600 !important;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
        }

        .modal-body .table tbody td {
            color: #212529 !important;
            border-color: rgba(30,60,114,0.1) !important;
            font-weight: 500 !important;
        }

        .modal-body .table tbody tr:hover {
            background-color: rgba(30,60,114,0.05) !important;
        }

        /* ===== RESPONSIVE ===== */
        @media (max-width: 768px) {
            .card {
                margin: 1rem;
            }

            .badge {
                font-size: 0.8rem;
                padding: 0.4rem 0.6rem;
            }

            .modal-dialog {
                margin: 0.5rem;
            }
        }
    </style>
</head>
<body class="theme-maritime">
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg snip-navbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-anchor me-2"></i>
                SNIP - Sistema Navale Integrato Portuale
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#"><i class="fas fa-ship me-1"></i>Operativo</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#"><i class="fas fa-file-alt me-1"></i>SOF</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#"><i class="fas fa-archive me-1"></i>Archivi</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Contenuto principale -->
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <!-- Header con titolo -->
                <div class="card fade-in-up mb-4">
                    <div class="card-header">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-anchor me-2"></i>
                            Test Tema Marittimo - Modal Archiviazione di Massa SOF
                        </h3>
                    </div>
                    <div class="card-body">
                        <p class="mb-3">
                            Questa pagina di test mostra il tema marittimo migliorato per il sistema SNIP, 
                            con particolare attenzione al modal di archiviazione di massa SOF.
                        </p>
                        
                        <!-- Badge di esempio -->
                        <div class="mb-4">
                            <h5 class="mb-3"><i class="fas fa-tags text-warning me-2"></i>Badge Migliorati</h5>
                            <div class="d-flex flex-wrap gap-2">
                                <span class="badge bg-primary">
                                    <i class="fas fa-ship me-1"></i>Primario
                                </span>
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>Successo
                                </span>
                                <span class="badge bg-warning">
                                    <i class="fas fa-exclamation-triangle me-1"></i>Attenzione
                                </span>
                                <span class="badge bg-info">
                                    <i class="fas fa-info-circle me-1"></i>Informazione
                                </span>
                                <span class="badge bg-danger">
                                    <i class="fas fa-times me-1"></i>Errore
                                </span>
                            </div>
                        </div>

                        <!-- Bottoni di test -->
                        <div class="mb-4">
                            <h5 class="mb-3"><i class="fas fa-mouse-pointer text-warning me-2"></i>Bottoni Interattivi</h5>
                            <div class="d-flex flex-wrap gap-2">
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#archiviazioneMassaModal">
                                    <i class="fas fa-archive me-2"></i>Apri Modal Archiviazione
                                </button>
                                <button class="btn btn-outline-primary">
                                    <i class="fas fa-eye me-2"></i>Visualizza
                                </button>
                                <button class="btn btn-outline-primary pulse-animation">
                                    <i class="fas fa-download me-2"></i>Scarica
                                </button>
                            </div>
                        </div>

                        <!-- Statistiche di esempio -->
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
                                        <h4 class="text-primary">156</h4>
                                        <p class="mb-0">SOF Completati</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-archive fa-2x text-success mb-2"></i>
                                        <h4 class="text-success">89</h4>
                                        <p class="mb-0">SOF Archiviati</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-ship fa-2x text-info mb-2"></i>
                                        <h4 class="text-info">23</h4>
                                        <p class="mb-0">Viaggi Attivi</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                        <h4 class="text-warning">12</h4>
                                        <p class="mb-0">In Attesa</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Archiviazione di Massa SOF Migliorato -->
    <div class="modal fade" id="archiviazioneMassaModal" tabindex="-1" aria-labelledby="archiviazioneMassaModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="archiviazioneMassaModalLabel">
                        <i class="fas fa-archive me-2"></i>Archiviazione di Massa SOF
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Filtri per Mese/Anno -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="filtroMese" class="form-label">
                                <i class="fas fa-calendar-alt me-2"></i>Seleziona Mese
                            </label>
                            <select class="form-select" id="filtroMese">
                                <option value="">-- Seleziona Mese --</option>
                                <option value="01">Gennaio</option>
                                <option value="02">Febbraio</option>
                                <option value="03">Marzo</option>
                                <option value="04">Aprile</option>
                                <option value="05">Maggio</option>
                                <option value="06">Giugno</option>
                                <option value="07">Luglio</option>
                                <option value="08">Agosto</option>
                                <option value="09">Settembre</option>
                                <option value="10">Ottobre</option>
                                <option value="11">Novembre</option>
                                <option value="12">Dicembre</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="filtroAnno" class="form-label">
                                <i class="fas fa-calendar me-2"></i>Seleziona Anno
                            </label>
                            <select class="form-select" id="filtroAnno">
                                <option value="">-- Seleziona Anno --</option>
                                <option value="2024">2024</option>
                                <option value="2023">2023</option>
                                <option value="2022">2022</option>
                            </select>
                        </div>
                    </div>

                    <!-- Area risultati con dati di esempio -->
                    <div id="risultatiArchiviazione">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Esempio di Risultati</strong> - Seleziona i viaggi da archiviare
                        </div>

                        <!-- Tabella viaggi di esempio -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" class="form-check-input" id="selectAll">
                                        </th>
                                        <th>Viaggio</th>
                                        <th>Nave</th>
                                        <th>Porto</th>
                                        <th>Data</th>
                                        <th>Stato</th>
                                        <th>Azioni</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input viaggio-checkbox" value="1">
                                        </td>
                                        <td><strong>VGG001</strong></td>
                                        <td>Costa Smeralda</td>
                                        <td>Salerno</td>
                                        <td>15/03/2024</td>
                                        <td><span class="badge bg-success">Completato</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input viaggio-checkbox" value="2">
                                        </td>
                                        <td><strong>VGG002</strong></td>
                                        <td>MSC Grandiosa</td>
                                        <td>Gioia Tauro</td>
                                        <td>18/03/2024</td>
                                        <td><span class="badge bg-success">Completato</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input viaggio-checkbox" value="3">
                                        </td>
                                        <td><strong>VGG003</strong></td>
                                        <td>Norwegian Epic</td>
                                        <td>Salerno</td>
                                        <td>22/03/2024</td>
                                        <td><span class="badge bg-warning">In Revisione</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Statistiche selezione -->
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body py-2">
                                        <small class="text-muted">Totale Viaggi</small>
                                        <h5 class="mb-0 text-primary">3</h5>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body py-2">
                                        <small class="text-muted">Selezionati</small>
                                        <h5 class="mb-0 text-success" id="contatoreSelezione">0</h5>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body py-2">
                                        <small class="text-muted">Da Archiviare</small>
                                        <h5 class="mb-0 text-warning" id="contatoreArchiviazione">0</h5>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Chiudi
                    </button>
                    <button type="button" class="btn btn-primary" id="btnArchiviaSelezionati" disabled>
                        <i class="fas fa-archive me-1"></i>Archivia Selezionati
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // JavaScript per gestire le interazioni del modal
        document.addEventListener('DOMContentLoaded', function() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const viaggioCheckboxes = document.querySelectorAll('.viaggio-checkbox');
            const contatoreSelezione = document.getElementById('contatoreSelezione');
            const contatoreArchiviazione = document.getElementById('contatoreArchiviazione');
            const btnArchivia = document.getElementById('btnArchiviaSelezionati');

            // Gestione select all
            selectAllCheckbox.addEventListener('change', function() {
                viaggioCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                aggiornaContatori();
            });

            // Gestione checkbox individuali
            viaggioCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    aggiornaSelectAll();
                    aggiornaContatori();
                });
            });

            function aggiornaSelectAll() {
                const checkedCount = document.querySelectorAll('.viaggio-checkbox:checked').length;
                const totalCount = viaggioCheckboxes.length;

                selectAllCheckbox.checked = checkedCount === totalCount;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
            }

            function aggiornaContatori() {
                const checkedCount = document.querySelectorAll('.viaggio-checkbox:checked').length;

                contatoreSelezione.textContent = checkedCount;
                contatoreArchiviazione.textContent = checkedCount;

                btnArchivia.disabled = checkedCount === 0;

                if (checkedCount > 0) {
                    btnArchivia.classList.add('pulse-animation');
                } else {
                    btnArchivia.classList.remove('pulse-animation');
                }
            }

            // Gestione click bottone archivia
            btnArchivia.addEventListener('click', function() {
                const checkedCount = document.querySelectorAll('.viaggio-checkbox:checked').length;

                if (checkedCount > 0) {
                    // Simula processo di archiviazione
                    this.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Archiviazione in corso...';
                    this.disabled = true;

                    setTimeout(() => {
                        alert(`✅ ${checkedCount} viaggi archiviati con successo!`);

                        // Reset
                        viaggioCheckboxes.forEach(checkbox => checkbox.checked = false);
                        selectAllCheckbox.checked = false;
                        aggiornaContatori();

                        this.innerHTML = '<i class="fas fa-archive me-1"></i>Archivia Selezionati';
                        this.disabled = true;
                    }, 2000);
                }
            });

            // Animazione di apertura modal
            const modal = document.getElementById('archiviazioneMassaModal');
            modal.addEventListener('shown.bs.modal', function() {
                const cards = modal.querySelectorAll('.card');
                cards.forEach((card, index) => {
                    setTimeout(() => {
                        card.classList.add('fade-in-up');
                    }, index * 100);
                });
            });
        });
    </script>
</body>
</html>
