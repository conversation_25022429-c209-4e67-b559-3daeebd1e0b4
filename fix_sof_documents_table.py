#!/usr/bin/env python3
"""
Script per aggiungere le colonne mancanti alla tabella SOF_DOCUMENTS
"""

import sys
import os
from datetime import datetime

# Aggiungi il percorso corrente al path
sys.path.append('.')

def fix_sof_documents_table():
    """Aggiunge le colonne created_at e updated_at alla tabella SOF_DOCUMENTS"""
    try:
        from database import get_db
        from sqlalchemy import text
        
        db = next(get_db())
        
        print("🔧 Aggiornamento tabella SOF_DOCUMENTS...")
        
        # Verifica struttura attuale
        try:
            result = db.execute(text("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'SOF_DOCUMENTS'
                ORDER BY ordinal_position
            """)).fetchall()
            
            print("📋 Struttura attuale:")
            existing_columns = []
            for row in result:
                print(f"   - {row[0]}: {row[1]}")
                existing_columns.append(row[0])
                
        except Exception as e:
            print(f"⚠️ Impossibile ottenere struttura tabella: {e}")
            existing_columns = []
        
        # Aggiungi colonne mancanti
        columns_to_add = [
            ("created_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
            ("updated_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
        ]
        
        for column_name, column_def in columns_to_add:
            if column_name not in existing_columns:
                try:
                    print(f"➕ Aggiunta colonna {column_name}...")
                    db.execute(text(f"""
                        ALTER TABLE "SOF_DOCUMENTS" 
                        ADD COLUMN {column_name} {column_def}
                    """))
                    print(f"✅ Colonna {column_name} aggiunta con successo")
                except Exception as e:
                    print(f"❌ Errore aggiunta colonna {column_name}: {e}")
            else:
                print(f"✅ Colonna {column_name} già esistente")
        
        # Commit delle modifiche
        db.commit()
        print("✅ Modifiche salvate nel database")
        
        # Verifica struttura finale
        try:
            result = db.execute(text("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'SOF_DOCUMENTS'
                ORDER BY ordinal_position
            """)).fetchall()
            
            print("\n📋 Struttura finale:")
            for row in result:
                print(f"   - {row[0]}: {row[1]}")
                
        except Exception as e:
            print(f"⚠️ Impossibile verificare struttura finale: {e}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Errore aggiornamento tabella SOF_DOCUMENTS: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_other_tables():
    """Aggiunge colonne updated_at alle altre tabelle se mancanti"""
    try:
        from database import get_db
        from sqlalchemy import text
        
        db = next(get_db())
        
        print("\n🔧 Aggiornamento altre tabelle...")
        
        tables_to_update = ["ORARI", "IMPORT", "EXPORT"]
        
        for table in tables_to_update:
            try:
                print(f"\n📋 Tabella {table}:")
                
                # Verifica se la colonna esiste
                try:
                    db.execute(text(f'SELECT updated_at FROM "{table}" LIMIT 1'))
                    print(f"   ✅ Colonna updated_at già esistente")
                except Exception:
                    # Colonna non esiste, aggiungila
                    try:
                        print(f"   ➕ Aggiunta colonna updated_at...")
                        db.execute(text(f"""
                            ALTER TABLE "{table}" 
                            ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        """))
                        print(f"   ✅ Colonna updated_at aggiunta con successo")
                    except Exception as e:
                        print(f"   ❌ Errore aggiunta colonna: {e}")
                        
            except Exception as e:
                print(f"❌ Errore elaborazione tabella {table}: {e}")
        
        # Commit delle modifiche
        db.commit()
        print("\n✅ Tutte le modifiche salvate nel database")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Errore aggiornamento altre tabelle: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fixed_tables():
    """Testa che le tabelle siano state aggiornate correttamente"""
    try:
        from database import get_db
        from sqlalchemy import text
        
        db = next(get_db())
        
        print("\n🧪 Test tabelle aggiornate...")
        
        # Test SOF_DOCUMENTS
        try:
            result = db.execute(text("""
                SELECT viaggio_id, filename, created_at, updated_at 
                FROM "SOF_DOCUMENTS" 
                LIMIT 1
            """)).fetchone()
            print("✅ Tabella SOF_DOCUMENTS: OK")
        except Exception as e:
            print(f"❌ Tabella SOF_DOCUMENTS: {e}")
        
        # Test altre tabelle
        tables = ["ORARI", "IMPORT", "EXPORT"]
        for table in tables:
            try:
                result = db.execute(text(f"""
                    SELECT viaggio_id, updated_at 
                    FROM "{table}" 
                    LIMIT 1
                """)).fetchone()
                print(f"✅ Tabella {table}: OK")
            except Exception as e:
                print(f"❌ Tabella {table}: {e}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Errore test tabelle: {e}")
        return False

def main():
    """Esegue tutti i fix necessari"""
    print("🔧 FIX TABELLE DATABASE PER SOF REGENERATION")
    print("=" * 50)
    print(f"🕐 Timestamp: {datetime.now()}")
    print()
    
    steps = [
        ("Fix tabella SOF_DOCUMENTS", fix_sof_documents_table),
        ("Fix altre tabelle", fix_other_tables),
        ("Test tabelle aggiornate", test_fixed_tables),
    ]
    
    results = []
    
    for step_name, step_func in steps:
        print(f"🔧 {step_name}")
        print("-" * 30)
        try:
            result = step_func()
            results.append((step_name, result))
            print(f"{'✅ COMPLETATO' if result else '❌ FALLITO'}")
        except Exception as e:
            print(f"❌ ERRORE: {e}")
            results.append((step_name, False))
        print()
    
    print("📊 RIEPILOGO")
    print("=" * 50)
    for step_name, result in results:
        status = "✅ OK" if result else "❌ ERRORE"
        print(f"{step_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\nTotale: {passed}/{total} step completati")
    
    if passed == total:
        print("🎉 Tutte le tabelle sono state aggiornate con successo!")
        print("   Ora puoi testare nuovamente il download SOF.")
    else:
        print("⚠️ Alcuni step sono falliti. Controlla gli errori sopra.")

if __name__ == "__main__":
    main()
