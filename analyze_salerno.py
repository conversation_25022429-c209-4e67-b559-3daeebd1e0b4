#!/usr/bin/env python3
"""
Script per analizzare il file Excel salerno123.xlsx
"""

import pandas as pd
import os

def analyze_salerno_file():
    """Analizza il file salerno123.xlsx per capire la struttura"""
    
    file_path = r"c:\Users\<USER>\Desktop\cline_thebest\salerno123.xlsx"
    
    if not os.path.exists(file_path):
        print(f"❌ File non trovato: {file_path}")
        return
    
    try:
        print("🔍 ANALISI FILE SALERNO123.XLSX")
        print("=" * 50)
        
        # Leggi tutti i fogli disponibili
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        
        print(f"📋 Fogli disponibili nel file:")
        for i, sheet in enumerate(sheet_names, 1):
            print(f"   {i}. {sheet}")
        
        print("\n" + "=" * 50)
        
        # Analizza ogni foglio
        for sheet_name in sheet_names:
            print(f"\n📄 FOGLIO: {sheet_name}")
            print("-" * 40)
            
            try:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                print(f"📊 Dimensioni: {df.shape[0]} righe x {df.shape[1]} colonne")
                print(f"🏷️ Colonne: {list(df.columns)}")
                
                if not df.empty:
                    print(f"\n📋 Prime 10 righe:")
                    for i in range(min(10, len(df))):
                        row = df.iloc[i]
                        col_a = row.iloc[0] if len(row) > 0 else 'N/A'
                        col_b = row.iloc[1] if len(row) > 1 else 'N/A'
                        print(f"   {i+1:2d}. Colonna A: '{col_a}' | Colonna B: '{col_b}'")
                    
                    # Mostra statistiche per colonne numeriche
                    numeric_cols = df.select_dtypes(include=['number']).columns
                    if len(numeric_cols) > 0:
                        print(f"\n📊 Statistiche colonne numeriche:")
                        print(df[numeric_cols].describe())
                
                else:
                    print("   ⚠️ Foglio vuoto")
                    
            except Exception as e:
                print(f"   ❌ Errore lettura foglio '{sheet_name}': {e}")
    
    except Exception as e:
        print(f"❌ Errore lettura file Excel: {e}")
        print(f"💡 Assicurati di avere pandas e openpyxl installati:")
        print(f"   pip install pandas openpyxl")

if __name__ == "__main__":
    analyze_salerno_file()
