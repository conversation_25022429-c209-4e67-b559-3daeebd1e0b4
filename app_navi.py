from fastapi import <PERSON><PERSON><PERSON>, Request, Form
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from pathlib import Path

app = FastAPI()

# Monta la cartella static
app.mount("/static", StaticFiles(directory="static"), name="static")

# Configura i templates
templates = Jinja2Templates(directory="templates")

@app.get("/", response_class=HTMLResponse)
def home(request: Request):
    return templates.TemplateResponse(
        "navi_semplice.html",
        {
            "request": request,
            "navi": [
                {"id": 1, "nave": "COSTA SMERALDA", "codice_nave": "CSM", "prefisso_viaggio": "SME"},
                {"id": 2, "nave": "MSC FANTASIA", "codice_nave": "MSF", "prefisso_viaggio": "FAN"}
            ],
            "prefixes": ["SME", "FAN"]
        }
    )

@app.post("/add", response_class=HTMLResponse)
def add_nave(
    request: Request,
    nome_nave: str = Form(...),
    codice_nave: str = Form(...),
    prefisso_viaggio: str = Form(...)
):
    # Reindirizza alla home
    return RedirectResponse(url="/", status_code=303)
