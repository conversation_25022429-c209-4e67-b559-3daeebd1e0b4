#!/usr/bin/env python3
"""
Script di debug completo per identificare e risolvere i problemi di archiviazione
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://127.0.0.1:8003"

def test_endpoint_archiviazione():
    """Test dell'endpoint di archiviazione"""
    print("🧪 Test endpoint archiviazione...")

    test_cases = [
        {
            "nome": "Anno 2024",
            "parametri": {"periodo_tipo": "anno", "periodo_valore": "2024"}
        },
        {
            "nome": "Mese Giugno 2024",
            "parametri": {"periodo_tipo": "mese", "anno": "2024", "mese": "6"}
        }
    ]

    # Test endpoint normale (con autenticazione)
    print("\n   📡 Test endpoint normale (con autenticazione):")
    for test_case in test_cases:
        print(f"\n   🔍 Test: {test_case['nome']}")

        try:
            response = requests.post(
                f"{BASE_URL}/api/operativo/sof/archivia-json",
                json=test_case["parametri"],
                headers={"Content-Type": "application/json"},
                timeout=10
            )

            print(f"      Status: {response.status_code}")

            try:
                result = response.json()
                print(f"      Response: {json.dumps(result, indent=2)}")

                if response.status_code == 401:
                    print("      ❌ Problema autenticazione - Serve login")
                elif response.status_code == 404:
                    print("      ⚠️ Nessun viaggio trovato (normale se DB vuoto)")
                elif response.status_code == 500:
                    print("      ❌ Errore server - Controlla log applicazione")
                elif response.status_code == 200:
                    print("      ✅ Richiesta riuscita")

            except json.JSONDecodeError:
                print(f"      ❌ Risposta non JSON: {response.text[:200]}")

        except Exception as e:
            print(f"      ❌ Errore richiesta: {e}")

    # Test endpoint di test (senza autenticazione)
    print("\n   🧪 Test endpoint di test (senza autenticazione):")
    for test_case in test_cases:
        print(f"\n   🔍 Test: {test_case['nome']}")

        try:
            response = requests.post(
                f"{BASE_URL}/api/operativo/sof/archivia-json/test",
                json=test_case["parametri"],
                headers={"Content-Type": "application/json"},
                timeout=10
            )

            print(f"      Status: {response.status_code}")

            try:
                result = response.json()
                print(f"      Response: {json.dumps(result, indent=2)}")

                if response.status_code == 200 and result.get("success"):
                    print("      ✅ Endpoint di test funziona!")
                else:
                    print("      ❌ Endpoint di test ha problemi")

            except json.JSONDecodeError:
                print(f"      ❌ Risposta non JSON: {response.text[:200]}")

        except Exception as e:
            print(f"      ❌ Errore richiesta: {e}")

def check_javascript_functions():
    """Controlla se le funzioni JavaScript esistono nella pagina"""
    print("\n🧪 Test funzioni JavaScript...")
    
    try:
        response = requests.get(f"{BASE_URL}/operativo/sof/archiviati?tab=gestione", timeout=10)
        content = response.text
        
        functions_to_check = [
            ("aggiornaCampiPeriodo", "function aggiornaCampiPeriodo()"),
            ("validaCampiPeriodo", "function validaCampiPeriodo()"),
            ("archiviaPerPeriodo", "function archiviaPerPeriodo()"),
            ("archiviaSelezionati", "function archiviaSelezionati()"),
            ("eseguiArchiviazione", "function eseguiArchiviazione("),
            ("testConnessioneAPI", "function testConnessioneAPI()")
        ]
        
        for func_name, func_signature in functions_to_check:
            if func_signature in content:
                print(f"   ✅ {func_name}: Trovata")
            else:
                print(f"   ❌ {func_name}: MANCANTE")
                
        # Controlla elementi HTML
        html_elements = [
            ("tipoPeriodo", 'id="tipoPeriodo"'),
            ("campiPeriodo", 'id="campiPeriodo"'),
            ("btnArchiviaPeriodo", 'id="btnArchiviaPeriodo"'),
            ("btnArchiviaSelezionati", 'id="btnArchiviaSelezionati"')
        ]
        
        print("\n   Elementi HTML:")
        for elem_name, elem_id in html_elements:
            if elem_id in content:
                print(f"   ✅ {elem_name}: Trovato")
            else:
                print(f"   ❌ {elem_name}: MANCANTE")
                
    except Exception as e:
        print(f"   ❌ Errore controllo JavaScript: {e}")

def check_backend_logs():
    """Suggerimenti per controllare i log del backend"""
    print("\n🔍 Controllo log backend...")
    print("   📋 Per vedere gli errori del backend:")
    print("   1. Controlla il terminale dove gira 'python main.py'")
    print("   2. Cerca errori con emoji ❌ o 🔧")
    print("   3. Controlla il file 'snip.log' se esiste")

def check_database_connection():
    """Test connessione database"""
    print("\n🗄️ Test connessione database...")
    print("   📋 Problemi comuni:")
    print("   - PostgreSQL non in esecuzione")
    print("   - Credenziali database errate")
    print("   - Tabelle mancanti (VIAGGIO, AGENTE, etc.)")
    print("   - Permessi utente insufficienti")

def generate_fix_suggestions():
    """Genera suggerimenti per risolvere i problemi"""
    print("\n🔧 SUGGERIMENTI PER RISOLVERE I PROBLEMI:")
    print("=" * 60)
    
    print("\n1. 🔐 PROBLEMA AUTENTICAZIONE:")
    print("   - Vai su http://127.0.0.1:8003/login")
    print("   - Accedi con credenziali admin")
    print("   - Verifica di avere ruolo ADMIN o SUPER_ADMIN")
    
    print("\n2. 📄 PROBLEMA PAGINA NON CARICA:")
    print("   - Controlla che l'applicazione sia in esecuzione")
    print("   - Verifica la porta 8003")
    print("   - Controlla errori nel terminale")
    
    print("\n3. 🔧 PROBLEMA JAVASCRIPT:")
    print("   - Apri console browser (F12)")
    print("   - Cerca errori JavaScript")
    print("   - Verifica che le funzioni esistano")
    
    print("\n4. 🗄️ PROBLEMA DATABASE:")
    print("   - Avvia PostgreSQL")
    print("   - Controlla credenziali in config.py")
    print("   - Verifica esistenza tabelle")
    
    print("\n5. 🎯 PROBLEMA API:")
    print("   - Controlla log nel terminale applicazione")
    print("   - Verifica endpoint /api/operativo/sof/archivia-json")
    print("   - Testa con parametri semplici")

def main():
    """Funzione principale di debug"""
    print("🚀 DEBUG COMPLETO ARCHIVIAZIONE SOF")
    print("=" * 60)
    
    # Test 1: Endpoint API
    test_endpoint_archiviazione()
    
    # Test 2: JavaScript e HTML
    check_javascript_functions()
    
    # Test 3: Suggerimenti log
    check_backend_logs()
    
    # Test 4: Database
    check_database_connection()
    
    # Test 5: Suggerimenti
    generate_fix_suggestions()
    
    print("\n" + "=" * 60)
    print("🏁 DEBUG COMPLETATO")
    print("\n💡 PROSSIMI PASSI:")
    print("1. Controlla i risultati sopra")
    print("2. Risolvi i problemi identificati")
    print("3. Testa manualmente nell'applicazione")
    print("4. Se persistono problemi, controlla i log del server")

if __name__ == "__main__":
    main()
