#!/usr/bin/env python3

import psycopg2
import sys

def migrate_missing_fields():
    print("🔧 MIGRAZIONE CAMPI MANCANTI")
    print("=" * 50)
    
    try:
        # Connessione al database
        conn = psycopg2.connect(
            host='localhost',
            database='AGENTE',
            user='re77',
            password='271077',
            connect_timeout=5
        )
        conn.autocommit = False  # Transazioni manuali
        cursor = conn.cursor()
        
        migrations_executed = []
        
        print("🚀 Inizio migrazioni...")
        
        # 1. Aggiunta campo tema_preferito alla tabella AGENTE
        print("\n1️⃣ Aggiunta campo tema_preferito a AGENTE...")
        try:
            # Verifica se il campo esiste già
            cursor.execute("""
                SELECT column_name FROM information_schema.columns 
                WHERE table_name = 'AGENTE' AND column_name = 'tema_preferito'
            """)
            
            if not cursor.fetchone():
                # Crea enum se non esiste
                cursor.execute("""
                    DO $$ BEGIN
                        CREATE TYPE temaenum AS ENUM ('light', 'dark', 'maritime');
                    EXCEPTION
                        WHEN duplicate_object THEN null;
                    END $$;
                """)
                
                # Aggiungi colonna
                cursor.execute("""
                    ALTER TABLE "AGENTE" 
                    ADD COLUMN tema_preferito temaenum DEFAULT 'maritime'
                """)
                
                print("   ✅ Campo tema_preferito aggiunto con successo")
                migrations_executed.append("AGENTE.tema_preferito")
            else:
                print("   ℹ️  Campo tema_preferito già esistente")
                
        except Exception as e:
            print(f"   ❌ Errore aggiunta tema_preferito: {e}")
        
        # 2. Aggiunta campo eta_originale alla tabella VIAGGIO
        print("\n2️⃣ Aggiunta campo eta_originale a VIAGGIO...")
        try:
            cursor.execute("""
                SELECT column_name FROM information_schema.columns 
                WHERE table_name = 'VIAGGIO' AND column_name = 'eta_originale'
            """)
            
            if not cursor.fetchone():
                cursor.execute("""
                    ALTER TABLE "VIAGGIO" 
                    ADD COLUMN eta_originale date
                """)
                
                print("   ✅ Campo eta_originale aggiunto con successo")
                migrations_executed.append("VIAGGIO.eta_originale")
            else:
                print("   ℹ️  Campo eta_originale già esistente")
                
        except Exception as e:
            print(f"   ❌ Errore aggiunta eta_originale: {e}")
        
        # 3. Aggiunta campo archiviato alla tabella VIAGGIO
        print("\n3️⃣ Aggiunta campo archiviato a VIAGGIO...")
        try:
            cursor.execute("""
                SELECT column_name FROM information_schema.columns 
                WHERE table_name = 'VIAGGIO' AND column_name = 'archiviato'
            """)
            
            if not cursor.fetchone():
                cursor.execute("""
                    ALTER TABLE "VIAGGIO" 
                    ADD COLUMN archiviato character varying(2) DEFAULT 'no'
                """)
                
                print("   ✅ Campo archiviato aggiunto con successo")
                migrations_executed.append("VIAGGIO.archiviato")
            else:
                print("   ℹ️  Campo archiviato già esistente")
                
        except Exception as e:
            print(f"   ❌ Errore aggiunta archiviato: {e}")
        
        # 4. Aggiornamento valori di default per campi esistenti
        print("\n4️⃣ Aggiornamento valori di default...")
        try:
            # Imposta tema_preferito = 'maritime' per utenti che hanno NULL
            cursor.execute("""
                UPDATE "AGENTE" 
                SET tema_preferito = 'maritime' 
                WHERE tema_preferito IS NULL
            """)
            updated_users = cursor.rowcount
            if updated_users > 0:
                print(f"   ✅ Aggiornati {updated_users} utenti con tema_preferito = 'maritime'")
                migrations_executed.append(f"Updated {updated_users} users tema_preferito")
            
            # Imposta archiviato = 'no' per viaggi che hanno NULL
            cursor.execute("""
                UPDATE "VIAGGIO" 
                SET archiviato = 'no' 
                WHERE archiviato IS NULL
            """)
            updated_viaggi = cursor.rowcount
            if updated_viaggi > 0:
                print(f"   ✅ Aggiornati {updated_viaggi} viaggi con archiviato = 'no'")
                migrations_executed.append(f"Updated {updated_viaggi} viaggi archiviato")
                
        except Exception as e:
            print(f"   ❌ Errore aggiornamento valori: {e}")
        
        # 5. Verifica finale
        print("\n5️⃣ Verifica finale struttura...")
        
        # Verifica AGENTE
        cursor.execute("""
            SELECT column_name, data_type, column_default 
            FROM information_schema.columns 
            WHERE table_name = 'AGENTE' AND column_name = 'tema_preferito'
        """)
        tema_result = cursor.fetchone()
        if tema_result:
            print(f"   ✅ AGENTE.tema_preferito: {tema_result[1]} DEFAULT {tema_result[2]}")
        
        # Verifica VIAGGIO
        cursor.execute("""
            SELECT column_name, data_type, column_default 
            FROM information_schema.columns 
            WHERE table_name = 'VIAGGIO' AND column_name IN ('eta_originale', 'archiviato')
            ORDER BY column_name
        """)
        viaggio_results = cursor.fetchall()
        for result in viaggio_results:
            print(f"   ✅ VIAGGIO.{result[0]}: {result[1]} DEFAULT {result[2]}")
        
        # Commit delle modifiche
        if migrations_executed:
            conn.commit()
            print(f"\n✅ MIGRAZIONE COMPLETATA!")
            print(f"📋 Modifiche applicate:")
            for migration in migrations_executed:
                print(f"   - {migration}")
        else:
            print(f"\nℹ️  Nessuna migrazione necessaria - tutti i campi già presenti")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Errore durante la migrazione: {e}")
        try:
            conn.rollback()
            conn.close()
        except:
            pass
        return False

if __name__ == "__main__":
    success = migrate_missing_fields()
    if success:
        print("\n🎉 Migrazione completata con successo!")
        print("💡 Riavvia l'app per utilizzare i nuovi campi")
    else:
        print("\n❌ Migrazione fallita!")
    
    sys.exit(0 if success else 1)
