#!/usr/bin/env python3
"""
Script per analizzare la struttura del file gioia.xls
"""

import pandas as pd
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_excel_structure():
    """Analizza la struttura del file Excel"""
    try:
        # Prova a leggere il file con diversi parametri
        print("🔍 ANALISI STRUTTURA FILE EXCEL gioia.xls")
        print("=" * 50)
        
        # Lettura 1: Senza header
        print("\n1. Lettura senza header:")
        df1 = pd.read_excel('gioia.xls', header=None)
        print(f"   Dimensioni: {df1.shape}")
        print(f"   Prime 10 righe:")
        for i, row in df1.head(10).iterrows():
            print(f"     Riga {i}: {list(row.values)}")
        
        # Lettura 2: Con header alla riga 0
        print("\n2. Lettura con header alla riga 0:")
        try:
            df2 = pd.read_excel('gioia.xls', header=0)
            print(f"   Dimensioni: {df2.shape}")
            print(f"   Colonne: {list(df2.columns)}")
            print(f"   Prime 5 righe:")
            for i, row in df2.head().iterrows():
                print(f"     Riga {i}: {list(row.values)}")
        except Exception as e:
            print(f"   Errore: {e}")
        
        # Lettura 3: Prova diverse righe come header
        for header_row in [1, 2, 3]:
            print(f"\n3.{header_row}. Lettura con header alla riga {header_row}:")
            try:
                df3 = pd.read_excel('gioia.xls', header=header_row)
                print(f"   Dimensioni: {df3.shape}")
                print(f"   Colonne: {list(df3.columns)}")
                print(f"   Prime 3 righe:")
                for i, row in df3.head(3).iterrows():
                    print(f"     Riga {i}: {list(row.values)}")
            except Exception as e:
                print(f"   Errore: {e}")
        
        # Lettura 4: Specifica colonne A e B
        print("\n4. Lettura solo colonne A e B:")
        try:
            df4 = pd.read_excel('gioia.xls', usecols=[0, 1], header=None)
            print(f"   Dimensioni: {df4.shape}")
            print(f"   Prime 20 righe:")
            for i, row in df4.head(20).iterrows():
                col_a = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else "VUOTO"
                col_b = str(row.iloc[1]).strip() if pd.notna(row.iloc[1]) else "VUOTO"
                print(f"     Riga {i}: A='{col_a}' | B='{col_b}'")
        except Exception as e:
            print(f"   Errore: {e}")
        
        # Lettura 5: Cerca righe che contengono nomi di navi
        print("\n5. Ricerca nomi di navi nel file:")
        try:
            df5 = pd.read_excel('gioia.xls', header=None)
            
            # Lista di nomi di navi conosciuti per test
            navi_test = [
                "APOLLON LEADER", "ATLAS LEADER", "Blue Aspire", "CELANDINE", 
                "CLEMENTINE", "Grande Ellade", "Grande Ghana", "FINNSIRIUS",
                "THRUXTON", "GREAT ANTWERP", "Grand Neptune", "Grande Marocco",
                "Eurocargo", "Repubblica", "GRANDE NAPOLI", "Finnsun"
            ]
            
            found_navi = []
            for i, row in df5.iterrows():
                for col_idx, cell_value in enumerate(row):
                    if pd.notna(cell_value):
                        cell_str = str(cell_value).strip()
                        for nave_test in navi_test:
                            if nave_test.lower() in cell_str.lower():
                                found_navi.append({
                                    'riga': i,
                                    'colonna': col_idx,
                                    'valore': cell_str,
                                    'nave_trovata': nave_test
                                })
                                break
            
            if found_navi:
                print(f"   Trovate {len(found_navi)} corrispondenze:")
                for match in found_navi[:10]:  # Mostra solo le prime 10
                    print(f"     Riga {match['riga']}, Col {match['colonna']}: '{match['valore']}' (contiene '{match['nave_trovata']}')")
            else:
                print("   Nessuna corrispondenza trovata con nomi di navi conosciuti")
        except Exception as e:
            print(f"   Errore: {e}")
        
        # Lettura 6: Analizza tutte le celle per trovare pattern
        print("\n6. Analisi pattern nelle celle:")
        try:
            df6 = pd.read_excel('gioia.xls', header=None)
            
            # Conta tipi di dati per colonna
            for col_idx in range(min(5, df6.shape[1])):  # Analizza prime 5 colonne
                col_data = df6.iloc[:, col_idx].dropna()
                
                numeric_count = 0
                text_count = 0
                mixed_count = 0
                
                for value in col_data:
                    str_value = str(value).strip()
                    if str_value.replace('.', '').replace(',', '').isdigit():
                        numeric_count += 1
                    elif str_value.isalpha() or ' ' in str_value:
                        text_count += 1
                    else:
                        mixed_count += 1
                
                print(f"   Colonna {col_idx}: {numeric_count} numerici, {text_count} testo, {mixed_count} misti")
                
                # Mostra alcuni esempi
                examples = col_data.head(5).tolist()
                print(f"     Esempi: {examples}")
        
        except Exception as e:
            print(f"   Errore: {e}")
        
    except Exception as e:
        logger.error(f"Errore generale analisi file: {e}")

if __name__ == "__main__":
    analyze_excel_structure()
