#!/usr/bin/env python3
"""
Debug completo per il problema delle dropdown porti
"""

import requests
import json
from bs4 import BeautifulSoup

def test_api_atlas():
    """Test API ATLAS"""
    print("🧪 TEST 1: API ATLAS")
    try:
        response = requests.get("http://localhost:8002/api/atlas?limit=5")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Success: {data.get('success')}")
            print(f"   Porti trovati: {len(data.get('data', []))}")
            
            if data.get('data'):
                print("   Primi 3 porti:")
                for i, porto in enumerate(data['data'][:3]):
                    print(f"     {i+1}. {porto.get('porto')} ({porto.get('stato')}) - ID: {porto.get('id_cod')}")
            return True
        else:
            print(f"   ❌ Errore HTTP: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Errore: {e}")
        return False

def test_html_elements():
    """Test elementi HTML nella pagina"""
    print("\n🧪 TEST 2: ELEMENTI HTML")
    try:
        response = requests.get("http://localhost:8002/operativo/sof/da-realizzare")
        print(f"   Status pagina: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Cerca elementi con ID porto
            porto_elements = soup.find_all(attrs={"id": lambda x: x and "porto" in x.lower()})
            print(f"   Elementi con 'porto' nell'ID: {len(porto_elements)}")
            
            for elem in porto_elements:
                print(f"     - ID: {elem.get('id')}, Tag: {elem.name}, Name: {elem.get('name', 'N/A')}")
            
            # Cerca specificamente gli elementi che ci servono
            target_ids = ['porto_arrivo', 'porto_destinazione', 'modifica_porto_arrivo', 'modifica_porto_destinazione']
            for target_id in target_ids:
                elem = soup.find(attrs={"id": target_id})
                if elem:
                    print(f"   ✅ Trovato {target_id}: {elem.name}")
                else:
                    print(f"   ❌ NON trovato {target_id}")
            
            return True
        else:
            print(f"   ❌ Errore caricamento pagina: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Errore: {e}")
        return False

def test_javascript_files():
    """Test file JavaScript"""
    print("\n🧪 TEST 3: FILE JAVASCRIPT")
    try:
        # Test viaggi.js
        response = requests.get("http://localhost:8002/static/js/viaggi.js")
        print(f"   viaggi.js status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            
            # Cerca funzioni chiave
            functions_to_check = [
                'popolaSelectPorti',
                'caricaPortiAtlas',
                'DOMContentLoaded'
            ]
            
            for func in functions_to_check:
                if func in content:
                    print(f"   ✅ Funzione {func} trovata")
                else:
                    print(f"   ❌ Funzione {func} NON trovata")
            
            # Conta le occorrenze di popolaSelectPorti
            count = content.count('popolaSelectPorti')
            print(f"   Occorrenze 'popolaSelectPorti': {count}")
            
            return True
        else:
            print(f"   ❌ Errore caricamento viaggi.js: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Errore: {e}")
        return False

def create_debug_script():
    """Crea uno script di debug da inserire nella pagina"""
    print("\n🧪 TEST 4: CREAZIONE SCRIPT DEBUG")
    
    debug_script = """
<script>
// SCRIPT DEBUG COMPLETO PER PORTI
console.log('🚀 INIZIO DEBUG COMPLETO PORTI');

// Test 1: Verifica elementi DOM
function debugElementiDOM() {
    console.log('\\n🔍 TEST 1: ELEMENTI DOM');
    
    const targetIds = ['porto_arrivo', 'porto_destinazione', 'modifica_porto_arrivo', 'modifica_porto_destinazione'];
    const results = {};
    
    targetIds.forEach(id => {
        const elem = document.getElementById(id);
        results[id] = {
            exists: !!elem,
            tag: elem ? elem.tagName : null,
            visible: elem ? getComputedStyle(elem).display !== 'none' : false,
            parent: elem ? elem.parentElement.className : null
        };
        console.log(`   ${id}: ${results[id].exists ? '✅' : '❌'} ${JSON.stringify(results[id])}`);
    });
    
    return results;
}

// Test 2: Verifica API
async function debugAPI() {
    console.log('\\n🌐 TEST 2: API ATLAS');
    
    try {
        const response = await fetch('/api/atlas?limit=3');
        console.log(`   Status: ${response.status}`);
        
        const data = await response.json();
        console.log(`   Success: ${data.success}`);
        console.log(`   Porti: ${data.data ? data.data.length : 0}`);
        
        if (data.data && data.data.length > 0) {
            console.log('   Primo porto:', data.data[0]);
        }
        
        return data;
    } catch (error) {
        console.error('   ❌ Errore API:', error);
        return null;
    }
}

// Test 3: Popolamento manuale
async function debugPopolamentoManuale() {
    console.log('\\n🔧 TEST 3: POPOLAMENTO MANUALE');
    
    const apiData = await debugAPI();
    if (!apiData || !apiData.success) {
        console.log('   ❌ API non funziona, skip test');
        return;
    }
    
    const portoArrivo = document.getElementById('porto_arrivo');
    if (portoArrivo) {
        console.log('   🔧 Popolamento manuale porto_arrivo...');
        
        // Salva HTML originale
        const originalHTML = portoArrivo.innerHTML;
        
        // Popola manualmente
        portoArrivo.innerHTML = '<option value="">MANUALE - Seleziona porto...</option>';
        apiData.data.forEach(porto => {
            const option = document.createElement('option');
            option.value = porto.id_cod;
            option.textContent = `MANUALE - ${porto.porto}`;
            portoArrivo.appendChild(option);
        });
        
        console.log(`   ✅ Popolato con ${portoArrivo.options.length} opzioni`);
        
        // Ripristina dopo 5 secondi
        setTimeout(() => {
            portoArrivo.innerHTML = originalHTML;
            console.log('   🔄 HTML originale ripristinato');
        }, 5000);
    } else {
        console.log('   ❌ Elemento porto_arrivo non trovato');
    }
}

// Test 4: Verifica funzioni globali
function debugFunzioniGlobali() {
    console.log('\\n🔍 TEST 4: FUNZIONI GLOBALI');
    
    const functions = ['popolaSelectPorti', 'caricaPortiAtlas'];
    functions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`   ✅ ${funcName} è disponibile globalmente`);
        } else {
            console.log(`   ❌ ${funcName} NON è disponibile globalmente`);
        }
    });
}

// Esegui tutti i test
async function eseguiTuttiITest() {
    console.log('🚀 ESECUZIONE COMPLETA DEBUG PORTI');
    
    debugElementiDOM();
    await debugAPI();
    debugFunzioniGlobali();
    
    // Test popolamento manuale dopo 2 secondi
    setTimeout(debugPopolamentoManuale, 2000);
}

// Funzione globale per test manuale
window.debugPorti = eseguiTuttiITest;

// Esegui automaticamente dopo 1 secondo
setTimeout(eseguiTuttiITest, 1000);

console.log('✅ Script debug caricato. Usa debugPorti() per test manuale.');
</script>
"""
    
    return debug_script

def main():
    """Esegue tutti i test"""
    print("🔍 DEBUG COMPLETO PROBLEMA PORTI")
    print("=" * 50)
    
    # Test API
    api_ok = test_api_atlas()
    
    # Test HTML
    html_ok = test_html_elements()
    
    # Test JavaScript
    js_ok = test_javascript_files()
    
    # Crea script debug
    debug_script = create_debug_script()
    
    print("\n" + "=" * 50)
    print("📋 RIEPILOGO:")
    print(f"   API ATLAS: {'✅' if api_ok else '❌'}")
    print(f"   HTML Elements: {'✅' if html_ok else '❌'}")
    print(f"   JavaScript: {'✅' if js_ok else '❌'}")
    
    print("\n🎯 PROSSIMI PASSI:")
    print("1. Aggiungi lo script debug alla pagina")
    print("2. Apri la console del browser")
    print("3. Controlla i log automatici")
    print("4. Usa debugPorti() per test manuali")
    
    # Salva script debug
    with open('debug_script.html', 'w', encoding='utf-8') as f:
        f.write(debug_script)
    print("\n💾 Script debug salvato in 'debug_script.html'")

if __name__ == "__main__":
    main()
