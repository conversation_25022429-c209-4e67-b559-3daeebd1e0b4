<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Upload Debug</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>Test Upload File JSON - Debug</h2>
        
        <div class="card">
            <div class="card-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="fileInput" class="form-label">Seleziona file JSON</label>
                        <input type="file" class="form-control" id="fileInput" accept=".json" multiple>
                    </div>
                    <button type="button" class="btn btn-success" onclick="testUpload()" id="uploadBtn">
                        Test Upload
                    </button>
                </form>
                
                <div class="mt-3" id="results">
                    <!-- Risultati qui -->
                </div>
            </div>
        </div>
    </div>

    <script>
        async function testUpload() {
            const fileInput = document.getElementById('fileInput');
            const results = document.getElementById('results');
            
            if (!fileInput.files || fileInput.files.length === 0) {
                results.innerHTML = '<div class="alert alert-warning">Seleziona almeno un file</div>';
                return;
            }
            
            try {
                console.log('🧪 Test upload iniziato');
                
                // Prepara FormData
                const formData = new FormData();
                for (let i = 0; i < fileInput.files.length; i++) {
                    formData.append('files', fileInput.files[i]);
                    console.log(`📁 File ${i}: ${fileInput.files[i].name}`);
                }
                
                results.innerHTML = '<div class="alert alert-info">Caricamento in corso...</div>';
                
                // Test fetch (usando endpoint debug)
                const response = await fetch('/api/sof/archiviati/upload/test', {
                    method: 'POST',
                    body: formData
                });
                
                console.log('📊 Response status:', response.status);
                console.log('📊 Response headers:', response.headers);
                
                const responseText = await response.text();
                console.log('📄 Response text:', responseText);
                
                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (e) {
                    result = { error: 'Invalid JSON response', raw: responseText };
                }
                
                results.innerHTML = `
                    <div class="alert ${response.ok ? 'alert-success' : 'alert-danger'}">
                        <h5>Status: ${response.status}</h5>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </div>
                `;
                
            } catch (error) {
                console.error('❌ Errore:', error);
                results.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>Errore JavaScript:</h5>
                        <pre>${error.message}</pre>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
