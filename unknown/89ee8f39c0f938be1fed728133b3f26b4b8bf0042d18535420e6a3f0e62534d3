#!/usr/bin/env python3
"""
Script per verificare lo stato del viaggio 43 e diagnosticare il problema 404
"""

import sys
import os
from datetime import datetime

# Aggiungi il percorso corrente al path
sys.path.append('.')

def check_viaggio_43():
    """Verifica lo stato del viaggio 43"""
    try:
        from database import get_db
        from sqlalchemy import text
        
        db = next(get_db())
        
        print("🔍 DIAGNOSI VIAGGIO 43")
        print("=" * 50)
        
        # Verifica se il viaggio 43 esiste
        result = db.execute(text("""
            SELECT v.id, v.viaggio, v.visibile, v.data_arrivo, v.data_partenza,
                   n."Nave" as nome_nave, pg.nome_porto
            FROM "VIAGGIO" v
            LEFT JOIN "NAVI" n ON v.nave_id = n.id
            LEFT JOIN "PORTI_GESTIONE" pg ON v.porto_gestione_id = pg.id_porto
            WHERE v.id = 43
        """)).fetchone()
        
        if result:
            print("✅ Viaggio 43 TROVATO:")
            print(f"   ID: {result[0]}")
            print(f"   Nome: {result[1]}")
            print(f"   Visibile: {result[2]}")
            print(f"   Data arrivo: {result[3]}")
            print(f"   Data partenza: {result[4]}")
            print(f"   Nave: {result[5]}")
            print(f"   Porto: {result[6]}")
            
            # Verifica se ha SOF
            sof_result = db.execute(text("""
                SELECT filename, created_at, file_path
                FROM "SOF_DOCUMENTS"
                WHERE viaggio_id = 43
            """)).fetchone()
            
            if sof_result:
                print(f"\n📄 SOF ESISTENTE:")
                print(f"   File: {sof_result[0]}")
                print(f"   Creato: {sof_result[1]}")
                print(f"   Path: {sof_result[2]}")
                print(f"   File esiste: {'✅' if os.path.exists(sof_result[2]) else '❌'}")
            else:
                print("\n📄 Nessun SOF trovato")
            
            # Verifica orari
            orari_result = db.execute(text("""
                SELECT COUNT(*) FROM "ORARI" WHERE viaggio_id = 43
            """)).fetchone()
            
            print(f"\n⏰ ORARI: {orari_result[0] if orari_result else 0} record")
            
            # Verifica import/export
            import_result = db.execute(text("""
                SELECT COUNT(*) FROM "IMPORT" WHERE viaggio_id = 43
            """)).fetchone()
            
            export_result = db.execute(text("""
                SELECT COUNT(*) FROM "EXPORT" WHERE viaggio_id = 43
            """)).fetchone()
            
            print(f"📦 IMPORT: {import_result[0] if import_result else 0} record")
            print(f"📤 EXPORT: {export_result[0] if export_result else 0} record")
            
            # Diagnosi problema 404
            print(f"\n🔍 DIAGNOSI PROBLEMA 404:")
            if result[2] == 'no':
                print("❌ PROBLEMA IDENTIFICATO: Viaggio non visibile (visibile = 'no')")
                print("   L'endpoint update_viaggio richiede che il viaggio sia visibile")
                print("   Questo succede quando il viaggio ha già un SOF generato")
                print("\n💡 SOLUZIONI POSSIBILI:")
                print("   1. Modificare l'endpoint per permettere update di viaggi non visibili")
                print("   2. Ripristinare il viaggio prima di modificarlo")
                print("   3. Usare un endpoint diverso per viaggi con SOF")
            else:
                print("✅ Viaggio visibile, problema potrebbe essere altrove")
                
        else:
            print("❌ Viaggio 43 NON TROVATO nel database")
            
            # Cerca viaggi simili
            similar = db.execute(text("""
                SELECT id, viaggio, visibile FROM "VIAGGIO" 
                WHERE id BETWEEN 40 AND 45
                ORDER BY id
            """)).fetchall()
            
            print("\n🔍 Viaggi simili (ID 40-45):")
            for row in similar:
                print(f"   ID {row[0]}: {row[1]} (visibile: {row[2]})")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Errore verifica viaggio 43: {e}")
        import traceback
        traceback.print_exc()
        return False

def suggest_solutions():
    """Suggerisce soluzioni per il problema"""
    print("\n🛠️ SOLUZIONI CONSIGLIATE")
    print("=" * 50)
    
    print("1. 🔧 MODIFICA ENDPOINT (Consigliato)")
    print("   Modificare l'endpoint update_viaggio per permettere")
    print("   l'aggiornamento di viaggi non visibili")
    
    print("\n2. 🔄 RIPRISTINO VIAGGIO")
    print("   Usare l'endpoint ripristina_viaggio prima di modificare:")
    print("   POST /api/viaggi/43/ripristina")
    
    print("\n3. 📋 NUOVO ENDPOINT")
    print("   Creare un endpoint specifico per viaggi con SOF:")
    print("   POST /api/viaggi/{viaggio_id}/update-completed")
    
    print("\n4. 🔍 VERIFICA FRONTEND")
    print("   Controllare se il frontend gestisce correttamente")
    print("   i viaggi non visibili")

def test_ripristino():
    """Test dell'endpoint di ripristino"""
    try:
        import requests
        
        print("\n🧪 TEST ENDPOINT RIPRISTINO")
        print("-" * 30)
        
        url = "http://localhost:8000/api/viaggi/43/ripristina"
        print(f"📤 Testando: {url}")
        
        try:
            response = requests.post(url, timeout=5)
            print(f"📥 Risposta: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Successo: {result.get('message', 'OK')}")
            else:
                print(f"❌ Errore: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print("⚠️ Server non in esecuzione (normale per test offline)")
        except Exception as e:
            print(f"❌ Errore richiesta: {e}")
            
    except Exception as e:
        print(f"❌ Errore test ripristino: {e}")

def main():
    """Esegue la diagnosi completa"""
    print("🔍 DIAGNOSI PROBLEMA 404 - VIAGGIO 43")
    print("=" * 60)
    print(f"🕐 Timestamp: {datetime.now()}")
    print()
    
    # Verifica stato viaggio
    success = check_viaggio_43()
    
    if success:
        # Suggerisci soluzioni
        suggest_solutions()
        
        # Test endpoint ripristino
        test_ripristino()
    
    print(f"\n🏁 Diagnosi completata")

if __name__ == "__main__":
    main()
