#!/usr/bin/env python3

import requests
import sys
import traceback

def debug_sof_download():
    print("🔍 DEBUG ERRORE DOWNLOAD SOF DOCX")
    print("=" * 50)
    
    # Test 1: Verifica se l'app è in esecuzione
    print("\n1️⃣ Test connessione app...")
    try:
        response = requests.get("http://localhost:8002", timeout=5)
        if response.status_code == 200:
            print("✅ App in esecuzione")
        else:
            print(f"❌ App risponde con status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ App non raggiungibile: {e}")
        return False
    
    # Test 2: Verifica dipendenze DOCX
    print("\n2️⃣ Test dipendenze DOCX...")
    try:
        from docx import Document
        from docx.shared import Inches, Pt
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        print("✅ Dipendenze DOCX importate correttamente")
    except ImportError as e:
        print(f"❌ Errore import dipendenze DOCX: {e}")
        return False
    
    # Test 3: Test creazione documento DOCX base
    print("\n3️⃣ Test creazione documento DOCX...")
    try:
        doc = Document()
        doc.add_heading('Test SOF Document', 0)
        doc.add_paragraph('Test di generazione documento.')
        
        test_file = "test_sof_debug.docx"
        doc.save(test_file)
        print(f"✅ Documento test creato: {test_file}")
        
        import os
        if os.path.exists(test_file):
            file_size = os.path.getsize(test_file)
            print(f"   Dimensione: {file_size} bytes")
            os.remove(test_file)
            print("✅ File test rimosso")
        
    except Exception as e:
        print(f"❌ Errore creazione documento: {e}")
        traceback.print_exc()
        return False
    
    # Test 4: Verifica directory sof_documents
    print("\n4️⃣ Test directory SOF...")
    try:
        import os
        sof_dir = "sof_documents"
        if not os.path.exists(sof_dir):
            os.makedirs(sof_dir)
            print(f"✅ Directory {sof_dir} creata")
        else:
            print(f"✅ Directory {sof_dir} esistente")
        
        # Lista file esistenti
        files = os.listdir(sof_dir)
        print(f"   File presenti: {len(files)}")
        for file in files[:5]:  # Mostra solo i primi 5
            print(f"   - {file}")
        if len(files) > 5:
            print(f"   ... e altri {len(files) - 5} file")
            
    except Exception as e:
        print(f"❌ Errore directory SOF: {e}")
        return False
    
    # Test 5: Verifica database e viaggi disponibili
    print("\n5️⃣ Test database viaggi...")
    try:
        import psycopg2
        conn = psycopg2.connect(
            host='localhost',
            database='AGENTE',
            user='re77',
            password='271077',
            connect_timeout=3
        )
        cursor = conn.cursor()
        
        # Trova viaggi disponibili
        cursor.execute("""
            SELECT v.id, v.viaggio, n."Nave", v.visibile
            FROM "VIAGGIO" v
            LEFT JOIN "NAVI" n ON v.nave_id = n.id
            ORDER BY v.id DESC
            LIMIT 5
        """)
        viaggi = cursor.fetchall()
        
        print(f"✅ Trovati {len(viaggi)} viaggi:")
        for viaggio in viaggi:
            print(f"   - ID: {viaggio[0]}, Codice: {viaggio[1]}, Nave: {viaggio[2]}, Visibile: {viaggio[3]}")
        
        conn.close()
        
        if viaggi:
            return viaggi[0][0]  # Restituisce ID del primo viaggio per test
        else:
            print("❌ Nessun viaggio trovato per test")
            return False
            
    except Exception as e:
        print(f"❌ Errore database: {e}")
        return False

def test_sof_endpoint(viaggio_id):
    """Test specifico dell'endpoint SOF"""
    print(f"\n6️⃣ Test endpoint SOF per viaggio {viaggio_id}...")
    
    try:
        # Test endpoint download SOF
        url = f"http://localhost:8002/operativo/sof/viaggio/{viaggio_id}/sof/download"
        print(f"   URL: {url}")
        
        # Nota: Questo richiederà autenticazione, ma possiamo vedere il tipo di errore
        response = requests.post(url, timeout=10)
        
        print(f"   Status Code: {response.status_code}")
        print(f"   Content-Type: {response.headers.get('content-type', 'N/A')}")
        
        if response.status_code == 401:
            print("   ℹ️  Errore 401 - Autenticazione richiesta (normale)")
            return True
        elif response.status_code == 500:
            print("   ❌ Errore 500 - Errore interno server")
            try:
                error_data = response.json()
                print(f"   Errore: {error_data.get('message', 'N/A')}")
            except:
                print(f"   Response text: {response.text[:200]}...")
            return False
        else:
            print(f"   ℹ️  Status inaspettato: {response.status_code}")
            return True
            
    except Exception as e:
        print(f"   ❌ Errore richiesta: {e}")
        return False

def test_generate_sof_function():
    """Test diretto della funzione generate_sof_docx"""
    print(f"\n7️⃣ Test funzione generate_sof_docx...")
    
    try:
        # Import della funzione dal main
        sys.path.append('.')
        from main import generate_sof_docx
        
        print("   ✅ Funzione importata")
        
        # Dati di test
        viaggio_id = 999
        viaggio_nome = "TEST_DEBUG"
        nave_nome = "NAVE_TEST"
        orari_data = ["PORTO_A", "2024-01-15T08:00", "2024-01-15T09:00", "2024-01-15T10:00", 
                     "2024-01-15T11:00", "8.5", "2024-01-15T12:00", "PORTO_B", 
                     "2024-01-15T13:00", "2024-01-15T14:00", "2024-01-15T15:00", 
                     "100", "50", "25"]
        import_data = [["PORTO1", "PORTO2", 10, "NCAR"]]
        export_data = [["PORTO2", "PORTO3", 5, "UVAN"]]
        
        print("   📄 Generazione documento...")
        docx_path = generate_sof_docx(viaggio_id, viaggio_nome, nave_nome, 
                                     orari_data, import_data, export_data)
        
        import os
        if os.path.exists(docx_path):
            file_size = os.path.getsize(docx_path)
            print(f"   ✅ SOF generato: {docx_path}")
            print(f"   Dimensione: {file_size} bytes")
            
            # Cleanup
            os.remove(docx_path)
            print("   ✅ File test rimosso")
            return True
        else:
            print("   ❌ File non generato")
            return False
            
    except Exception as e:
        print(f"   ❌ Errore funzione: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Avvio debug SOF download...")
    
    # Test preliminari
    viaggio_id = debug_sof_download()
    
    if viaggio_id:
        # Test endpoint
        endpoint_ok = test_sof_endpoint(viaggio_id)
        
        # Test funzione diretta
        function_ok = test_generate_sof_function()
        
        print(f"\n{'='*50}")
        print("📊 RISULTATI DEBUG:")
        print(f"   Endpoint SOF: {'✅' if endpoint_ok else '❌'}")
        print(f"   Funzione generate_sof_docx: {'✅' if function_ok else '❌'}")
        
        if endpoint_ok and function_ok:
            print("\n✅ SISTEMA SOF FUNZIONANTE")
            print("💡 L'errore potrebbe essere legato all'autenticazione o ai dati specifici")
        else:
            print("\n❌ PROBLEMI IDENTIFICATI")
            print("💡 Controlla i log dell'app per dettagli specifici")
    else:
        print("\n❌ Test preliminari falliti")
    
    print("\n🏁 Debug completato")
