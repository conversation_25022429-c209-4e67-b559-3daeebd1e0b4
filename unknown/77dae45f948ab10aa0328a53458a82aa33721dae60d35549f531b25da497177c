#!/usr/bin/env python3
"""
Script per correggere i temi degli utenti esistenti nel database.
Aggiorna tutti gli utenti che hanno tema_preferito = 'light' a 'maritime'.
"""

import sys
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_database_url():
    """Ottiene l'URL del database dalle variabili d'ambiente o usa il default PostgreSQL"""
    return os.getenv('DATABASE_URL', 'postgresql://re77:271077@localhost:5432/AGENTE')

def fix_user_themes():
    """Corregge i temi degli utenti nel database"""
    try:
        # Connessione al database
        database_url = get_database_url()
        engine = create_engine(database_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        with SessionLocal() as db:
            logger.info("🔧 Iniziando correzione temi utenti...")
            
            # Verifica utenti con tema 'light'
            result = db.execute(text("""
                SELECT id_user, "Nome", "Cognome", email, tema_preferito
                FROM "AGENTE"
                WHERE tema_preferito = 'light' OR tema_preferito IS NULL
            """))
            
            users_to_fix = result.fetchall()
            logger.info(f"📊 Trovati {len(users_to_fix)} utenti da correggere")
            
            if not users_to_fix:
                logger.info("✅ Nessun utente da correggere")
                return True
            
            # Mostra utenti da correggere
            for user in users_to_fix:
                logger.info(f"   - {user[1]} {user[2]} ({user[3]}) - Tema attuale: {user[4]}")
            
            # Conferma prima di procedere
            if len(sys.argv) > 1 and sys.argv[1] == '--auto':
                proceed = True
            else:
                response = input(f"\n🤔 Vuoi aggiornare {len(users_to_fix)} utenti da 'light' a 'maritime'? (s/N): ")
                proceed = response.lower() in ['s', 'si', 'y', 'yes']
            
            if not proceed:
                logger.info("❌ Operazione annullata dall'utente")
                return False
            
            # Aggiorna i temi
            logger.info("🔄 Aggiornamento temi in corso...")
            
            result = db.execute(text("""
                UPDATE "AGENTE" 
                SET tema_preferito = 'maritime' 
                WHERE tema_preferito = 'light' OR tema_preferito IS NULL
            """))
            
            rows_affected = result.rowcount if hasattr(result, 'rowcount') else 0
            db.commit()
            
            logger.info(f"✅ Aggiornati {rows_affected} utenti con successo!")
            
            # Verifica finale
            result = db.execute(text("""
                SELECT tema_preferito, COUNT(*) as count
                FROM "AGENTE" 
                GROUP BY tema_preferito
                ORDER BY tema_preferito
            """))
            
            theme_stats = result.fetchall()
            logger.info("📊 Statistiche temi dopo l'aggiornamento:")
            for theme, count in theme_stats:
                logger.info(f"   - {theme}: {count} utenti")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Errore durante la correzione: {str(e)}")
        return False

def verify_theme_consistency():
    """Verifica la consistenza dei temi nel sistema"""
    try:
        database_url = get_database_url()
        engine = create_engine(database_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        with SessionLocal() as db:
            logger.info("🔍 Verifica consistenza temi...")
            
            # Statistiche temi
            result = db.execute(text("""
                SELECT tema_preferito, COUNT(*) as count
                FROM "AGENTE" 
                GROUP BY tema_preferito
                ORDER BY tema_preferito
            """))
            
            theme_stats = result.fetchall()
            logger.info("📊 Statistiche temi attuali:")
            for theme, count in theme_stats:
                logger.info(f"   - {theme}: {count} utenti")
            
            # Verifica temi non validi
            result = db.execute(text("""
                SELECT id_user, "Nome", "Cognome", email, tema_preferito
                FROM "AGENTE"
                WHERE tema_preferito NOT IN ('light', 'dark', 'maritime')
                   OR tema_preferito IS NULL
            """))
            
            invalid_themes = result.fetchall()
            if invalid_themes:
                logger.warning(f"⚠️ Trovati {len(invalid_themes)} utenti con temi non validi:")
                for user in invalid_themes:
                    logger.warning(f"   - {user[1]} {user[2]} ({user[3]}) - Tema: {user[4]}")
            else:
                logger.info("✅ Tutti i temi sono validi")
            
            return len(invalid_themes) == 0
            
    except Exception as e:
        logger.error(f"❌ Errore durante la verifica: {str(e)}")
        return False

def main():
    """Funzione principale"""
    logger.info("🎨 SNIP - Correzione Temi Utenti")
    logger.info("=" * 50)
    
    # Verifica stato attuale
    if not verify_theme_consistency():
        logger.info("\n🔧 Procedendo con la correzione...")
        if fix_user_themes():
            logger.info("\n🔍 Verifica finale...")
            verify_theme_consistency()
        else:
            logger.error("❌ Correzione fallita")
            sys.exit(1)
    else:
        logger.info("✅ Tutti i temi sono già corretti!")
    
    logger.info("\n🎉 Operazione completata!")

if __name__ == "__main__":
    main()
