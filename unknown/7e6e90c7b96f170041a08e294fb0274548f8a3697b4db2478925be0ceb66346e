{"viaggio": {"codice_viaggio": "TEST001", "nome_nave": "NAVE_TEST", "porto_gestione": "Porto Test", "data_arrivo": "2025-06-11T08:00:00", "data_partenza": "2025-06-11T18:00:00"}, "metadata": {"filename": "NAVE_TEST_11062025.json", "archiviato_il": "2025-06-11T22:00:00", "archiviato_da": "<EMAIL>"}, "orari": [{"porto": "Porto Test", "arrivo": "2025-06-11T08:00:00", "partenza": "2025-06-11T18:00:00", "note": "Test di caricamento"}], "import_data": {"total_records": 2, "records": [{"pol": "PORTO_A", "pod": "PORTO_B", "qt": 100, "type": "Container", "created_at": "2025-06-11T10:00:00"}, {"pol": "PORTO_C", "pod": "PORTO_D", "qt": 50, "type": "Trailer", "created_at": "2025-06-11T11:00:00"}]}, "export_data": {"total_records": 1, "records": [{"pol": "PORTO_B", "pod": "PORTO_E", "qt": 75, "type": "Container", "created_at": "2025-06-11T12:00:00"}]}}