#!/usr/bin/env python3
"""
Migrazione per aggiungere i campi porto_arrivo e porto_destinazione alla tabella VIAGGIO
"""

import sys
import os
from sqlalchemy import text
from database import SessionLocal
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_porti_to_viaggio():
    """Aggiunge i campi porto_arrivo e porto_destinazione alla tabella VIAGGIO"""
    db = SessionLocal()
    
    try:
        logger.info("🚢 Inizio migrazione campi porti nella tabella VIAGGIO...")
        
        # Verifica se le colonne esistono già
        result = db.execute(text("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'VIAGGIO' AND column_name IN ('porto_arrivo', 'porto_destinazione')
        """)).fetchall()

        existing_columns = [row[0] for row in result]
        
        # Aggiungi porto_arrivo se non esiste
        if 'porto_arrivo' not in existing_columns:
            logger.info("📝 Aggiunta colonna porto_arrivo alla tabella VIAGGIO...")
            db.execute(text("""
                ALTER TABLE "VIAGGIO"
                ADD COLUMN porto_arrivo VARCHAR(10) REFERENCES "ATLAS"("ID_COD")
            """))
            logger.info("✅ Colonna porto_arrivo aggiunta")
        else:
            logger.info("✅ Colonna porto_arrivo già presente")

        # Aggiungi porto_destinazione se non esiste
        if 'porto_destinazione' not in existing_columns:
            logger.info("📝 Aggiunta colonna porto_destinazione alla tabella VIAGGIO...")
            db.execute(text("""
                ALTER TABLE "VIAGGIO"
                ADD COLUMN porto_destinazione VARCHAR(10) REFERENCES "ATLAS"("ID_COD")
            """))
            logger.info("✅ Colonna porto_destinazione aggiunta")
        else:
            logger.info("✅ Colonna porto_destinazione già presente")

        # Crea indici per performance
        try:
            db.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_viaggio_porto_arrivo
                ON "VIAGGIO" (porto_arrivo)
            """))
            logger.info("📊 Indice idx_viaggio_porto_arrivo creato")
        except Exception as e:
            logger.warning(f"⚠️ Errore creazione indice porto_arrivo: {e}")

        try:
            db.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_viaggio_porto_destinazione
                ON "VIAGGIO" (porto_destinazione)
            """))
            logger.info("📊 Indice idx_viaggio_porto_destinazione creato")
        except Exception as e:
            logger.warning(f"⚠️ Errore creazione indice porto_destinazione: {e}")

        db.commit()
        logger.info("✅ Migrazione completata con successo")

        # Verifica finale
        logger.info("🔍 Verifica finale della migrazione...")
        
        # Controlla struttura tabella
        result = db.execute(text("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns
            WHERE table_name = 'VIAGGIO'
            ORDER BY ordinal_position
        """)).fetchall()
        
        logger.info("📋 Struttura attuale tabella VIAGGIO:")
        for row in result:
            logger.info(f"   - {row[0]} ({row[1]}) - Nullable: {row[2]}")
        
        # Controlla foreign keys
        result = db.execute(text("""
            SELECT 
                tc.constraint_name, 
                tc.table_name, 
                kcu.column_name, 
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name 
            FROM 
                information_schema.table_constraints AS tc 
                JOIN information_schema.key_column_usage AS kcu
                  ON tc.constraint_name = kcu.constraint_name
                  AND tc.table_schema = kcu.table_schema
                JOIN information_schema.constraint_column_usage AS ccu
                  ON ccu.constraint_name = tc.constraint_name
                  AND ccu.table_schema = tc.table_schema
            WHERE tc.constraint_type = 'FOREIGN KEY' 
                AND tc.table_name='VIAGGIO'
                AND kcu.column_name IN ('porto_arrivo', 'porto_destinazione')
        """)).fetchall()
        
        if result:
            logger.info("🔗 Foreign keys per porti:")
            for row in result:
                logger.info(f"   - {row[2]} -> {row[3]}.{row[4]}")
        else:
            logger.info("🔗 Nessun foreign key trovato per i porti")

        # Verifica indici
        result = db.execute(text("""
            SELECT indexname, indexdef
            FROM pg_indexes
            WHERE tablename = 'VIAGGIO' AND indexname LIKE '%porto%'
        """)).fetchall()
        
        if result:
            logger.info("📊 Indici relativi ai porti:")
            for row in result:
                logger.info(f"   - {row[0]}: {row[1]}")
        else:
            logger.info("📊 Nessun indice relativo ai porti trovato")

    except Exception as e:
        logger.error(f"❌ Errore durante la migrazione: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def main():
    """Esegue la migrazione completa"""
    logger.info("🚀 Inizio migrazione campi porti per tabella VIAGGIO...")
    
    try:
        # Aggiungi campi porti alla tabella VIAGGIO
        add_porti_to_viaggio()
        
        logger.info("🎉 Migrazione completata con successo!")
        logger.info("")
        logger.info("📋 RIEPILOGO:")
        logger.info("   ✅ Colonna porto_arrivo aggiunta alla tabella VIAGGIO")
        logger.info("   ✅ Colonna porto_destinazione aggiunta alla tabella VIAGGIO")
        logger.info("   ✅ Foreign keys verso tabella ATLAS configurati")
        logger.info("   ✅ Indici per performance creati")
        logger.info("")
        logger.info("🎛️ PROSSIMI PASSI:")
        logger.info("   1. Aggiornare la modale 'Aggiungi Nuovo Viaggio'")
        logger.info("   2. Modificare le API per gestire i nuovi campi")
        logger.info("   3. Aggiornare la logica di pre-popolamento SOF")
        logger.info("   4. Testare il flusso completo")
        logger.info("")
        logger.info("🔄 FUNZIONALITÀ:")
        logger.info("   - I porti selezionati nel viaggio saranno automaticamente")
        logger.info("     popolati nella pagina SOF tab orari")
        logger.info("   - Collegamento diretto tra VIAGGIO e ATLAS (porti)")
        logger.info("   - Miglioramento UX per evitare inserimenti duplicati")
        
    except Exception as e:
        logger.error(f"💥 Migrazione fallita: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
