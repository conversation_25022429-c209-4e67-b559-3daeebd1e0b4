#!/usr/bin/env python3

import psycopg2
import sys

def analyze_missing_fields():
    print("🔍 ANALISI COMPLETA CAMPI MANCANTI NEL DATABASE")
    print("=" * 60)
    
    try:
        # Connessione al database
        conn = psycopg2.connect(
            host='localhost',
            database='AGENTE',
            user='re77',
            password='271077',
            connect_timeout=5
        )
        cursor = conn.cursor()
        
        # Definizione struttura prevista dai modelli
        expected_structure = {
            "AGENTE": {
                "id_user": "integer PRIMARY KEY",
                "Nome": "character varying",
                "Cognome": "character varying", 
                "email": "character varying UNIQUE",
                "password": "character varying",
                "reparto": "USER-DEFINED (enum)",
                "ruolo": "USER-DEFINED (enum)",
                "visibile": "character varying DEFAULT 'no'",
                "tema_preferito": "USER-DEFINED (enum) DEFAULT 'maritime'"
            },
            "NAVI": {
                "id": "integer PRIMARY KEY",
                "Nave": "character varying",
                "Codice_Nave": "character varying",
                "Prefisso_viaggio": "character varying",
                "Agemar": "numeric(10,2)",
                "armatore_id": "integer FK->ARMATORE.id"
            },
            "VIAGGIO": {
                "id": "integer PRIMARY KEY",
                "porto_gestione_id": "integer FK->PORTI_GESTIONE.id_porto",
                "nave_id": "integer FK->NAVI.id",
                "viaggio": "character varying",
                "data_arrivo": "date",
                "data_partenza": "date",
                "eta_originale": "date",
                "porto_arrivo": "character varying(10)",
                "porto_destinazione": "character varying(10)",
                "visibile": "character varying DEFAULT 'si'",
                "archiviato": "character varying(2) DEFAULT 'no'"
            },
            "ORARI": {
                "id": "integer PRIMARY KEY",
                "viaggio_id": "integer FK->VIAGGIO.id",
                "porto_arrivo": "character varying",
                "sbe": "timestamp without time zone",
                "pilota_arrivo": "timestamp without time zone",
                "all_fast": "timestamp without time zone",
                "tug_arrivo": "integer",
                "draft": "numeric(10,2)",
                "soc": "timestamp without time zone",
                "porto_di_destinazione": "character varying",
                "pilota_partenza": "timestamp without time zone",
                "tug_partenza": "integer",
                "foc": "timestamp without time zone",
                "fo": "numeric(10,2)",
                "do": "numeric(10,2)",
                "lo": "numeric(10,2)"
            },
            "IMPORT": {
                "id": "integer PRIMARY KEY",
                "viaggio_id": "integer FK->VIAGGIO.id",
                "pol": "character varying",
                "pod": "character varying",
                "qt": "numeric(10,2)",
                "type": "character varying",
                "created_at": "timestamp with time zone",
                "updated_at": "timestamp with time zone"
            },
            "EXPORT": {
                "id": "integer PRIMARY KEY",
                "viaggio_id": "integer FK->VIAGGIO.id",
                "pol": "character varying",
                "pod": "character varying",
                "qt": "numeric(10,2)",
                "type": "character varying",
                "created_at": "timestamp with time zone",
                "updated_at": "timestamp with time zone"
            },
            "ARMATORE": {
                "id": "integer PRIMARY KEY",
                "Nome_Armatore": "character varying"
            }
        }
        
        print("\n📊 CONFRONTO STRUTTURA TABELLE:")
        print("=" * 60)
        
        missing_fields_found = False
        
        for table_name, expected_fields in expected_structure.items():
            print(f"\n🔍 TABELLA: {table_name}")
            print("-" * 40)
            
            # Verifica se la tabella esiste
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = %s
                );
            """, (table_name,))
            
            table_exists = cursor.fetchone()[0]
            
            if not table_exists:
                print(f"❌ TABELLA {table_name} NON ESISTE!")
                missing_fields_found = True
                continue
            
            # Ottieni struttura attuale
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = %s AND table_schema = 'public'
                ORDER BY ordinal_position;
            """, (table_name,))
            
            actual_columns = cursor.fetchall()
            actual_fields = {col[0]: col[1] for col in actual_columns}
            
            print(f"✅ Tabella esistente con {len(actual_fields)} campi")
            
            # Confronta campi
            for field_name, expected_type in expected_fields.items():
                if field_name in actual_fields:
                    actual_type = actual_fields[field_name]
                    print(f"  ✅ {field_name}: {actual_type}")
                else:
                    print(f"  ❌ MANCANTE: {field_name} ({expected_type})")
                    missing_fields_found = True
            
            # Campi extra non previsti
            extra_fields = set(actual_fields.keys()) - set(expected_fields.keys())
            if extra_fields:
                print(f"  ℹ️  Campi extra: {', '.join(extra_fields)}")
        
        # Verifica tabelle di sistema
        print(f"\n🔧 TABELLE DI SISTEMA:")
        print("-" * 40)
        
        system_tables = [
            "SYSTEM_CONFIG", "AUDIT_LOG", "USER_SESSIONS", 
            "DEPARTMENT_NOTIFICATIONS", "USER_NOTIFICATION_READ", "SYSTEM_STATS"
        ]
        
        for table in system_tables:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = %s
                );
            """, (table,))
            
            exists = cursor.fetchone()[0]
            if exists:
                cursor.execute(f'SELECT COUNT(*) FROM "{table}"')
                count = cursor.fetchone()[0]
                print(f"  ✅ {table}: {count} record")
            else:
                print(f"  ❌ {table}: NON ESISTE")
                missing_fields_found = True
        
        # Verifica foreign keys
        print(f"\n🔗 VERIFICA FOREIGN KEYS:")
        print("-" * 40)
        
        fk_checks = [
            ("NAVI", "armatore_id", "ARMATORE", "id"),
            ("VIAGGIO", "porto_gestione_id", "PORTI_GESTIONE", "id_porto"),
            ("VIAGGIO", "nave_id", "NAVI", "id"),
            ("ORARI", "viaggio_id", "VIAGGIO", "id"),
            ("IMPORT", "viaggio_id", "VIAGGIO", "id"),
            ("EXPORT", "viaggio_id", "VIAGGIO", "id")
        ]
        
        for table, fk_column, ref_table, ref_column in fk_checks:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT 1 FROM information_schema.table_constraints tc
                    JOIN information_schema.key_column_usage kcu 
                        ON tc.constraint_name = kcu.constraint_name
                    JOIN information_schema.constraint_column_usage ccu 
                        ON ccu.constraint_name = tc.constraint_name
                    WHERE tc.constraint_type = 'FOREIGN KEY'
                        AND tc.table_name = %s
                        AND kcu.column_name = %s
                        AND ccu.table_name = %s
                        AND ccu.column_name = %s
                );
            """, (table, fk_column, ref_table, ref_column))
            
            fk_exists = cursor.fetchone()[0]
            if fk_exists:
                print(f"  ✅ {table}.{fk_column} -> {ref_table}.{ref_column}")
            else:
                print(f"  ❌ MANCANTE: {table}.{fk_column} -> {ref_table}.{ref_column}")
                missing_fields_found = True
        
        conn.close()
        
        print(f"\n{'='*60}")
        if missing_fields_found:
            print("❌ TROVATI CAMPI/TABELLE MANCANTI!")
            print("💡 Suggerimento: Esegui le migrazioni necessarie")
        else:
            print("✅ STRUTTURA DATABASE COMPLETA!")
            print("🎉 Tutti i campi previsti sono presenti")
        
        return not missing_fields_found
        
    except Exception as e:
        print(f"❌ Errore durante l'analisi: {e}")
        return False

if __name__ == "__main__":
    success = analyze_missing_fields()
    sys.exit(0 if success else 1)
