#!/usr/bin/env python3
"""
Script per cambiare il tema dell'utente a 'dark' per testare il problema
"""

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import os

def main():
    database_url = os.getenv('DATABASE_URL', 'postgresql://re77:271077@localhost:5432/AGENTE')
    engine = create_engine(database_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    with SessionLocal() as db:
        print('Cambio tema utente a dark per test...')
        result = db.execute(text('UPDATE "AGENTE" SET tema_preferito = \'dark\' WHERE email = \'<EMAIL>\''))
        rows_affected = result.rowcount if hasattr(result, 'rowcount') else 0
        db.commit()
        print(f'Aggiornato {rows_affected} utente')
        
        # Verifica
        result = db.execute(text('SELECT email, tema_preferito FROM "AGENTE" WHERE email = \'<EMAIL>\''))
        user = result.fetchone()
        if user:
            print(f'Utente {user[0]} ora ha tema: {user[1]}')
        else:
            print('Utente non trovato')

if __name__ == "__main__":
    main()
