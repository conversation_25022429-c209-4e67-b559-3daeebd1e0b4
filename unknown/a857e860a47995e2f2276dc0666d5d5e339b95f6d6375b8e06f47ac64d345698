// JavaScript per la gestione dei SOF realizzati

// Variabili globali
let viaggioIdDaRipristinare = null;
let viaggioIdPerNotifica = null;
let viaggioIdDaArchiviare = null;

// Inizializzazione quando il DOM è caricato
document.addEventListener('DOMContentLoaded', function() {
    console.log('SOF Realizzati - JavaScript caricato');

    // Inizializza i modali
    inizializzaModali();

    // Aggiungi animazioni alle card
    animaStatistiche();
});

// Funzione per inizializzare i modali
function inizializzaModali() {
    // Modal ripristina viaggio
    const ripristinaModal = document.getElementById('ripristinaViaggioModal');
    if (ripristinaModal) {
        ripristinaModal.addEventListener('hidden.bs.modal', function() {
            viaggioIdDaRipristinare = null;
        });
    }

    // Modal invia notifica SOF
    const notificaModal = document.getElementById('inviaNotificaSOFModal');
    if (notificaModal) {
        notificaModal.addEventListener('hidden.bs.modal', function() {
            viaggioIdPerNotifica = null;
        });
    }

    // Modal archivia SOF
    const archiviaModal = document.getElementById('archiviaSOFModal');
    if (archiviaModal) {
        archiviaModal.addEventListener('hidden.bs.modal', function() {
            viaggioIdDaArchiviare = null;
        });
    }

    // Bottone conferma ripristino
    const confermaRipristinaBtn = document.getElementById('confermaRipristinaViaggio');
    if (confermaRipristinaBtn) {
        confermaRipristinaBtn.addEventListener('click', function() {
            if (viaggioIdDaRipristinare) {
                eseguiRipristinoViaggio(viaggioIdDaRipristinare);
            }
        });
    }

    // Bottone conferma invio notifica
    const confermaNotificaBtn = document.getElementById('confermaInviaNotifica');
    if (confermaNotificaBtn) {
        confermaNotificaBtn.addEventListener('click', function() {
            if (viaggioIdPerNotifica) {
                eseguiInvioNotificaSOF(viaggioIdPerNotifica);
            }
        });
    }

    // Bottone conferma archiviazione
    const confermaArchiviaBtn = document.getElementById('confermaArchiviaSOF');
    if (confermaArchiviaBtn) {
        confermaArchiviaBtn.addEventListener('click', function() {
            if (viaggioIdDaArchiviare) {
                eseguiArchiviazioneSOF(viaggioIdDaArchiviare);
            }
        });
    }
}

// Funzione per animare le statistiche
function animaStatistiche() {
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.5s ease';

            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        }, index * 100);
    });
}

// Funzione per visualizzare i dettagli di un viaggio
function visualizzaDettagli(viaggioId) {
    console.log('Visualizza dettagli viaggio:', viaggioId);

    // Reindirizza alla pagina di dettaglio del viaggio
    window.location.href = `/operativo/sof/viaggio/${viaggioId}`;
}

// Funzione per inviare notifica SOF
function inviaNotificaSOF(viaggioId, nomeViaggio) {
    console.log('Invia notifica SOF:', viaggioId, nomeViaggio);

    viaggioIdPerNotifica = viaggioId;

    // Aggiorna il nome del viaggio nel modal
    const nomeElement = document.getElementById('notifica_viaggio_nome');
    if (nomeElement) {
        nomeElement.textContent = nomeViaggio || `ID ${viaggioId}`;
    }

    // Mostra il modal
    const modal = new bootstrap.Modal(document.getElementById('inviaNotificaSOFModal'));
    modal.show();
}

// Funzione per ripristinare un viaggio
function ripristinaViaggio(viaggioId, nomeViaggio) {
    console.log('Ripristina viaggio:', viaggioId, nomeViaggio);

    viaggioIdDaRipristinare = viaggioId;

    // Aggiorna il nome del viaggio nel modal
    const nomeElement = document.getElementById('ripristina_viaggio_nome');
    if (nomeElement) {
        nomeElement.textContent = nomeViaggio || `ID ${viaggioId}`;
    }

    // Mostra il modal
    const modal = new bootstrap.Modal(document.getElementById('ripristinaViaggioModal'));
    modal.show();
}

// Funzione per archiviare un SOF
function archiviaSOF(viaggioId, nomeViaggio) {
    console.log('Archivia SOF:', viaggioId, nomeViaggio);

    // Verifica che i parametri siano validi
    if (!viaggioId) {
        console.error('Errore: viaggioId non valido:', viaggioId);
        alert('Errore: ID viaggio non valido');
        return;
    }

    viaggioIdDaArchiviare = viaggioId;

    // Aggiorna il nome del viaggio nel modal
    const nomeElement = document.getElementById('archivia_viaggio_nome');
    if (nomeElement) {
        nomeElement.textContent = nomeViaggio || `ID ${viaggioId}`;
    }

    // Mostra il modal
    const modalElement = document.getElementById('archiviaSOFModal');
    if (modalElement) {
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
    } else {
        console.error('Modal archiviaSOFModal non trovato!');
    }
}

// Rendi le funzioni globalmente accessibili
window.archiviaSOF = archiviaSOF;
window.visualizzaDettagli = visualizzaDettagli;
window.inviaNotificaSOF = inviaNotificaSOF;
window.ripristinaViaggio = ripristinaViaggio;

// Funzione per eseguire l'invio della notifica SOF
function eseguiInvioNotificaSOF(viaggioId) {
    console.log('Esegui invio notifica SOF:', viaggioId);

    // Mostra loading
    const btn = document.getElementById('confermaInviaNotifica');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Invio...';
    btn.disabled = true;

    // Chiamata API per inviare la notifica SOF
    fetch(`/api/sof/${viaggioId}/send-notification`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            mostraSuccesso('Notifica inviata con successo!', `Email di notifica SOF inviata per ${data.data.viaggio_nome} (${data.data.nave})`);

            // Chiudi il modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('inviaNotificaSOFModal'));
            modal.hide();
        } else {
            mostraErrore('Errore durante l\'invio', data.message || 'Si è verificato un errore durante l\'invio della notifica.');
        }
    })
    .catch(error => {
        console.error('Errore invio notifica SOF:', error);
        mostraErrore('Errore di connessione', 'Si è verificato un errore di connessione durante l\'invio della notifica.');
    })
    .finally(() => {
        // Ripristina il bottone
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// Funzione per eseguire il ripristino del viaggio
function eseguiRipristinoViaggio(viaggioId) {
    console.log('Esegui ripristino viaggio:', viaggioId);

    // Mostra loading
    const btn = document.getElementById('confermaRipristinaViaggio');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Ripristino...';
    btn.disabled = true;

    // Chiamata API per ripristinare il viaggio (imposta visibile = 'si')
    fetch(`/api/viaggi/${viaggioId}/ripristina`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            mostraSuccesso('Viaggio ripristinato con successo!', 'Il viaggio è stato ripristinato e tornerà nella lista "SOF da Realizzare".');

            // Chiudi il modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('ripristinaViaggioModal'));
            modal.hide();

            // Ricarica la pagina dopo un breve delay
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            mostraErrore('Errore durante il ripristino', data.message || 'Si è verificato un errore durante il ripristino del viaggio.');
        }
    })
    .catch(error => {
        console.error('Errore ripristino viaggio:', error);
        mostraErrore('Errore di connessione', 'Si è verificato un errore di connessione durante il ripristino.');
    })
    .finally(() => {
        // Ripristina il bottone
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// Funzione per eseguire l'archiviazione del SOF
function eseguiArchiviazioneSOF(viaggioId) {
    console.log('Esegui archiviazione SOF:', viaggioId);

    // Mostra loading
    const btn = document.getElementById('confermaArchiviaSOF');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Archiviazione in corso...';
    btn.disabled = true;

    // Chiamata API per archiviare il SOF
    fetch(`/api/sof/${viaggioId}/archivia`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            mostraSuccesso(
                'SOF archiviato con successo!',
                `Il viaggio "${data.data?.viaggio || 'N/A'}" è stato archiviato e sarà visibile nella sezione "SOF Archiviati".`
            );

            // Chiudi il modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('archiviaSOFModal'));
            modal.hide();

            // Ricarica la pagina dopo un breve delay
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            mostraErrore('Errore durante l\'archiviazione', data.message || 'Si è verificato un errore durante l\'archiviazione del SOF.');
        }
    })
    .catch(error => {
        console.error('Errore archiviazione SOF:', error);
        mostraErrore('Errore di connessione', 'Si è verificato un errore di connessione durante l\'archiviazione.');
    })
    .finally(() => {
        // Ripristina il bottone
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// 🎨 Funzioni di utilità per i messaggi SPETTACOLARI
function mostraSuccesso(titolo, messaggio) {
    // Usa il sistema SNIP Messages se disponibile
    if (typeof window.snipMessages !== 'undefined') {
        return window.snipMessages.success(titolo, messaggio);
    }

    // Fallback: Toast Bootstrap
    const toastHtml = `
        <div class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <strong>${titolo}</strong><br>${messaggio}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    mostraToast(toastHtml);
}

function mostraErrore(titolo, messaggio) {
    // Crea un toast di errore
    const toastHtml = `
        <div class="toast align-items-center text-white bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <strong>${titolo}</strong><br>${messaggio}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    mostraToast(toastHtml);
}

function mostraInfo(titolo, messaggio) {
    // Crea un toast informativo
    const toastHtml = `
        <div class="toast align-items-center text-white bg-info border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <strong>${titolo}</strong><br>${messaggio}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    mostraToast(toastHtml);
}

function mostraToast(toastHtml) {
    // Crea il container per i toast se non esiste
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    // Aggiungi il toast
    const toastElement = document.createElement('div');
    toastElement.innerHTML = toastHtml;
    toastContainer.appendChild(toastElement.firstElementChild);

    // Inizializza e mostra il toast
    const toast = new bootstrap.Toast(toastContainer.lastElementChild, {
        autohide: true,
        delay: 5000
    });
    toast.show();

    // Rimuovi il toast dal DOM dopo che è stato nascosto
    toastContainer.lastElementChild.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

// ===== FUNZIONI ARCHIVIAZIONE DI MASSA =====

function apriModalArchiviazioneMassa() {
    // Apri il modal
    const modal = new bootstrap.Modal(document.getElementById('archiviazioneMassaModal'));
    modal.show();

    // Carica gli anni disponibili
    caricaAnniDisponibili();
}

function caricaAnniDisponibili() {
    fetch('/api/sof/realizzati/anni-disponibili')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const selectAnno = document.getElementById('filtroAnno');
            selectAnno.innerHTML = '<option value="">-- Seleziona Anno --</option>';

            data.anni.forEach(anno => {
                const option = document.createElement('option');
                option.value = anno;
                option.textContent = anno;
                selectAnno.appendChild(option);
            });

            console.log('📅 Anni disponibili caricati:', data.anni);
        } else {
            mostraErrore('Errore', data.message || 'Errore nel caricamento degli anni');
        }
    })
    .catch(error => {
        console.error('Errore caricamento anni:', error);
        mostraErrore('Errore di rete', 'Impossibile caricare gli anni disponibili');
    });
}

function caricaViaggiPerPeriodo() {
    const mese = document.getElementById('filtroMese').value;
    const anno = document.getElementById('filtroAnno').value;
    const risultatiDiv = document.getElementById('risultatiArchiviazione');
    const btnArchivia = document.getElementById('btnArchiviaSelezionati');

    // Reset pulsante
    btnArchivia.disabled = true;

    if (!mese || !anno) {
        risultatiDiv.innerHTML = `
            <div class="text-center text-muted py-5">
                <i class="fas fa-search fa-3x mb-3"></i>
                <h5>Seleziona Mese e Anno</h5>
                <p>Scegli il periodo per visualizzare i viaggi disponibili per l'archiviazione</p>
            </div>
        `;
        return;
    }

    // Mostra loading
    risultatiDiv.innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Caricamento...</span>
            </div>
            <p class="mt-3">Caricamento viaggi per ${mese}/${anno}...</p>
        </div>
    `;

    fetch(`/api/sof/realizzati/viaggi-per-periodo?mese=${mese}&anno=${anno}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            mostraViaggiPerArchiviazione(data.viaggi, data.periodo);
        } else {
            risultatiDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Errore:</strong> ${data.message}
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Errore caricamento viaggi:', error);
        risultatiDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Errore di rete:</strong> Impossibile caricare i viaggi
            </div>
        `;
    });
}

function mostraViaggiPerArchiviazione(viaggi, periodo) {
    const risultatiDiv = document.getElementById('risultatiArchiviazione');
    const btnArchivia = document.getElementById('btnArchiviaSelezionati');

    if (viaggi.length === 0) {
        risultatiDiv.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Nessun viaggio trovato</strong><br>
                Non ci sono viaggi realizzati per ${periodo.mese_nome} ${periodo.anno}
            </div>
        `;
        btnArchivia.disabled = true;
        return;
    }

    let html = `
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            <strong>Trovati ${viaggi.length} viaggi per ${periodo.mese_nome} ${periodo.anno}</strong><br>
            Seleziona i viaggi che vuoi archiviare
        </div>

        <div class="mb-3">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="selezionaTutti" onchange="toggleSelezionaTutti()">
                <label class="form-check-label fw-bold" for="selezionaTutti">
                    <i class="fas fa-check-double me-2"></i>Seleziona/Deseleziona Tutti
                </label>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-primary">
                    <tr>
                        <th width="50">
                            <i class="fas fa-check-square"></i>
                        </th>
                        <th><i class="fas fa-barcode me-2"></i>Codice Viaggio</th>
                        <th><i class="fas fa-ship me-2"></i>Nome Nave</th>
                        <th><i class="fas fa-anchor me-2"></i>Porto</th>
                        <th><i class="fas fa-calendar-alt me-2"></i>Data Arrivo</th>
                        <th><i class="fas fa-calendar-alt me-2"></i>Data Partenza</th>
                    </tr>
                </thead>
                <tbody>
    `;

    viaggi.forEach(viaggio => {
        html += `
            <tr>
                <td>
                    <div class="form-check">
                        <input class="form-check-input viaggio-checkbox" type="checkbox"
                               value="${viaggio.id}" id="viaggio_${viaggio.id}"
                               onchange="aggiornaStatoPulsanteArchivia()">
                    </div>
                </td>
                <td>
                    <span class="badge bg-success">${viaggio.viaggio}</span>
                </td>
                <td>
                    <strong>${viaggio.nome_nave}</strong>
                </td>
                <td>
                    <span class="badge bg-primary">
                        <i class="fas fa-anchor me-1"></i>${viaggio.porto_gestione}
                    </span>
                </td>
                <td>
                    <span class="text-success fw-bold">
                        <i class="fas fa-calendar-check me-1"></i>${viaggio.data_arrivo || 'N/A'}
                    </span>
                </td>
                <td>
                    <span class="text-warning fw-bold">
                        <i class="fas fa-calendar-check me-1"></i>${viaggio.data_partenza || 'N/A'}
                    </span>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    risultatiDiv.innerHTML = html;
    btnArchivia.disabled = true; // Inizialmente disabilitato
}

function toggleSelezionaTutti() {
    const selezionaTuttiCheckbox = document.getElementById('selezionaTutti');
    const checkboxes = document.querySelectorAll('.viaggio-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selezionaTuttiCheckbox.checked;
    });

    aggiornaStatoPulsanteArchivia();
}

function aggiornaStatoPulsanteArchivia() {
    const checkboxes = document.querySelectorAll('.viaggio-checkbox:checked');
    const btnArchivia = document.getElementById('btnArchiviaSelezionati');
    const selezionaTuttiCheckbox = document.getElementById('selezionaTutti');

    // Aggiorna pulsante archivia
    btnArchivia.disabled = checkboxes.length === 0;

    if (checkboxes.length > 0) {
        btnArchivia.innerHTML = `<i class="fas fa-archive me-1"></i>Archivia ${checkboxes.length} Selezionati`;
    } else {
        btnArchivia.innerHTML = `<i class="fas fa-archive me-1"></i>Archivia Selezionati`;
    }

    // Aggiorna stato checkbox "Seleziona Tutti"
    const tuttiCheckboxes = document.querySelectorAll('.viaggio-checkbox');
    if (tuttiCheckboxes.length > 0) {
        if (checkboxes.length === tuttiCheckboxes.length) {
            selezionaTuttiCheckbox.checked = true;
            selezionaTuttiCheckbox.indeterminate = false;
        } else if (checkboxes.length > 0) {
            selezionaTuttiCheckbox.checked = false;
            selezionaTuttiCheckbox.indeterminate = true;
        } else {
            selezionaTuttiCheckbox.checked = false;
            selezionaTuttiCheckbox.indeterminate = false;
        }
    }
}

// Variabile globale per memorizzare i viaggi da archiviare
let viaggiDaArchiviare = [];

function mostraConfermaArchiviazione() {
    const checkboxes = document.querySelectorAll('.viaggio-checkbox:checked');
    viaggiDaArchiviare = Array.from(checkboxes).map(cb => parseInt(cb.value));

    if (viaggiDaArchiviare.length === 0) {
        mostraErrore('Nessuna selezione', 'Seleziona almeno un viaggio da archiviare');
        return;
    }

    // Aggiorna il numero nel modal di conferma
    document.getElementById('numeroViaggiDaArchiviare').textContent = viaggiDaArchiviare.length;

    // Mostra il modal di conferma
    const confermaModal = new bootstrap.Modal(document.getElementById('confermaArchiviazioneModal'));
    confermaModal.show();
}

function eseguiArchiviazioneConfermata() {
    // Chiudi il modal di conferma
    const confermaModal = bootstrap.Modal.getInstance(document.getElementById('confermaArchiviazioneModal'));
    confermaModal.hide();

    // Esegui l'archiviazione
    archiviaViaggiSelezionati();
}

function archiviaViaggiSelezionati() {
    if (viaggiDaArchiviare.length === 0) {
        mostraErrore('Errore', 'Nessun viaggio da archiviare');
        return;
    }

    const btnArchivia = document.getElementById('btnArchiviaSelezionati');
    const originalText = btnArchivia.innerHTML;

    // Mostra loading
    btnArchivia.innerHTML = `
        <span class="spinner-border spinner-border-sm me-2"></span>
        Archiviazione in corso...
    `;
    btnArchivia.disabled = true;

    console.log('🚀 Inizio archiviazione di massa per viaggi:', viaggiDaArchiviare);

    // Invia richiesta di archiviazione
    fetch('/api/sof/realizzati/archivia-massa', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            viaggio_ids: viaggiDaArchiviare
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            mostraSuccesso(
                '🎉 Archiviazione Completata!',
                `${data.totale_archiviati} viaggi sono stati archiviati con successo e sono ora visibili nella sezione "SOF Archiviati".`
            );

            // Rimuovi i viaggi archiviati dalla tabella principale
            rimuoviViaggiArchiviatiDallaTabella(data.archiviati);

            // Ricarica i viaggi per il periodo corrente nel modal
            setTimeout(() => {
                caricaViaggiPerPeriodo();
            }, 1000);

        } else {
            let messaggio = data.message || 'Errore durante l\'archiviazione';

            if (data.errori && data.errori.length > 0) {
                messaggio += `\n\nErrori specifici:\n${data.errori.map(e => `• Viaggio ${e.id}: ${e.errore}`).join('\n')}`;
            }

            mostraErrore('Errore Archiviazione', messaggio);
        }
    })
    .catch(error => {
        console.error('Errore archiviazione massa:', error);
        mostraErrore('Errore di rete', 'Impossibile completare l\'archiviazione. Riprova più tardi.');
    })
    .finally(() => {
        // Ripristina il pulsante
        btnArchivia.innerHTML = originalText;
        btnArchivia.disabled = false;

        // Reset variabile globale
        viaggiDaArchiviare = [];

        console.log('✅ Processo di archiviazione completato');
    });
}

function rimuoviViaggiArchiviatiDallaTabella(viaggiArchiviati) {
    if (!viaggiArchiviati || viaggiArchiviati.length === 0) {
        console.log('⚠️ Nessun viaggio da rimuovere dalla tabella');
        return;
    }

    console.log('🗑️ Rimozione viaggi dalla tabella principale:', viaggiArchiviati);

    let viaggiRimossi = 0;

    viaggiArchiviati.forEach(viaggio => {
        // Cerca il badge con il codice viaggio specifico
        const badges = document.querySelectorAll('.badge.bg-success');

        badges.forEach(badge => {
            // Verifica se il badge contiene esattamente il codice viaggio
            if (badge.textContent.trim() === viaggio.viaggio) {
                // Trova la riga padre (tr) che contiene questo badge
                const riga = badge.closest('tr');

                if (riga) {
                    console.log(`🎯 Trovato viaggio ${viaggio.viaggio} - Rimozione in corso...`);

                    // Animazione di rimozione elegante
                    riga.style.transition = 'all 0.8s ease-out';
                    riga.style.position = 'relative';

                    // Fase 1: Evidenzia in verde (archiviato)
                    riga.style.backgroundColor = '#d1e7dd';
                    riga.style.borderLeft = '4px solid #0f5132';

                    setTimeout(() => {
                        // Fase 2: Fade out e slide
                        riga.style.backgroundColor = '#f8d7da';
                        riga.style.borderLeft = '4px solid #842029';
                        riga.style.transform = 'translateX(-100%)';
                        riga.style.opacity = '0';
                        riga.style.height = '0';
                        riga.style.padding = '0';
                        riga.style.margin = '0';

                        setTimeout(() => {
                            riga.remove();
                            viaggiRimossi++;

                            console.log(`✅ Viaggio ${viaggio.viaggio} rimosso dalla tabella`);

                            // Aggiorna il contatore
                            aggiornaContatoreSofCompletati();

                            // Mostra messaggio se la tabella è vuota
                            verificaTabellaVuota();
                        }, 800);
                    }, 1000);
                }
            }
        });
    });

    // Log finale
    setTimeout(() => {
        console.log(`🎯 Rimozione completata: ${viaggiRimossi}/${viaggiArchiviati.length} viaggi rimossi dalla tabella`);

        if (viaggiRimossi < viaggiArchiviati.length) {
            console.warn(`⚠️ Alcuni viaggi non sono stati trovati nella tabella corrente`);
        }
    }, 2000);
}

function aggiornaContatoreSofCompletati() {
    // Aggiorna il badge con il numero di SOF completati
    const badgeContatore = document.querySelector('.d-flex.flex-column.align-items-end .badge.bg-success');
    if (badgeContatore) {
        const righeVisibili = document.querySelectorAll('tbody tr').length;
        const iconaCheck = '<i class="fas fa-check me-1"></i>';
        badgeContatore.innerHTML = `${iconaCheck}${righeVisibili} SOF Completati`;

        console.log(`📊 Contatore aggiornato: ${righeVisibili} SOF completati`);
    }
}

function verificaTabellaVuota() {
    const tbody = document.querySelector('tbody');
    const righeVisibili = document.querySelectorAll('tbody tr').length;

    if (righeVisibili === 0 && tbody) {
        // Mostra messaggio di tabella vuota
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-5">
                    <div class="text-muted">
                        <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                        <h5>🎉 Tutti i SOF sono stati archiviati!</h5>
                        <p>Non ci sono più SOF realizzati da visualizzare.<br>
                        Puoi visualizzare i SOF archiviati nella sezione dedicata.</p>
                        <a href="/operativo/sof/archiviati" class="btn btn-outline-primary">
                            <i class="fas fa-archive me-2"></i>Vai ai SOF Archiviati
                        </a>
                    </div>
                </td>
            </tr>
        `;

        console.log('📋 Tabella vuota - Messaggio di completamento mostrato');
    }
}
