#!/usr/bin/env python3
import psycopg2
import sys

def check_database_structure():
    try:
        # Connessione al database PostgreSQL
        conn = psycopg2.connect(
            host='localhost',
            database='AGENTE',
            user='re77',
            password='271077'
        )
        cursor = conn.cursor()

        # Ottieni lista delle tabelle
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name;
        """)
        tables = cursor.fetchall()

        print('=== TABELLE PRESENTI NEL DATABASE POSTGRESQL ===')
        for table in tables:
            table_name = table[0]
            print(f'\n--- TABELLA: {table_name.upper()} ---')
            
            # Ottieni struttura della tabella
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = %s AND table_schema = 'public'
                ORDER BY ordinal_position;
            """, (table_name,))
            columns = cursor.fetchall()
            
            print('Colonne:')
            for col in columns:
                name, data_type, nullable, default = col
                nullable_str = ' NULL' if nullable == 'YES' else ' NOT NULL'
                default_str = f' DEFAULT {default}' if default else ''
                print(f'  - {name}: {data_type}{nullable_str}{default_str}')
            
            # Conta righe
            cursor.execute(f'SELECT COUNT(*) FROM "{table_name}"')
            count = cursor.fetchone()[0]
            print(f'Righe presenti: {count}')

        conn.close()
        
    except Exception as e:
        print(f'Errore connessione database: {e}')
        sys.exit(1)

if __name__ == "__main__":
    check_database_structure()
