<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Tema Bianco - SNIP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- CSS Temi Globali -->
    <link rel="stylesheet" href="{{ url_for('static', path='css/global-themes.css') }}">
    <!-- CSS Tema Bianco Professionale -->
    <link rel="stylesheet" href="{{ url_for('static', path='css/theme-light-professional.css') }}">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #dee2e6;
            border-radius: 10px;
        }
        .test-title {
            background: #f8f9fa;
            padding: 10px;
            margin: -20px -20px 20px -20px;
            border-radius: 8px 8px 0 0;
            font-weight: bold;
        }
    </style>
</head>
<body class="theme-light">
    <!-- Navbar Test -->
    <nav class="navbar navbar-expand-lg snip-navbar">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-anchor me-2"></i>SNIP - Test Tema Bianco
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <h1 class="text-center mb-4">🧪 Test Completo Tema Bianco</h1>
        
        <!-- Test Pulsanti -->
        <div class="test-section">
            <div class="test-title">🔘 Test Pulsanti</div>
            <div class="row">
                <div class="col-md-6">
                    <h6>Pulsanti Standard:</h6>
                    <button class="btn btn-primary me-2 mb-2"><i class="fas fa-plus me-1"></i>Primary</button>
                    <button class="btn btn-secondary me-2 mb-2"><i class="fas fa-cog me-1"></i>Secondary</button>
                    <button class="btn btn-success me-2 mb-2"><i class="fas fa-check me-1"></i>Success</button>
                    <button class="btn btn-warning me-2 mb-2"><i class="fas fa-warning me-1"></i>Warning</button>
                    <button class="btn btn-danger me-2 mb-2"><i class="fas fa-trash me-1"></i>Danger</button>
                    <button class="btn btn-info me-2 mb-2"><i class="fas fa-info me-1"></i>Info</button>
                </div>
                <div class="col-md-6">
                    <h6>Pulsanti Outline:</h6>
                    <button class="btn btn-outline-primary me-2 mb-2"><i class="fas fa-edit me-1"></i>Primary</button>
                    <button class="btn btn-outline-secondary me-2 mb-2"><i class="fas fa-gear me-1"></i>Secondary</button>
                    <button class="btn btn-outline-success me-2 mb-2"><i class="fas fa-save me-1"></i>Success</button>
                    <button class="btn btn-outline-warning me-2 mb-2"><i class="fas fa-exclamation me-1"></i>Warning</button>
                    <button class="btn btn-outline-danger me-2 mb-2"><i class="fas fa-times me-1"></i>Danger</button>
                    <button class="btn btn-outline-info me-2 mb-2"><i class="fas fa-question me-1"></i>Info</button>
                </div>
            </div>
        </div>

        <!-- Test Badge -->
        <div class="test-section">
            <div class="test-title">🏷️ Test Badge</div>
            <span class="badge bg-primary me-2"><i class="fas fa-user me-1"></i>Primary</span>
            <span class="badge bg-secondary me-2"><i class="fas fa-tag me-1"></i>Secondary</span>
            <span class="badge bg-success me-2"><i class="fas fa-check me-1"></i>Success</span>
            <span class="badge bg-warning me-2"><i class="fas fa-warning me-1"></i>Warning</span>
            <span class="badge bg-danger me-2"><i class="fas fa-times me-1"></i>Danger</span>
            <span class="badge bg-info me-2"><i class="fas fa-info me-1"></i>Info</span>
            <span class="badge bg-light me-2"><i class="fas fa-lightbulb me-1"></i>Light</span>
            <span class="badge bg-dark me-2"><i class="fas fa-moon me-1"></i>Dark</span>
        </div>

        <!-- Test Card -->
        <div class="test-section">
            <div class="test-title">🃏 Test Card</div>
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="fas fa-ship me-2"></i>Card Header</h5>
                            <small class="text-muted">Sottotitolo card</small>
                        </div>
                        <div class="card-body">
                            <p class="card-text">Questo è il contenuto della card. Dovrebbe essere leggibile.</p>
                            <button class="btn btn-primary"><i class="fas fa-arrow-right me-1"></i>Azione</button>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-light">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="fas fa-anchor me-2"></i>Card Light</h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">Card con background light.</p>
                            <button class="btn btn-outline-primary"><i class="fas fa-eye me-1"></i>Visualizza</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Tabella -->
        <div class="test-section">
            <div class="test-title">📊 Test Tabella</div>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th><i class="fas fa-id-card me-1"></i>ID</th>
                            <th><i class="fas fa-user me-1"></i>Nome</th>
                            <th><i class="fas fa-envelope me-1"></i>Email</th>
                            <th><i class="fas fa-cogs me-1"></i>Azioni</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><span class="badge bg-secondary">001</span></td>
                            <td><strong>Mario Rossi</strong></td>
                            <td><EMAIL></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary"><i class="fas fa-edit"></i></button>
                                <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                        <tr>
                            <td><span class="badge bg-primary">002</span></td>
                            <td><strong>Luigi Verdi</strong></td>
                            <td><EMAIL></td>
                            <td>
                                <button class="btn btn-sm btn-outline-warning"><i class="fas fa-eye"></i></button>
                                <button class="btn btn-sm btn-outline-info"><i class="fas fa-download"></i></button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Test Form -->
        <div class="test-section">
            <div class="test-title">📝 Test Form</div>
            <form>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label"><i class="fas fa-user me-1"></i>Nome</label>
                            <input type="text" class="form-control" placeholder="Inserisci nome">
                        </div>
                        <div class="mb-3">
                            <label class="form-label"><i class="fas fa-envelope me-1"></i>Email</label>
                            <input type="email" class="form-control" placeholder="Inserisci email">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label"><i class="fas fa-list me-1"></i>Categoria</label>
                            <select class="form-select">
                                <option>Seleziona categoria</option>
                                <option>Categoria 1</option>
                                <option>Categoria 2</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="check1">
                                <label class="form-check-label" for="check1">
                                    <i class="fas fa-check me-1"></i>Accetto i termini
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Test Alert -->
        <div class="test-section">
            <div class="test-title">⚠️ Test Alert</div>
            <div class="alert alert-primary"><i class="fas fa-info-circle me-2"></i>Alert Primary</div>
            <div class="alert alert-secondary"><i class="fas fa-tag me-2"></i>Alert Secondary</div>
            <div class="alert alert-success"><i class="fas fa-check-circle me-2"></i>Alert Success</div>
            <div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>Alert Warning</div>
            <div class="alert alert-danger"><i class="fas fa-times-circle me-2"></i>Alert Danger</div>
            <div class="alert alert-info"><i class="fas fa-info me-2"></i>Alert Info</div>
        </div>

        <!-- Test Icone Standalone -->
        <div class="test-section">
            <div class="test-title">🎨 Test Icone Standalone</div>
            <div class="row">
                <div class="col-md-12">
                    <h6>Icone con classi colore:</h6>
                    <i class="fas fa-home fa-2x text-primary me-3"></i>
                    <i class="fas fa-user fa-2x text-secondary me-3"></i>
                    <i class="fas fa-cog fa-2x text-success me-3"></i>
                    <i class="fas fa-bell fa-2x text-warning me-3"></i>
                    <i class="fas fa-heart fa-2x text-danger me-3"></i>
                    <i class="fas fa-star fa-2x text-info me-3"></i>
                    <i class="fas fa-sun fa-2x text-light me-3"></i>
                    <i class="fas fa-moon fa-2x text-dark me-3"></i>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-12">
                    <h6>Icone senza classi colore (dovrebbero ereditare):</h6>
                    <i class="fas fa-ship fa-2x me-3"></i>
                    <i class="fas fa-anchor fa-2x me-3"></i>
                    <i class="fas fa-compass fa-2x me-3"></i>
                    <i class="fas fa-map fa-2x me-3"></i>
                    <i class="fas fa-route fa-2x me-3"></i>
                    <i class="fas fa-globe fa-2x me-3"></i>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-12">
                    <h6>Icone grandi colorate:</h6>
                    <i class="fas fa-ship fa-5x text-primary me-3"></i>
                    <i class="fas fa-anchor fa-4x text-success me-3"></i>
                    <i class="fas fa-compass fa-3x text-info me-3"></i>
                </div>
            </div>
        </div>

        <!-- Test Immagini e Avatar -->
        <div class="test-section">
            <div class="test-title">🖼️ Test Immagini e Avatar</div>
            <div class="row">
                <div class="col-md-4">
                    <h6>Avatar Circle:</h6>
                    <div class="avatar-circle me-3" style="width: 80px; height: 80px; font-size: 2rem;">
                        U
                    </div>
                </div>
                <div class="col-md-4">
                    <h6>Logo Test:</h6>
                    <div class="logo" style="width: 80px; height: 80px; background: #667eea; border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">
                        LOGO
                    </div>
                </div>
                <div class="col-md-4">
                    <h6>Profile Image:</h6>
                    <div class="profile-pic" style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">
                        IMG
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Dropdown -->
        <div class="test-section">
            <div class="test-title">📋 Test Dropdown</div>
            <div class="dropdown">
                <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-list me-1"></i>Dropdown Test
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#"><i class="fas fa-home me-2"></i>Home</a></li>
                    <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profilo</a></li>
                    <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Impostazioni</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>

        <!-- Test Controlli Tema -->
        <div class="test-section">
            <div class="test-title">🎨 Controlli Tema</div>
            <button class="btn btn-primary me-2" onclick="setTheme('maritime')">Tema Marittimo</button>
            <button class="btn btn-secondary me-2" onclick="setTheme('dark')">Tema Scuro Professionale</button>
            <button class="btn btn-light me-2" onclick="setTheme('light')">Tema Chiaro Professionale</button>
            <hr class="my-3">
            <h6>🔗 Link Pagine Test:</h6>
            <a href="/test-tema-scuro" class="btn btn-outline-dark me-2">Test Tema Scuro</a>
            <a href="/test-tema-bianco" class="btn btn-outline-light me-2">Test Tema Bianco</a>
            <a href="/dashboard/operativo" class="btn btn-outline-primary me-2">Dashboard</a>

            <hr class="my-3">
            <h6>🌙 Test Tema Scuro Inline:</h6>
            <button class="btn btn-dark me-2" onclick="testDarkTheme()">Attiva Tema Scuro</button>
            <div id="dark-theme-test" style="display: none; margin-top: 20px; padding: 20px; background: #1a1a2e; color: white; border-radius: 10px;">
                <h5>Test Tema Scuro</h5>
                <p class="text-primary">Testo Primary</p>
                <p class="text-secondary">Testo Secondary</p>
                <p class="text-success">Testo Success</p>
                <p class="text-warning">Testo Warning</p>
                <p class="text-danger">Testo Danger</p>
                <p class="text-info">Testo Info</p>
                <p class="text-muted">Testo Muted</p>
                <button class="btn btn-primary me-2">Primary</button>
                <button class="btn btn-secondary me-2">Secondary</button>
                <button class="btn btn-success me-2">Success</button>
                <button class="btn btn-warning me-2">Warning</button>
                <button class="btn btn-danger me-2">Danger</button>
                <button class="btn btn-info me-2">Info</button>
            </div>
        </div>

        <!-- Test Stat Cards -->
        <div class="test-section">
            <div class="test-title">📊 Test Stat Cards</div>
            <div class="row">
                <div class="col-md-3">
                    <div class="card stat-card primary">
                        <div class="card-body text-center">
                            <i class="fas fa-ship stat-icon"></i>
                            <div class="stat-number">1305</div>
                            <div class="stat-label">Navi Totali</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card success">
                        <div class="card-body text-center">
                            <i class="fas fa-calendar-alt stat-icon"></i>
                            <div class="stat-number">7</div>
                            <div class="stat-label">Navi Schedulate</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card warning">
                        <div class="card-body text-center">
                            <i class="fas fa-file-alt stat-icon"></i>
                            <div class="stat-number">7</div>
                            <div class="stat-label">SOF da Completare</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card info">
                        <div class="card-body text-center">
                            <i class="fas fa-users stat-icon"></i>
                            <div class="stat-number">1</div>
                            <div class="stat-label">Utenti Online</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/theme-manager.js"></script>
    <script src="/static/js/global-themes.js"></script>
    <script>
        function setTheme(theme) {
            if (window.SNIPThemes) {
                console.log('🎨 Richiesta cambio tema a:', theme);
                window.SNIPThemes.change(theme);
                // La pagina si ricaricherà automaticamente
            } else {
                console.warn('SNIPThemes non ancora caricato, riprovo...');
                setTimeout(() => setTheme(theme), 100);
            }
        }

        function testDarkTheme() {
            const testDiv = document.getElementById('dark-theme-test');
            if (testDiv.style.display === 'none') {
                testDiv.style.display = 'block';
                setTheme('dark');
            } else {
                testDiv.style.display = 'none';
                setTheme('light');
            }
        }

        // Inizializza tema al caricamento
        document.addEventListener('DOMContentLoaded', function() {
            // Aspetta che SNIPThemes sia caricato
            const waitForThemes = () => {
                if (window.SNIPThemes) {
                    console.log('✅ SNIPThemes caricato, tema corrente:', window.SNIPThemes.getCurrentTheme());
                } else {
                    setTimeout(waitForThemes, 100);
                }
            };
            waitForThemes();
        });
        
        // Forza tema chiaro all'avvio
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                setTheme('light');
            }, 500);
        });
    </script>
</body>
</html>
