# Comandi per Aggiustare pg_dump - Backup PostgreSQL

## Problema Iniziale
Il sistema non trovava `pg_dump` anche se pgAdmin4 era installato, causando errori di backup.

## Diagnosi del Problema

### 1. Verifica se pg_dump è nel PATH
```powershell
where pg_dump
```
**Risultato**: Comando non trovato

```powershell
pg_dump --version
```
**Risultato**: `pg_dump : Termine 'pg_dump' non riconosciuto`

### 2. Ricerca di pg_dump nel sistema
```powershell
Get-ChildItem "C:\Program Files\PostgreSQL" -Recurse -Name "pg_dump.exe" -ErrorAction SilentlyContinue
```
**Risultato**: 
```
17\bin\pg_dump.exe
17\pgAdmin 4\runtime\pg_dump.exe
```

**Percorso trovato**: `C:\Program Files\PostgreSQL\17\bin\pg_dump.exe`

## Soluzione Implementata

### 3. Modifica del BackupManager
Aggiornato il metodo `_try_pg_dump_backup()` per cercare pg_dump in percorsi standard:

```python
# Trova pg_dump nel sistema
pg_dump_paths = [
    'pg_dump',  # Se è nel PATH
    r'C:\Program Files\PostgreSQL\17\bin\pg_dump.exe',
    r'C:\Program Files\PostgreSQL\16\bin\pg_dump.exe',
    r'C:\Program Files\PostgreSQL\15\bin\pg_dump.exe',
    r'C:\Program Files (x86)\PostgreSQL\17\bin\pg_dump.exe'
]

pg_dump_exe = None
for path in pg_dump_paths:
    try:
        if path == 'pg_dump':
            # Test se è nel PATH
            subprocess.run([path, '--version'], capture_output=True, timeout=5)
            pg_dump_exe = path
            break
        else:
            # Test percorso completo
            from pathlib import Path as PathLib
            if PathLib(path).exists():
                pg_dump_exe = path
                break
    except:
        continue

if not pg_dump_exe:
    logger.warning("pg_dump non trovato nel sistema")
    return False
```

### 4. Test del Fix
```python
python test_pgdump_native.py
```

**Risultato**:
```
SUCCESS: ENTRAMBI I FORMATI FUNZIONANTI!
pg_dump nativo disponibile e funzionante

FORMATI DISPONIBILI:
1. SQL format (psql compatible)
2. Custom format (pg_restore compatible)
```

## Formati di Backup Disponibili

### Formato SQL (Testo)
```python
backup_path = backup_manager.create_backup(format_type="sql")
```
- **File**: `snip_backup_YYYYMMDD_HHMMSS.sql.gz`
- **Compatibile**: `psql`
- **Importazione**: 
  ```bash
  gunzip file.sql.gz
  psql -d AGENTE -f file.sql
  ```

### Formato Custom (Binario) - RACCOMANDATO
```python
backup_path = backup_manager.create_backup(format_type="custom")
```
- **File**: `snip_backup_YYYYMMDD_HHMMSS.dump.gz`
- **Compatibile**: `pg_restore`
- **Importazione**: 
  ```bash
  gunzip file.dump.gz
  pg_restore -d AGENTE file.dump
  ```

## Vantaggi del Formato Custom

✅ **Nessun errore di sintassi**: Formato nativo PostgreSQL  
✅ **Ripristino selettivo**: Singole tabelle o schemi  
✅ **Performance migliori**: Import più veloce  
✅ **Compatibilità garantita**: pg_restore ufficiale  
✅ **Formato binario compatto**: Più efficiente  

## Risoluzione Errori Precedenti

### ❌ "input file does not appear to be a valid archive"
**Causa**: Tentativo di usare `pg_restore` su file SQL  
**Soluzione**: Usa formato custom per `pg_restore`

### ❌ "zero-length delimited identifier"
**Causa**: Errori di parsing nei nomi delle sequenze  
**Soluzione**: pg_dump nativo gestisce correttamente la sintassi

### ❌ "relation does not exist"
**Causa**: Sequenze non create correttamente  
**Soluzione**: pg_dump include automaticamente tutte le dipendenze

### ❌ "syntax error at or near"
**Causa**: Caratteri speciali non escaped correttamente  
**Soluzione**: pg_dump gestisce automaticamente l'escape

## Configurazione Finale Raccomandata

### Per Backup Automatici (Scheduler)
```python
# Usa formato custom per backup automatici
backup_path = backup_manager.create_backup(format_type="custom")
```

### Per Backup Manuali/Debug
```python
# Usa formato SQL per ispezione manuale
backup_path = backup_manager.create_backup(format_type="sql")
```

## Verifica Installazione PostgreSQL

### Controlla versione PostgreSQL
```powershell
"C:\Program Files\PostgreSQL\17\bin\pg_dump.exe" --version
```

### Controlla connessione database
```powershell
"C:\Program Files\PostgreSQL\17\bin\psql.exe" -h localhost -p 5432 -U re77 -d AGENTE -c "\dt"
```

## Note Importanti

1. **pgAdmin4 include pg_dump**: Se pgAdmin4 è installato, pg_dump è disponibile
2. **PATH non necessario**: Il sistema trova automaticamente pg_dump
3. **Formato custom raccomandato**: Per backup di produzione
4. **Backup automatico**: Funziona alle 12:45 con formato custom
5. **Email backup**: Allegato in formato .dump.gz

## Stato Finale

✅ **pg_dump nativo**: Funzionante  
✅ **Formato SQL**: Disponibile per psql  
✅ **Formato Custom**: Disponibile per pg_restore  
✅ **Backup automatico**: Operativo  
✅ **Tutti gli errori**: Risolti  

**Il sistema di backup PostgreSQL è ora completamente funzionante!**
