#!/usr/bin/env python3
"""
Migrazione per aggiungere la colonna tema_preferito alla tabella AGENTE
"""

import sys
import os
from sqlalchemy import text
from database import SessionLocal
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_user_theme_column():
    """Aggiunge la colonna tema_preferito alla tabella AGENTE se non esiste"""
    db = SessionLocal()
    
    try:
        logger.info("🎨 Inizio migrazione colonna tema_preferito...")
        
        # Verifica se la colonna esiste già
        result = db.execute(text("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'AGENTE' AND column_name = 'tema_preferito'
        """)).fetchone()

        if not result:
            logger.info("📝 Aggiunta colonna tema_preferito alla tabella AGENTE...")
            
            # Aggiungi la colonna con valore predefinito 'light'
            db.execute(text("""
                ALTER TABLE "AGENTE"
                ADD COLUMN tema_preferito VARCHAR(20) DEFAULT 'light' 
                CHECK (tema_preferito IN ('light', 'dark', 'maritime'))
            """))

            # Crea indice per performance
            try:
                db.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_agente_tema_preferito
                    ON "AGENTE" (tema_preferito)
                """))
                logger.info("📊 Indice idx_agente_tema_preferito creato")
            except Exception as e:
                logger.warning(f"⚠️ Errore creazione indice: {e}")

            db.commit()
            logger.info("✅ Colonna tema_preferito aggiunta alla tabella AGENTE")

            # Aggiorna tutti gli utenti esistenti con tema 'light' come default
            logger.info("🔄 Aggiornamento utenti esistenti...")
            result = db.execute(text("""
                UPDATE "AGENTE"
                SET tema_preferito = 'light'
                WHERE tema_preferito IS NULL
            """))
            
            rows_updated = result.rowcount
            db.commit()
            logger.info(f"✅ Aggiornati {rows_updated} utenti esistenti con tema predefinito 'light'")
            
        else:
            logger.info("✅ Colonna tema_preferito già presente nella tabella AGENTE")

    except Exception as e:
        logger.error(f"❌ Errore durante la migrazione: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

def verify_migration():
    """Verifica che la migrazione sia stata completata correttamente"""
    db = SessionLocal()
    
    try:
        logger.info("🔍 Verifica migrazione...")
        
        # Verifica struttura colonna
        result = db.execute(text("""
            SELECT column_name, data_type, column_default, is_nullable
            FROM information_schema.columns
            WHERE table_name = 'AGENTE' AND column_name = 'tema_preferito'
        """)).fetchone()
        
        if result:
            logger.info(f"✅ Colonna tema_preferito trovata:")
            logger.info(f"   - Tipo: {result[1]}")
            logger.info(f"   - Default: {result[2]}")
            logger.info(f"   - Nullable: {result[3]}")
        else:
            logger.error("❌ Colonna tema_preferito non trovata!")
            return False
            
        # Verifica constraint
        constraint_result = db.execute(text("""
            SELECT constraint_name, check_clause
            FROM information_schema.check_constraints
            WHERE constraint_name LIKE '%tema_preferito%'
        """)).fetchone()
        
        if constraint_result:
            logger.info(f"✅ Constraint trovato: {constraint_result[1]}")
        else:
            logger.warning("⚠️ Constraint tema_preferito non trovato")
            
        # Conta utenti per tema
        theme_counts = db.execute(text("""
            SELECT tema_preferito, COUNT(*) as count
            FROM "AGENTE"
            GROUP BY tema_preferito
        """)).fetchall()
        
        logger.info("📊 Distribuzione temi utenti:")
        for theme, count in theme_counts:
            logger.info(f"   - {theme}: {count} utenti")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore durante la verifica: {str(e)}")
        return False
    finally:
        db.close()

def main():
    """Funzione principale per eseguire la migrazione"""
    try:
        logger.info("🚀 Avvio migrazione tema utente...")
        logger.info("")
        
        # Esegui migrazione
        add_user_theme_column()
        
        # Verifica migrazione
        if verify_migration():
            logger.info("")
            logger.info("🎉 Migrazione completata con successo!")
            logger.info("")
            logger.info("📋 RIEPILOGO:")
            logger.info("   ✅ Colonna tema_preferito aggiunta alla tabella AGENTE")
            logger.info("   ✅ Constraint per valori validi (light, dark, maritime)")
            logger.info("   ✅ Indice per performance creato")
            logger.info("   ✅ Utenti esistenti aggiornati con tema 'light'")
            logger.info("")
            logger.info("🎛️ FUNZIONALITÀ:")
            logger.info("   - Ogni utente può scegliere il proprio tema")
            logger.info("   - Tema salvato nel database per ogni utente")
            logger.info("   - Tema mantenuto tra le sessioni")
            logger.info("   - Temi disponibili: light, dark, maritime")
            logger.info("")
            logger.info("🧪 TEST:")
            logger.info("   - Accedi come utente diverso")
            logger.info("   - Cambia tema dalle impostazioni")
            logger.info("   - Verifica che il tema sia mantenuto al prossimo login")
            logger.info("")
        else:
            logger.error("❌ Migrazione fallita durante la verifica!")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"💥 Errore critico durante la migrazione: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
