# 🔧 Correzione Errore Sottrazione - RISOLTO

## 🎯 Problema Identificato

**Errore**: `unsupported operand type(s) for -: 'str' and 'int'`
**Posizione**: Funzione `is_password_expired` in `main.py` riga 3352
**Causa**: Tentativo di sottrarre un intero da una stringa

## 🔍 Analisi del Problema

### **Errore Originale:**
```python
# Riga 3352 in main.py
days_remaining = expiry_days - days_passed
```

### **Causa Radice:**
La variabile `expiry_days` veniva letta dal database tramite `get_security_config()` e poteva essere restituita come **stringa** invece che come **intero**.

Quando il database restituisce il valore `"90"` (stringa) invece di `90` (intero), l'operazione:
```python
"90" - 15  # Errore: unsupported operand type(s) for -: 'str' and 'int'
```

## ✅ Soluzione Implementata

### **Correzione Applicata:**
```python
# PRIMA (problematico)
config = get_security_config(db)
expiry_days = config.get("password_expiry_days", 90)

# DOPO (corretto)
config = get_security_config(db)
expiry_days_raw = config.get("password_expiry_days", 90)

# Assicurati che expiry_days sia un intero
try:
    expiry_days = int(expiry_days_raw)
except (ValueError, TypeError):
    logger.warning(f"Valore password_expiry_days non valido: {expiry_days_raw}, uso default 90")
    expiry_days = 90
```

### **Vantaggi della Correzione:**
1. ✅ **Conversione Sicura**: Converte sempre il valore in intero
2. ✅ **Gestione Errori**: Cattura errori di conversione
3. ✅ **Fallback Robusto**: Usa valore predefinito se conversione fallisce
4. ✅ **Logging**: Registra valori problematici per debug

## 🧪 Test della Correzione

### **Scenari Testati:**
1. **Valore Intero Corretto**: `90` → `90` (int)
2. **Valore Stringa Valida**: `"90"` → `90` (int)
3. **Valore Stringa Non Numerica**: `"abc"` → `90` (default)
4. **Valore None**: `None` → `90` (default)
5. **Valore Vuoto**: `""` → `90` (default)

### **Comportamento Atteso:**
```python
# Test 1: Valore corretto
expiry_days_raw = 90
expiry_days = int(expiry_days_raw)  # → 90

# Test 2: Stringa numerica
expiry_days_raw = "90"
expiry_days = int(expiry_days_raw)  # → 90

# Test 3: Valore non valido
expiry_days_raw = "abc"
# ValueError catturato → expiry_days = 90 (default)
```

## 🔄 Funzionalità Correlate

### **Funzione `is_password_expired`:**
Questa funzione è utilizzata durante il **processo di login** per:
1. Verificare se la password dell'utente è scaduta
2. Calcolare i giorni rimanenti prima della scadenza
3. Forzare il cambio password se necessario
4. Mostrare avvisi di scadenza imminente

### **Configurazione Sistema:**
Il valore `password_expiry_days` è configurabile tramite:
- **Database**: Tabella `SYSTEM_CONFIG`
- **Admin Panel**: Dashboard > Amministrazione > Configurazioni > Sicurezza
- **Default**: 90 giorni se non configurato

## 🎯 Impatto della Correzione

### **Prima della Correzione:**
- ❌ Errore durante login se `password_expiry_days` era stringa
- ❌ Impossibilità di accedere al sistema
- ❌ Log di errore: "unsupported operand type(s) for -: 'str' and 'int'"

### **Dopo la Correzione:**
- ✅ Login funziona sempre, indipendentemente dal tipo di dato
- ✅ Conversione automatica stringa → intero
- ✅ Fallback sicuro a valore predefinito
- ✅ Logging per debug di valori problematici

## 🔧 Codice Completo Corretto

```python
def is_password_expired(user_data, db: Session) -> tuple[bool, int]:
    """
    Verifica se la password dell'utente è scaduta
    Restituisce (is_expired, days_remaining)
    """
    try:
        # Assicurati che la colonna esista
        add_last_password_change_column(db)

        # Ottieni configurazioni di sicurezza
        config = get_security_config(db)
        expiry_days_raw = config.get("password_expiry_days", 90)
        
        # Assicurati che expiry_days sia un intero
        try:
            expiry_days = int(expiry_days_raw)
        except (ValueError, TypeError):
            logger.warning(f"Valore password_expiry_days non valido: {expiry_days_raw}, uso default 90")
            expiry_days = 90

        # Se expiry_days è 0 o negativo, le password non scadono mai
        if expiry_days <= 0:
            return False, -1

        # ... resto della funzione rimane invariato
        
        # Calcola i giorni trascorsi
        days_passed = (now - last_change).days
        days_remaining = expiry_days - days_passed  # ← Ora sempre int - int
        
        is_expired = days_remaining <= 0
        return is_expired, max(0, days_remaining)

    except Exception as e:
        logger.error(f"Errore verifica scadenza password per {user_data.email}: {e}")
        return False, -1
```

## 🎉 Risultato Finale

**Errore completamente risolto!**

L'applicazione ora gestisce correttamente:
- ✅ **Valori interi** dal database
- ✅ **Valori stringa** dal database (con conversione automatica)
- ✅ **Valori non validi** (con fallback sicuro)
- ✅ **Logging** per debug e monitoraggio

**Il sistema di scadenza password ora funziona in modo robusto e affidabile.**
