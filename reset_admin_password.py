#!/usr/bin/env python3
"""
Script per resettare la password dell'admin esistente
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import bcrypt
from datetime import datetime

# Configurazione database
DATABASE_URL = "postgresql://re77:271077@localhost:5432/AGENTE"

def reset_admin_password():
    """Resetta la password dell'admin esistente"""
    
    print("🔑 RESET PASSWORD ADMIN")
    print("=" * 30)
    
    try:
        # Connessione al database
        engine = create_engine(DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        print("✅ Connessione database stabilita")
        
        # Trova tutti gli admin
        result = db.execute(text('''
            SELECT email, "Nome", "Cognome", visibile 
            FROM "AGENTE" 
            WHERE ruolo = 'SUPER_ADMIN'
        '''))
        admins = result.fetchall()
        
        if not admins:
            print("❌ Nessun admin trovato!")
            return False
        
        print(f"📊 Admin trovati: {len(admins)}")
        
        # Nuova password
        new_password = "admin123"
        password_hash = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        print(f"🔐 Nuova password: {new_password}")
        print("🔒 Hash generato")
        
        # Aggiorna password per tutti gli admin
        for admin in admins:
            email, nome, cognome, visibile = admin
            print(f"\n👤 Aggiornamento admin: {email}")
            print(f"   📝 Nome: {nome} {cognome}")
            print(f"   👁️ Visibile: {visibile}")
            
            # Aggiorna password e assicurati che sia visibile
            db.execute(text('''
                UPDATE "AGENTE" 
                SET password = :password,
                    visibile = 'si',
                    last_password_change = :last_change
                WHERE email = :email
            '''), {
                "password": password_hash,
                "last_change": datetime.now(),
                "email": email
            })
            
            print(f"   ✅ Password aggiornata")
            print(f"   ✅ Account attivato")
        
        db.commit()
        print("\n💾 Modifiche salvate nel database")
        
        # Verifica finale
        result = db.execute(text('''
            SELECT email, visibile 
            FROM "AGENTE" 
            WHERE ruolo = 'SUPER_ADMIN' AND visibile = 'si'
        '''))
        active_admins = result.fetchall()
        
        print(f"\n📊 Admin attivi dopo aggiornamento: {len(active_admins)}")
        for admin in active_admins:
            email, visibile = admin
            print(f"   ✅ {email} - Visibile: {visibile}")
        
        return len(active_admins) > 0
        
    except Exception as e:
        print(f"❌ Errore durante reset password: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            db.close()
        except:
            pass

def test_login_with_reset_password():
    """Testa il login con la password resettata"""
    
    print("\n🧪 TEST LOGIN CON PASSWORD RESETTATA")
    print("-" * 40)
    
    try:
        import requests
        
        # Prova login con admin esistente
        login_data = {
            "username": "<EMAIL>",
            "password": "admin123"
        }
        
        print(f"🔐 Test login: {login_data['username']}")
        
        response = requests.post(
            "http://127.0.0.1:8003/login", 
            data=login_data, 
            timeout=15, 
            allow_redirects=False
        )
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 303:
            redirect_url = response.headers.get('location', '')
            print(f"✅ LOGIN RIUSCITO!")
            print(f"🔄 Redirect a: {redirect_url}")
            return True
        elif response.status_code == 200:
            # Analizza il contenuto per capire l'errore
            content = response.text.lower()
            if "credenziali non valide" in content:
                print("❌ Credenziali non valide")
            elif "account non attivo" in content:
                print("❌ Account non attivo")
            elif "password scaduta" in content:
                print("⚠️ Password scaduta - ma login processato")
                return True
            elif "dashboard" in content:
                print("✅ Login riuscito (dashboard caricata)")
                return True
            else:
                print("⚠️ Login processato ma stato incerto")
                # Mostra parte del contenuto per debug
                print(f"   📄 Contenuto (primi 200 char): {response.text[:200]}")
        else:
            print(f"❌ Login fallito: {response.status_code}")
        
        return False
        
    except Exception as e:
        print(f"❌ Errore test login: {e}")
        return False

def main():
    """Funzione principale"""
    
    print("🚀 RESET PASSWORD ADMIN SNIP")
    print("=" * 40)
    print(f"🕐 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Step 1: Reset password admin
    if not reset_admin_password():
        print("\n❌ IMPOSSIBILE RESETTARE PASSWORD ADMIN")
        return False
    
    # Step 2: Test login
    if test_login_with_reset_password():
        print("\n🎉 PASSWORD RESETTATA E LOGIN FUNZIONANTE!")
        print("\n📋 CREDENZIALI ADMIN:")
        print("   Email: <EMAIL>")
        print("   Password: admin123")
        print("\n🔧 PROSSIMI PASSI:")
        print("1. Accedi con le credenziali sopra")
        print("2. Cambia la password per sicurezza")
        print("3. Attiva altri utenti se necessario")
        return True
    else:
        print("\n❌ PASSWORD RESETTATA MA LOGIN ANCORA NON FUNZIONA")
        print("\n🔧 POSSIBILI PROBLEMI:")
        print("1. Funzione verify_password non funziona correttamente")
        print("2. Problema con l'hashing delle password")
        print("3. Altri controlli nel processo di login")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
