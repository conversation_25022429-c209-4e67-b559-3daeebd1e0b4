<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Tema Scuro - SNIP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- CSS Temi Globali -->
    <link rel="stylesheet" href="{{ url_for('static', path='css/global-themes.css') }}">
    <!-- CSS Tema Scuro Professionale -->
    <link rel="stylesheet" href="{{ url_for('static', path='css/theme-dark-professional.css') }}">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #dee2e6;
            border-radius: 10px;
        }
        .test-title {
            background: #f8f9fa;
            padding: 10px;
            margin: -20px -20px 20px -20px;
            border-radius: 8px 8px 0 0;
            font-weight: bold;
            color: #212529;
        }
        .visibility-test {
            background: white;
            color: white;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }
        .visibility-test.dark-bg {
            background: #1a1a2e;
        }
        .visibility-test.medium-bg {
            background: #666;
        }
    </style>
</head>
<body class="theme-dark">
    <!-- Navbar Test -->
    <nav class="navbar navbar-expand-lg snip-navbar">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-anchor me-2"></i>SNIP - Test Tema Scuro
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#"><i class="fas fa-user me-1"></i>Utente Test</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h1 class="text-center mb-4">🌙 Test Completo Tema Scuro - Leggibilità</h1>
        
        <!-- Test Visibilità Testi -->
        <div class="test-section">
            <div class="test-title">👁️ Test Visibilità Testi</div>
            <div class="row">
                <div class="col-md-12">
                    <h6>Testi su sfondi diversi:</h6>
                    <div class="visibility-test">Testo bianco su sfondo bianco (INVISIBILE)</div>
                    <div class="visibility-test dark-bg">Testo bianco su sfondo scuro (VISIBILE)</div>
                    <div class="visibility-test medium-bg">Testo bianco su sfondo medio (PROBLEMATICO)</div>
                    
                    <h6 class="mt-3">Testi con classi Bootstrap:</h6>
                    <p class="text-primary">Testo Primary</p>
                    <p class="text-secondary">Testo Secondary</p>
                    <p class="text-success">Testo Success</p>
                    <p class="text-warning">Testo Warning</p>
                    <p class="text-danger">Testo Danger</p>
                    <p class="text-info">Testo Info</p>
                    <p class="text-light">Testo Light</p>
                    <p class="text-dark">Testo Dark</p>
                    <p class="text-muted">Testo Muted</p>
                    <p class="text-white">Testo White</p>
                </div>
            </div>
        </div>

        <!-- Test Stat Cards -->
        <div class="test-section">
            <div class="test-title">📊 Test Stat Cards</div>
            <div class="row">
                <div class="col-md-3">
                    <div class="card stat-card primary">
                        <div class="card-body text-center">
                            <i class="fas fa-ship stat-icon"></i>
                            <div class="stat-number">1305</div>
                            <div class="stat-label">Navi Totali</div>
                            <small class="text-muted">Testo piccolo</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card success">
                        <div class="card-body text-center">
                            <i class="fas fa-calendar-alt stat-icon"></i>
                            <div class="stat-number">7</div>
                            <div class="stat-label">Navi Schedulate</div>
                            <small class="text-muted">Dal 06/06 al 17/06</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card warning">
                        <div class="card-body text-center">
                            <i class="fas fa-file-alt stat-icon"></i>
                            <div class="stat-number">7</div>
                            <div class="stat-label">SOF da Completare</div>
                            <small class="text-muted">Urgenti</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stat-card info">
                        <div class="card-body text-center">
                            <i class="fas fa-users stat-icon"></i>
                            <div class="stat-number">1</div>
                            <div class="stat-label">Utenti Online</div>
                            <small class="text-muted">Ultimi 30 min</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Pulsanti -->
        <div class="test-section">
            <div class="test-title">🔘 Test Pulsanti</div>
            <div class="row">
                <div class="col-md-6">
                    <h6>Pulsanti Standard:</h6>
                    <button class="btn btn-primary me-2 mb-2"><i class="fas fa-plus me-1"></i>Primary</button>
                    <button class="btn btn-secondary me-2 mb-2"><i class="fas fa-cog me-1"></i>Secondary</button>
                    <button class="btn btn-success me-2 mb-2"><i class="fas fa-check me-1"></i>Success</button>
                    <button class="btn btn-warning me-2 mb-2"><i class="fas fa-warning me-1"></i>Warning</button>
                    <button class="btn btn-danger me-2 mb-2"><i class="fas fa-trash me-1"></i>Danger</button>
                    <button class="btn btn-info me-2 mb-2"><i class="fas fa-info me-1"></i>Info</button>
                    <button class="btn btn-light me-2 mb-2"><i class="fas fa-sun me-1"></i>Light</button>
                    <button class="btn btn-dark me-2 mb-2"><i class="fas fa-moon me-1"></i>Dark</button>
                </div>
                <div class="col-md-6">
                    <h6>Pulsanti Outline:</h6>
                    <button class="btn btn-outline-primary me-2 mb-2"><i class="fas fa-edit me-1"></i>Primary</button>
                    <button class="btn btn-outline-secondary me-2 mb-2"><i class="fas fa-gear me-1"></i>Secondary</button>
                    <button class="btn btn-outline-success me-2 mb-2"><i class="fas fa-save me-1"></i>Success</button>
                    <button class="btn btn-outline-warning me-2 mb-2"><i class="fas fa-exclamation me-1"></i>Warning</button>
                    <button class="btn btn-outline-danger me-2 mb-2"><i class="fas fa-times me-1"></i>Danger</button>
                    <button class="btn btn-outline-info me-2 mb-2"><i class="fas fa-question me-1"></i>Info</button>
                    <button class="btn btn-outline-light me-2 mb-2"><i class="fas fa-lightbulb me-1"></i>Light</button>
                    <button class="btn btn-outline-dark me-2 mb-2"><i class="fas fa-circle me-1"></i>Dark</button>
                </div>
            </div>
        </div>

        <!-- Test Card Generiche -->
        <div class="test-section">
            <div class="test-title">🃏 Test Card Generiche</div>
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="fas fa-ship me-2"></i>Card Header</h5>
                            <small class="text-muted">Sottotitolo card</small>
                        </div>
                        <div class="card-body">
                            <p class="card-text">Questo è il contenuto della card. Dovrebbe essere leggibile.</p>
                            <p class="text-muted">Testo secondario muted</p>
                            <button class="btn btn-primary"><i class="fas fa-arrow-right me-1"></i>Azione</button>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><i class="fas fa-anchor me-2"></i>Card Test</h5>
                        </div>
                        <div class="card-body">
                            <h6 class="card-subtitle mb-2 text-muted">Sottotitolo</h6>
                            <p class="card-text">Contenuto della card con vari elementi di testo.</p>
                            <a href="#" class="card-link">Link card</a>
                            <a href="#" class="card-link">Altro link</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Form -->
        <div class="test-section">
            <div class="test-title">📝 Test Form</div>
            <form>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label"><i class="fas fa-user me-1"></i>Nome</label>
                            <input type="text" class="form-control" placeholder="Inserisci nome">
                            <div class="form-text">Testo di aiuto</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label"><i class="fas fa-envelope me-1"></i>Email</label>
                            <input type="email" class="form-control" placeholder="Inserisci email">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label"><i class="fas fa-list me-1"></i>Categoria</label>
                            <select class="form-select">
                                <option>Seleziona categoria</option>
                                <option>Categoria 1</option>
                                <option>Categoria 2</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="check1">
                                <label class="form-check-label" for="check1">
                                    <i class="fas fa-check me-1"></i>Accetto i termini
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Test Tabella -->
        <div class="test-section">
            <div class="test-title">📊 Test Tabella</div>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th><i class="fas fa-id-card me-1"></i>ID</th>
                            <th><i class="fas fa-user me-1"></i>Nome</th>
                            <th><i class="fas fa-envelope me-1"></i>Email</th>
                            <th><i class="fas fa-cogs me-1"></i>Azioni</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><span class="badge bg-secondary">001</span></td>
                            <td><strong>Mario Rossi</strong></td>
                            <td><EMAIL></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary"><i class="fas fa-edit"></i></button>
                                <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                        <tr>
                            <td><span class="badge bg-primary">002</span></td>
                            <td><strong>Luigi Verdi</strong></td>
                            <td><EMAIL></td>
                            <td>
                                <button class="btn btn-sm btn-outline-warning"><i class="fas fa-eye"></i></button>
                                <button class="btn btn-sm btn-outline-info"><i class="fas fa-download"></i></button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Test Alert -->
        <div class="test-section">
            <div class="test-title">⚠️ Test Alert</div>
            <div class="alert alert-primary"><i class="fas fa-info-circle me-2"></i>Alert Primary - Testo importante</div>
            <div class="alert alert-secondary"><i class="fas fa-tag me-2"></i>Alert Secondary - Informazione secondaria</div>
            <div class="alert alert-success"><i class="fas fa-check-circle me-2"></i>Alert Success - Operazione completata</div>
            <div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>Alert Warning - Attenzione richiesta</div>
            <div class="alert alert-danger"><i class="fas fa-times-circle me-2"></i>Alert Danger - Errore critico</div>
            <div class="alert alert-info"><i class="fas fa-info me-2"></i>Alert Info - Informazione utile</div>
        </div>

        <!-- Test Badge -->
        <div class="test-section">
            <div class="test-title">🏷️ Test Badge</div>
            <span class="badge bg-primary me-2"><i class="fas fa-user me-1"></i>Primary</span>
            <span class="badge bg-secondary me-2"><i class="fas fa-tag me-1"></i>Secondary</span>
            <span class="badge bg-success me-2"><i class="fas fa-check me-1"></i>Success</span>
            <span class="badge bg-warning me-2"><i class="fas fa-warning me-1"></i>Warning</span>
            <span class="badge bg-danger me-2"><i class="fas fa-times me-1"></i>Danger</span>
            <span class="badge bg-info me-2"><i class="fas fa-info me-1"></i>Info</span>
            <span class="badge bg-light me-2"><i class="fas fa-lightbulb me-1"></i>Light</span>
            <span class="badge bg-dark me-2"><i class="fas fa-moon me-1"></i>Dark</span>
        </div>

        <!-- Test Controlli Tema -->
        <div class="test-section">
            <div class="test-title">🎨 Controlli Tema</div>
            <button class="btn btn-primary me-2" onclick="setTheme('maritime')">Tema Marittimo</button>
            <button class="btn btn-secondary me-2" onclick="setTheme('dark')">Tema Scuro Professionale</button>
            <button class="btn btn-light me-2" onclick="setTheme('light')">Tema Chiaro Professionale</button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/theme-manager.js"></script>
    <script src="/static/js/global-themes.js"></script>
    <script>
        function setTheme(theme) {
            if (window.SNIPThemes) {
                console.log('🎨 Richiesta cambio tema a:', theme);
                window.SNIPThemes.change(theme);
                // La pagina si ricaricherà automaticamente
            } else {
                console.warn('SNIPThemes non ancora caricato, riprovo...');
                setTimeout(() => setTheme(theme), 100);
            }
        }

        // Inizializza tema al caricamento
        document.addEventListener('DOMContentLoaded', function() {
            // Aspetta che SNIPThemes sia caricato
            const waitForThemes = () => {
                if (window.SNIPThemes) {
                    console.log('✅ SNIPThemes caricato, tema corrente:', window.SNIPThemes.getCurrentTheme());
                    // Imposta tema scuro per questa pagina di test
                    if (window.SNIPThemes.getCurrentTheme() !== 'dark') {
                        window.SNIPThemes.change('dark');
                    }
                } else {
                    setTimeout(waitForThemes, 100);
                }
            };
            waitForThemes();
        });
        
        // Forza tema scuro all'avvio
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                setTheme('dark');
            }, 500);
        });
    </script>
</body>
</html>
