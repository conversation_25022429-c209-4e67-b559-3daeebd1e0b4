#!/usr/bin/env python3
"""
Script per creare la tabella USER_SESSIONS mancante
"""

import psycopg2
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_user_sessions_table():
    """Crea la tabella USER_SESSIONS nel database"""
    try:
        # Connessione al database
        conn = psycopg2.connect(
            host='localhost',
            database='AGENTE',
            user='re77',
            password='271077',
            connect_timeout=5
        )
        conn.autocommit = False
        cursor = conn.cursor()
        
        logger.info("✅ Connesso al database")
        
        # Verifica se la tabella esiste già
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'USER_SESSIONS'
            );
        """)
        
        table_exists = cursor.fetchone()[0]
        
        if table_exists:
            logger.info("ℹ️ La tabella USER_SESSIONS esiste già")
            return True
        
        logger.info("🔧 Creazione tabella USER_SESSIONS...")
        
        # Crea la tabella USER_SESSIONS
        cursor.execute("""
            CREATE TABLE "USER_SESSIONS" (
                id SERIAL PRIMARY KEY,
                user_id INTEGER NOT NULL,
                session_token VARCHAR(1000) UNIQUE NOT NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                last_activity TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP WITH TIME ZONE,
                
                -- Indici per performance
                CONSTRAINT fk_user_sessions_user_id 
                    FOREIGN KEY (user_id) REFERENCES "AGENTE"(id_user) ON DELETE CASCADE
            );
        """)
        
        # Crea indici per performance
        cursor.execute("""
            CREATE INDEX idx_user_sessions_user_id ON "USER_SESSIONS"(user_id);
        """)
        
        cursor.execute("""
            CREATE INDEX idx_user_sessions_session_token ON "USER_SESSIONS"(session_token);
        """)
        
        cursor.execute("""
            CREATE INDEX idx_user_sessions_is_active ON "USER_SESSIONS"(is_active);
        """)
        
        cursor.execute("""
            CREATE INDEX idx_user_sessions_last_activity ON "USER_SESSIONS"(last_activity);
        """)
        
        cursor.execute("""
            CREATE INDEX idx_user_sessions_expires_at ON "USER_SESSIONS"(expires_at);
        """)
        
        # Commit delle modifiche
        conn.commit()
        
        logger.info("✅ Tabella USER_SESSIONS creata con successo!")
        logger.info("✅ Indici creati per ottimizzare le performance")
        
        # Verifica finale
        cursor.execute("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'USER_SESSIONS' 
            ORDER BY ordinal_position;
        """)
        
        columns = cursor.fetchall()
        logger.info("📋 Struttura tabella USER_SESSIONS:")
        for col in columns:
            name, data_type, nullable = col
            logger.info(f"   • {name}: {data_type} ({'NULL' if nullable == 'YES' else 'NOT NULL'})")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore durante la creazione della tabella: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
        
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    logger.info("🚀 CREAZIONE TABELLA USER_SESSIONS")
    logger.info("=" * 50)
    
    success = create_user_sessions_table()
    
    if success:
        logger.info("\n🎉 OPERAZIONE COMPLETATA CON SUCCESSO!")
        logger.info("La tabella USER_SESSIONS è ora disponibile per l'applicazione.")
    else:
        logger.error("\n❌ OPERAZIONE FALLITA!")
        logger.error("Controlla i log per i dettagli dell'errore.")
    
    exit(0 if success else 1)
