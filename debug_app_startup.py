#!/usr/bin/env python3

import sys
import traceback
import os

def debug_app_startup():
    print("🔍 DEBUG AVVIO APP - Controllo errori...")
    
    # 1. Test import delle dipendenze principali
    print("\n1️⃣ Test import dipendenze...")
    try:
        import fastapi
        print("✅ FastAPI importato")
    except Exception as e:
        print(f"❌ Errore FastAPI: {e}")
        return False
    
    try:
        import uvicorn
        print("✅ Uvicorn importato")
    except Exception as e:
        print(f"❌ Errore Uvicorn: {e}")
        return False
    
    try:
        import psycopg2
        print("✅ psycopg2 importato")
    except Exception as e:
        print(f"❌ Errore psycopg2: {e}")
        return False
    
    try:
        import sqlalchemy
        print("✅ SQLAlchemy importato")
    except Exception as e:
        print(f"❌ Errore SQLAlchemy: {e}")
        return False
    
    # 2. Test connessione database
    print("\n2️⃣ Test connessione database...")
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='AGENTE',
            user='re77',
            password='271077',
            connect_timeout=3
        )
        print("✅ Connessione database OK")
        conn.close()
    except Exception as e:
        print(f"❌ Errore database: {e}")
        return False
    
    # 3. Test import moduli locali
    print("\n3️⃣ Test import moduli locali...")
    try:
        from config import settings
        print("✅ Config importato")
    except Exception as e:
        print(f"❌ Errore config: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return False
    
    try:
        from database import get_db
        print("✅ Database module importato")
    except Exception as e:
        print(f"❌ Errore database module: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return False
    
    try:
        from models import Agente
        print("✅ Models importato")
    except Exception as e:
        print(f"❌ Errore models: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return False
    
    try:
        from session_manager import session_manager
        print("✅ Session manager importato")
    except Exception as e:
        print(f"❌ Errore session manager: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return False
    
    # 4. Test sintassi main.py
    print("\n4️⃣ Test sintassi main.py...")
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            code = f.read()
        compile(code, 'main.py', 'exec')
        print("✅ Sintassi main.py OK")
    except SyntaxError as e:
        print(f"❌ Errore sintassi main.py: {e}")
        print(f"Riga {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"❌ Errore lettura main.py: {e}")
        return False
    
    # 5. Test creazione app FastAPI
    print("\n5️⃣ Test creazione app FastAPI...")
    try:
        from fastapi import FastAPI
        app = FastAPI()
        print("✅ App FastAPI creata")
    except Exception as e:
        print(f"❌ Errore creazione app: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return False
    
    # 6. Test file necessari
    print("\n6️⃣ Test file necessari...")
    required_files = [
        'main.py', 'config.py', 'database.py', 'models.py', 
        'session_manager.py', 'admin_routes.py', 'notification_system.py'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} presente")
        else:
            print(f"❌ {file} mancante")
            return False
    
    # 7. Test directory necessarie
    print("\n7️⃣ Test directory necessarie...")
    required_dirs = ['templates', 'static']
    
    for dir in required_dirs:
        if os.path.exists(dir):
            print(f"✅ {dir}/ presente")
        else:
            print(f"❌ {dir}/ mancante")
            return False
    
    print("\n✅ Tutti i test passati! L'app dovrebbe avviarsi.")
    return True

if __name__ == "__main__":
    success = debug_app_startup()
    if not success:
        print("\n❌ Errori trovati - l'app non può avviarsi")
        sys.exit(1)
    else:
        print("\n🚀 Provo ad avviare l'app...")
        try:
            # Import e avvio dell'app
            import main
        except Exception as e:
            print(f"❌ Errore durante l'avvio: {e}")
            print(f"Traceback completo:")
            traceback.print_exc()
            sys.exit(1)
