<!-- CSS Dropdown Moderni -->
<link rel="stylesheet" href="{{ url_for('static', path='css/modern-dropdowns.css') }}">
<!-- CSS Fix Contrasto Icone -->
<link rel="stylesheet" href="{{ url_for('static', path='css/contrast-fixes.css') }}">
<!-- CSS Temi Globali -->
<link rel="stylesheet" href="{{ url_for('static', path='css/global-themes.css') }}">
<!-- CSS Tema Bianco Professionale -->
<link rel="stylesheet" href="{{ url_for('static', path='css/theme-light-professional.css') }}">
<!-- CSS Tema Scuro Professionale -->
<link rel="stylesheet" href="{{ url_for('static', path='css/theme-dark-professional.css') }}">

<!-- Navbar Principale SNIP -->
<nav class="navbar navbar-expand-lg snip-navbar">
    <div class="container-fluid">
        <!-- Brand/Logo Migliorato -->
        {% set logo_reparto_url = current_user.reparto.value.lower() if current_user.reparto.value else current_user.reparto.lower() %}
        {% if logo_reparto_url == 'contabilita\'' %}
            {% set logo_reparto_url = 'contabilita' %}
        {% endif %}
        <a class="navbar-brand snip-logo" href="/dashboard/{{ logo_reparto_url }}">
            <div class="logo-container">
                <div class="logo-icon">
                    <div class="ship-container">
                        <i class="fas fa-ship ship-icon"></i>
                        <div class="waves">
                            <div class="wave wave1"></div>
                            <div class="wave wave2"></div>
                            <div class="wave wave3"></div>
                        </div>
                    </div>
                    <i class="fas fa-anchor anchor-icon"></i>
                </div>
                <div class="logo-text">
                    <div class="brand-main">
                        <span class="brand-s">S</span><span class="brand-n">N</span><span class="brand-i">I</span><span class="brand-p">P</span>
                    </div>
                    <div class="brand-subtitle">Sistema Navale Integrato Portuale</div>
                    <div class="brand-version" id="app-version-display">v2.0</div>
                </div>
            </div>
        </a>
        
        <!-- Toggle button per mobile -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <!-- Menu principale -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <!-- Menu centrale -->
            <ul class="navbar-nav me-auto">
                <!-- Dashboard principale -->
                <li class="nav-item">
                    {% set nav_reparto_url = current_user.reparto.value.lower() if current_user.reparto.value else current_user.reparto.lower() %}
                    {% if nav_reparto_url == 'contabilita\'' %}
                        {% set nav_reparto_url = 'contabilita' %}
                    {% endif %}
                    <a class="nav-link" href="/dashboard/{{ nav_reparto_url }}">
                        <i class="fas fa-tachometer-alt me-1"></i>
                        Dashboard
                    </a>
                </li>
                
                <!-- Menu specifici per reparto -->
                {% if current_user.reparto.value == 'OPERATIVO' or current_user.reparto == 'OPERATIVO' %}
                <!-- Menu NAVI -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="naviDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-ship me-1"></i>
                        NAVI
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/operativo/sof/da-realizzare" data-description="Gestisci tutti i viaggi delle navi">
                            <i class="fas fa-route"></i>
                            <span>Gestione Viaggi</span>
                        </a></li>
                        <li><a class="dropdown-item" href="/operativo/navi" data-description="Database completo delle navi">
                            <i class="fas fa-ship"></i>
                            <span>Gestione Navi</span>
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/operativo/armatori" data-description="Gestione compagnie armatoriali">
                            <i class="fas fa-handshake"></i>
                            <span>Armatori</span>
                        </a></li>
                        <li><a class="dropdown-item" href="/operativo/porti" data-description="Database porti e terminal">
                            <i class="fas fa-anchor"></i>
                            <span>Porti</span>
                        </a></li>
                    </ul>
                </li>

                <!-- Menu SOF -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="sofDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-file-alt me-1"></i>
                        SOF
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/operativo/sof/da-realizzare" data-description="SOF in attesa di completamento">
                            <i class="fas fa-file-plus"></i>
                            <span>SOF da Realizzare</span>
                        </a></li>
                        <li><a class="dropdown-item" href="/operativo/sof/realizzati" data-description="Archivio SOF completati">
                            <i class="fas fa-file-check"></i>
                            <span>SOF Realizzati</span>
                        </a></li>
                        <li><a class="dropdown-item" href="/operativo/sof/archiviati" data-description="SOF archiviati e storici">
                            <i class="fas fa-archive"></i>
                            <span>SOF Archiviati</span>
                        </a></li>
                    </ul>
                </li>
                {% endif %}
                
                {% if current_user.reparto.value == 'AMMINISTRAZIONE' or current_user.reparto == 'AMMINISTRAZIONE' %}
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-building me-1"></i>
                        Amministrazione
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/fatturazione"><i class="fas fa-file-invoice me-2"></i>Fatturazione</a></li>
                        <li><a class="dropdown-item" href="/contratti"><i class="fas fa-handshake me-2"></i>Contratti</a></li>
                        <li><a class="dropdown-item" href="/documenti"><i class="fas fa-folder me-2"></i>Documenti</a></li>
                    </ul>
                </li>
                {% endif %}
                
                {% if current_user.reparto.value == 'SHORTSEA' or current_user.reparto == 'SHORTSEA' %}
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="shortseaDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-anchor me-1"></i>
                        Shortsea
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/shortsea/rotte"><i class="fas fa-map-marked-alt me-2"></i>Rotte Shortsea</a></li>
                        <li><a class="dropdown-item" href="/shortsea/planning"><i class="fas fa-calendar-alt me-2"></i>Planning</a></li>
                        <li><a class="dropdown-item" href="/shortsea/tracking"><i class="fas fa-satellite me-2"></i>Tracking</a></li>
                    </ul>
                </li>
                {% endif %}
                
                {% if current_user.reparto.value == 'CONTABILITA' or current_user.reparto == 'CONTABILITA' %}
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="contabilitaDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-calculator me-1"></i>
                        Contabilità
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/contabilita/bilanci"><i class="fas fa-chart-line me-2"></i>Bilanci</a></li>
                        <li><a class="dropdown-item" href="/contabilita/costi"><i class="fas fa-coins me-2"></i>Analisi Costi</a></li>
                        <li><a class="dropdown-item" href="/contabilita/report"><i class="fas fa-file-excel me-2"></i>Report</a></li>
                    </ul>
                </li>
                {% endif %}
                

            </ul>
            
            <!-- Menu utente spostato più a sinistra -->
            <div class="navbar-nav user-section-left navbar-right-section">
                <!-- Data attuale -->
                <div class="nav-item me-3">
                    <span class="nav-link current-date" id="currentDate">
                        <i class="fas fa-calendar-alt me-2"></i>
                        <span id="dateText"></span>
                    </span>
                </div>



                <!-- Notifiche Real-Time -->
                <div class="nav-item dropdown me-3">
                    <a class="nav-link notification-bell" href="#" id="notificationDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge" id="notificationBadge" style="display: none;">0</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end notification-dropdown" id="notificationDropdownMenu">
                        <li class="dropdown-header">
                            <i class="fas fa-bell me-2"></i>Notifiche {{ current_user.reparto.value }}
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li id="notificationLoading" class="text-center py-3">
                            <i class="fas fa-spinner fa-spin"></i> Caricamento...
                        </li>
                        <li id="noNotifications" style="display: none;" class="text-center py-3 text-muted">
                            <i class="fas fa-bell-slash me-2"></i>Nessuna notifica
                        </li>
                        <div id="notificationsList"></div>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-center" href="/notifiche">
                            <i class="fas fa-eye me-2"></i>Vedi tutte le notifiche
                        </a></li>
                    </ul>
                </div>
                
                <!-- Menu utente -->
                {% include 'components/user_menu.html' %}
            </div>
        </div>
    </div>
</nav>

<style>
.snip-navbar {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 12px 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

/* ===== LOGO SNIP MIGLIORATO ===== */
.snip-logo {
    color: white !important;
    text-decoration: none !important;
    padding: 8px 16px !important;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.snip-logo:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    overflow: hidden;
}

.ship-container {
    position: relative;
    z-index: 2;
}

.ship-icon {
    font-size: 1.4rem;
    color: white;
    animation: shipFloat 3s ease-in-out infinite;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.anchor-icon {
    position: absolute;
    bottom: 2px;
    right: 2px;
    font-size: 0.8rem;
    color: #f39c12;
    animation: anchorSway 2s ease-in-out infinite;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.waves {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 20px;
    overflow: hidden;
}

.wave {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 200%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    border-radius: 50%;
    animation: waveMove 4s linear infinite;
}

.wave1 {
    animation-delay: 0s;
    opacity: 0.7;
}

.wave2 {
    animation-delay: -1s;
    opacity: 0.5;
}

.wave3 {
    animation-delay: -2s;
    opacity: 0.3;
}

.logo-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.brand-main {
    display: flex;
    align-items: center;
    gap: 2px;
    font-family: 'Arial Black', Arial, sans-serif;
    font-size: 1.8rem;
    font-weight: 900;
    letter-spacing: 2px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.brand-s {
    color: #e74c3c;
    animation: letterPulse 2s ease-in-out infinite;
    animation-delay: 0s;
}

.brand-n {
    color: #3498db;
    animation: letterPulse 2s ease-in-out infinite;
    animation-delay: 0.2s;
}

.brand-i {
    color: #2ecc71;
    animation: letterPulse 2s ease-in-out infinite;
    animation-delay: 0.4s;
}

.brand-p {
    color: #f39c12;
    animation: letterPulse 2s ease-in-out infinite;
    animation-delay: 0.6s;
}

.brand-subtitle {
    font-size: 0.7rem;
    opacity: 0.9;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    letter-spacing: 0.5px;
    line-height: 1;
}

.brand-version {
    font-size: 0.6rem;
    color: #f39c12;
    font-weight: 600;
    background: rgba(243, 156, 18, 0.2);
    padding: 1px 6px;
    border-radius: 8px;
    align-self: flex-start;
    border: 1px solid rgba(243, 156, 18, 0.3);
    text-shadow: none;
}

/* ===== ANIMAZIONI ===== */
@keyframes shipFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-3px) rotate(1deg); }
}

@keyframes anchorSway {
    0%, 100% { transform: rotate(-5deg); }
    50% { transform: rotate(5deg); }
}

@keyframes waveMove {
    0% { transform: translateX(-50%); }
    100% { transform: translateX(0%); }
}

@keyframes letterPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 8px 16px !important;
    border-radius: 8px;
    transition: all 0.3s ease;
    margin: 0 2px;
}

.navbar-nav .nav-link:hover {
    color: white !important;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    color: white !important;
    background: rgba(255, 255, 255, 0.2);
}

.admin-menu {
    background: rgba(255, 193, 7, 0.2) !important;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.admin-menu:hover {
    background: rgba(255, 193, 7, 0.3) !important;
}

/* ===== DROPDOWN ULTRA-MODERNI ===== */
.dropdown-menu {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.15),
        0 8px 25px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    margin-top: 12px;
    padding: 16px 8px;
    min-width: 280px;
    animation: dropdownSlideIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform-origin: top center;
}

@keyframes dropdownSlideIn {
    0% {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.dropdown-item {
    color: #2c3e50;
    padding: 14px 20px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-radius: 12px;
    margin: 4px 0;
    position: relative;
    overflow: hidden;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 12px;
    border: 1px solid transparent;
    text-decoration: none;
}

.dropdown-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.dropdown-item:hover::before {
    left: 100%;
}

.dropdown-item:hover {
    color: white;
    transform: translateX(8px) translateY(-2px);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.15),
        0 4px 12px rgba(0, 0, 0, 0.1);
}

.dropdown-item i {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.dropdown-item:hover i {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1) rotate(5deg);
}

/* ===== MENU NAVI E SOF ULTRA-MODERNI ===== */
#naviDropdown {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.2) 0%, rgba(41, 128, 185, 0.2) 100%) !important;
    border: 1px solid rgba(52, 152, 219, 0.4);
    position: relative;
    overflow: hidden;
}

#naviDropdown::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

#naviDropdown:hover::before {
    left: 100%;
}

#naviDropdown:hover {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.4) 0%, rgba(41, 128, 185, 0.4) 100%) !important;
    color: white !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

#sofDropdown {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.2) 0%, rgba(39, 174, 96, 0.2) 100%) !important;
    border: 1px solid rgba(46, 204, 113, 0.4);
    position: relative;
    overflow: hidden;
}

#sofDropdown::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

#sofDropdown:hover::before {
    left: 100%;
}

#sofDropdown:hover {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.4) 0%, rgba(39, 174, 96, 0.4) 100%) !important;
    color: white !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(46, 204, 113, 0.3);
}

/* Dropdown NAVI - Tema Blu Oceano */
#naviDropdown + .dropdown-menu {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.05) 0%, rgba(255, 255, 255, 0.98) 100%);
    border: 2px solid rgba(52, 152, 219, 0.2);
}

#naviDropdown + .dropdown-menu::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 16px;
    height: 16px;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1) 0%, rgba(255, 255, 255, 0.9) 100%);
    border: 2px solid rgba(52, 152, 219, 0.2);
    border-bottom: none;
    border-right: none;
    transform: translateX(-50%) rotate(45deg);
}

#naviDropdown + .dropdown-menu .dropdown-item:hover {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    box-shadow:
        0 8px 25px rgba(52, 152, 219, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

#naviDropdown + .dropdown-menu .dropdown-item:hover i {
    background: rgba(255, 255, 255, 0.4);
    color: white;
}

/* Dropdown SOF - Tema Verde Natura */
#sofDropdown + .dropdown-menu {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.05) 0%, rgba(255, 255, 255, 0.98) 100%);
    border: 2px solid rgba(46, 204, 113, 0.2);
}

#sofDropdown + .dropdown-menu::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 16px;
    height: 16px;
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.1) 0%, rgba(255, 255, 255, 0.9) 100%);
    border: 2px solid rgba(46, 204, 113, 0.2);
    border-bottom: none;
    border-right: none;
    transform: translateX(-50%) rotate(45deg);
}

#sofDropdown + .dropdown-menu .dropdown-item:hover {
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    box-shadow:
        0 8px 25px rgba(46, 204, 113, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

#sofDropdown + .dropdown-menu .dropdown-item:hover i {
    background: rgba(255, 255, 255, 0.4);
    color: white;
}

/* Menu attivo */
.nav-link.active-menu {
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
}

/* ===== MICRO-INTERAZIONI AVANZATE ===== */
.dropdown-item {
    position: relative;
}

.dropdown-item::after {
    content: '';
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    background: currentColor;
    border-radius: 50%;
    opacity: 0;
    transition: all 0.3s ease;
}

.dropdown-item:hover::after {
    opacity: 0.6;
    transform: translateY(-50%) translateX(4px);
}

/* Separatori eleganti */
.dropdown-divider {
    height: 1px;
    margin: 12px 16px;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
    border: none;
}

/* Effetto ripple sui click */
.dropdown-item {
    overflow: hidden;
}

.dropdown-item:active {
    transform: scale(0.98);
}

/* Tooltip per descrizioni */
.dropdown-item[data-description]:hover::before {
    content: attr(data-description);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    margin-left: 8px;
    z-index: 1000;
    opacity: 0;
    animation: tooltipFadeIn 0.3s ease forwards;
}

@keyframes tooltipFadeIn {
    to {
        opacity: 1;
    }
}

/* Badge rimossi - CSS pulito */

/* Animazione ripple */
@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

/* Responsive dropdown */
@media (max-width: 768px) {
    .dropdown-menu {
        min-width: 250px;
        margin-top: 8px;
        border-radius: 16px;
    }

    .dropdown-item {
        padding: 12px 16px;
        font-size: 14px;
    }

    .dropdown-item i {
        width: 20px;
        height: 20px;
        font-size: 14px;
    }
}

/* Animazioni badge rimosse */

/* ===== DATA ATTUALE ===== */
.current-date {
    color: white !important;
    font-weight: 500;
    font-size: 0.9rem;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    padding: 8px 16px !important;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    cursor: default;
}

.current-date:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.current-date i {
    color: rgba(255, 255, 255, 0.8);
}

/* ===== NOTIFICHE ===== */
.notification-bell {
    position: relative;
    font-size: 1.2rem;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.notification-dropdown {
    min-width: 350px;
    max-width: 400px;
    width: 380px;
    /* FORZA il posizionamento all'interno della viewport */
    position: fixed !important;
    top: 60px !important;
    right: 20px !important;
    left: auto !important;
    transform: none !important;
    max-height: calc(100vh - 80px) !important;
    overflow-y: auto !important;
    z-index: 9999 !important;
    /* Assicura che non esca mai dai bordi */
    box-sizing: border-box !important;
}

/* Media query per schermi più piccoli */
@media (max-width: 1200px) {
    .notification-dropdown {
        width: 350px !important;
        right: 15px !important;
    }
}

@media (max-width: 1000px) {
    .notification-dropdown {
        width: 320px !important;
        right: 10px !important;
    }
}

@media (max-width: 800px) {
    .notification-dropdown {
        width: calc(100vw - 40px) !important;
        max-width: 300px !important;
        right: 20px !important;
        left: 20px !important;
    }
}

/* Sovrascrive completamente gli stili Bootstrap */
.dropdown-menu.notification-dropdown {
    position: fixed !important;
    top: 60px !important;
    right: 20px !important;
    left: auto !important;
    transform: none !important;
    margin: 0 !important;
    inset: auto !important;
}

.notification-item {
    padding: 8px 12px;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

.notification-title {
    font-weight: 600;
    font-size: 0.8rem;
    margin-bottom: 2px;
    color: #495057;
}

.notification-message {
    font-size: 0.75rem;
    color: #6c757d;
    line-height: 1.3;
    margin-bottom: 3px;
    max-height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.notification-time {
    font-size: 0.75rem;
    color: #adb5bd;
    text-align: right;
}

/* ===== POSIZIONAMENTO BADGE UTENTE A SINISTRA ===== */
.user-section-left {
    margin-left: auto !important;
    margin-right: 150px !important;  /* Sposta 150px a sinistra dal bordo destro */
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;

    /* Debug: bordo per visualizzare il posizionamento */
    border: 2px dashed rgba(255, 255, 0, 0.7) !important;
    padding: 5px 10px !important;
    border-radius: 8px !important;
    background: rgba(255, 255, 0, 0.1) !important;
}

/* Responsive per badge utente */
@media (max-width: 1400px) {
    .user-section-left {
        margin-right: 120px !important;  /* Meno spostamento su schermi medi */
    }
}

@media (max-width: 1200px) {
    .user-section-left {
        margin-right: 100px !important;  /* Ancora meno su laptop */
    }
}

@media (max-width: 991px) {
    .user-section-left {
        margin-right: 80px !important;   /* Ridotto su tablet */
    }
}

@media (max-width: 768px) {
    .user-section-left {
        margin-right: 60px !important;   /* Minimo su mobile */
    }
}

@media (max-width: 576px) {
    .user-section-left {
        margin-right: 40px !important;   /* Molto ridotto su mobile piccolo */
    }
}

@media (max-width: 400px) {
    .user-section-left {
        margin-right: 20px !important;   /* Minimo assoluto */
    }
}

.navbar-toggler {
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 4px 8px;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* ===== RESPONSIVE LOGO ===== */
@media (max-width: 991px) {
    .brand-subtitle {
        display: none;
    }

    .brand-version {
        display: none;
    }

    .logo-icon {
        width: 45px;
        height: 45px;
    }

    .brand-main {
        font-size: 1.6rem;
    }

    .navbar-nav .nav-link {
        margin: 2px 0;
    }

    .notification-dropdown {
        /* Mobile: dropdown a tutta larghezza con margini */
        position: fixed !important;
        top: 60px !important;
        left: 10px !important;
        right: 10px !important;
        width: auto !important;
        max-width: none !important;
        min-width: auto !important;
        max-height: calc(100vh - 80px) !important;
        z-index: 9999 !important;
        transform: none !important;
    }
}

@media (max-width: 768px) {
    .snip-logo {
        padding: 6px 12px !important;
    }

    .logo-container {
        gap: 8px;
    }

    .logo-icon {
        width: 40px;
        height: 40px;
    }

    .ship-icon {
        font-size: 1.2rem;
    }

    .anchor-icon {
        font-size: 0.7rem;
    }

    .brand-main {
        font-size: 1.4rem;
        letter-spacing: 1px;
    }

    .brand-subtitle {
        font-size: 0.6rem;
    }

    .notification-bell {
        font-size: 1.1rem;
    }
}

@media (max-width: 576px) {
    .snip-logo {
        padding: 4px 8px !important;
    }

    .logo-container {
        gap: 6px;
    }

    .logo-icon {
        width: 35px;
        height: 35px;
    }

    .ship-icon {
        font-size: 1rem;
    }

    .brand-main {
        font-size: 1.2rem;
        letter-spacing: 0.5px;
    }

    .brand-subtitle {
        display: none;
    }
}

/* Stili aggiuntivi per notifiche real-time */
.notification-item {
    padding: 8px 16px;
    margin: 2px 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    border-left: 3px solid transparent;
}

.notification-item:hover {
    background: rgba(0,123,255,0.1);
    transform: translateX(2px);
}

.notification-item.unread {
    background: rgba(255,193,7,0.1);
    border-left-color: #ffc107;
}

.notification-item.info { border-left-color: #17a2b8; }
.notification-item.warning { border-left-color: #ffc107; }
.notification-item.success { border-left-color: #28a745; }
.notification-item.error { border-left-color: #dc3545; }
.notification-item.urgent { border-left-color: #6f42c1; }

.notification-title {
    font-weight: 600;
    font-size: 0.8rem;
    margin-bottom: 1px;
}

.notification-message {
    font-size: 0.75rem;
    color: #6c757d;
    margin-bottom: 2px;
}

.notification-time {
    font-size: 0.65rem;
    color: #adb5bd;
}

.notification-badge.pulse {
    animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* ===== SEZIONE DESTRA NAVBAR - TEMI ===== */
.navbar-right-section {
    padding: 8px 16px !important;
    border-radius: 25px !important;
    margin-left: 12px !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(15px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Tema Marittimo (default) */
body.theme-maritime .navbar-right-section,
body:not([class*="theme-"]) .navbar-right-section {
    background: linear-gradient(135deg, rgba(30, 60, 114, 0.8) 0%, rgba(42, 82, 152, 0.8) 100%) !important;
    border-color: rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 4px 20px rgba(30, 60, 114, 0.3) !important;
}

/* Tema Scuro */
body.theme-dark .navbar-right-section {
    background: linear-gradient(135deg, rgba(44, 62, 80, 0.9) 0%, rgba(52, 73, 94, 0.9) 100%) !important;
    border-color: rgba(255, 255, 255, 0.15) !important;
    box-shadow: 0 4px 20px rgba(44, 62, 80, 0.4) !important;
}

/* Tema Chiaro */
body.theme-light .navbar-right-section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.95) 100%) !important;
    border-color: rgba(0, 0, 0, 0.1) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

/* Adattamenti per il tema chiaro - testo scuro */
body.theme-light .navbar-right-section .nav-link {
    color: #343a40 !important;
}

body.theme-light .navbar-right-section .nav-link:hover {
    color: #212529 !important;
    background: rgba(0, 0, 0, 0.05) !important;
}

body.theme-light .navbar-right-section .current-date {
    color: #495057 !important;
}

body.theme-light .navbar-right-section .notification-bell {
    color: #495057 !important;
}

body.theme-light .navbar-right-section .notification-badge {
    background: #dc3545 !important;
    color: white !important;
}
</style>

<!-- Script per gestione refresh centralizzato -->
<script src="/static/js/refresh-config.js"></script>

<script>
// Sistema notifiche real-time per navbar
class NotificationSystem {
    constructor() {
        this.updateInterval = null;
        this.lastUnreadCount = 0;
        this.init();
    }

    init() {
        console.log('🔔 Inizializzazione sistema notifiche...');

        // Test connettività API prima di inizializzare
        this.testAPIConnectivity().then(isConnected => {
            if (isConnected) {
                console.log('✅ API raggiungibili, avvio sistema...');

                // Carica notifiche iniziali
                this.loadNotifications();
                this.updateUnreadCount();

                // Usa il sistema centralizzato di refresh
                if (window.RefreshManager) {
                    window.RefreshManager.register('notifications', () => {
                        console.log('🔄 Auto-refresh notifiche...');
                        this.loadNotifications();
                        this.updateUnreadCount();
                    }, 'NOTIFICATIONS');
                } else {
                    // Fallback se RefreshManager non è disponibile
                    this.updateInterval = setInterval(() => {
                        console.log('🔄 Auto-refresh notifiche (fallback)...');
                        this.loadNotifications();
                        this.updateUnreadCount();
                    }, 120000);
                }

                // Gestisci click su dropdown
                document.getElementById('notificationDropdown').addEventListener('click', () => {
                    console.log('🔔 Click campanella - caricamento notifiche...');
                    this.loadNotifications();

                    // Posiziona il dropdown per rimanere all'interno della viewport
                    setTimeout(() => {
                        this.adjustDropdownPosition();
                    }, 100);
                });

                // Listener per ridimensionamento finestra
                window.addEventListener('resize', () => {
                    console.log('🔄 Finestra ridimensionata, regolo dropdown...');
                    setTimeout(() => {
                        this.adjustDropdownPosition();
                    }, 100);
                });

                console.log('✅ Sistema notifiche inizializzato');
            } else {
                console.error('❌ API non raggiungibili, sistema notifiche disabilitato');
                // Nascondi campanella se API non funzionano
                const badge = document.getElementById('notificationBadge');
                if (badge) badge.style.display = 'none';
            }
        });
    }

    async testAPIConnectivity() {
        try {
            console.log('🔍 Test connettività API...');

            const response = await fetch('/api/notifications/unread-count', {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            console.log('📊 Test API response: ' + response.status);

            // Considera connesso se otteniamo una risposta (anche 401)
            return response.status === 200 || response.status === 401;

        } catch (error) {
            console.error('❌ Test API fallito:', error);
            return false;
        }
    }

    async updateUnreadCount() {
        try {
            const response = await fetch('/api/notifications/unread-count', {
                method: 'GET',
                credentials: 'include',  // IMPORTANTE: Include i cookie di autenticazione
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            // Gestisci errori di autenticazione
            if (response.status === 401) {
                console.log('🔐 Utente non autenticato - nascondo badge notifiche');
                const badge = document.getElementById('notificationBadge');
                badge.style.display = 'none';
                return;
            }

            if (!response.ok) {
                throw new Error('HTTP ' + response.status + ': ' + response.statusText);
            }

            const data = await response.json();

            if (data.success) {
                const badge = document.getElementById('notificationBadge');
                const count = data.unread_count;

                console.log('🔔 API unread-count risposta: ' + count);
                console.log('🔍 Debug API unread-count:', data);

                if (count > 0) {
                    badge.textContent = count > 99 ? '99+' : count;
                    badge.style.display = 'flex';

                    // Anima se il conteggio è aumentato
                    if (count > this.lastUnreadCount) {
                        badge.classList.add('pulse');
                        setTimeout(() => badge.classList.remove('pulse'), 2000);
                    }
                } else {
                    badge.style.display = 'none';
                }

                this.lastUnreadCount = count;
            } else {
                console.error('❌ Errore API unread-count:', data.message);
            }
        } catch (error) {
            console.error('❌ Errore aggiornamento conteggio notifiche:', error);
        }
    }

    async loadNotifications() {
        const loadingEl = document.getElementById('notificationLoading');
        const noNotificationsEl = document.getElementById('noNotifications');
        const listEl = document.getElementById('notificationsList');

        // Mostra loading
        loadingEl.style.display = 'block';
        noNotificationsEl.style.display = 'none';
        listEl.innerHTML = '';

        try {
            const response = await fetch('/api/notifications/recent', {
                method: 'GET',
                credentials: 'include',  // IMPORTANTE: Include i cookie di autenticazione
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            // Gestisci errori di autenticazione
            if (response.status === 401) {
                console.log('🔐 Utente non autenticato - nascondo notifiche');
                noNotificationsEl.innerHTML = '<div class="text-center py-3 text-muted"><i class="fas fa-lock me-2"></i>Effettua il login per vedere le notifiche non lette</div>';
                noNotificationsEl.style.display = 'block';
                return;
            }

            if (!response.ok) {
                throw new Error('HTTP ' + response.status + ': ' + response.statusText);
            }

            const data = await response.json();

            if (data.success) {
                const allNotifications = data.notifications;

                console.log('📋 Notifiche totali caricate: ' + allNotifications.length);
                console.log('🔍 Debug notifiche:', allNotifications);

                // Filtra solo le notifiche NON LETTE
                const unreadNotifications = allNotifications.filter(notif => !notif.is_read);

                console.log('🔔 Notifiche non lette: ' + unreadNotifications.length);
                console.log('🔍 Debug notifiche non lette:', unreadNotifications);

                if (unreadNotifications.length === 0) {
                    noNotificationsEl.innerHTML = '<div class="text-center py-3 text-muted"><i class="fas fa-check-circle me-2"></i>Tutte le notifiche sono state lette</div>';
                    noNotificationsEl.style.display = 'block';
                    listEl.style.display = 'none';
                } else {
                    noNotificationsEl.style.display = 'none';
                    listEl.style.display = 'block';
                    listEl.innerHTML = unreadNotifications.map(notif => this.createNotificationItem(notif)).join('');
                    console.log('✅ Notifiche non lette visualizzate nel dropdown');
                }
            } else {
                console.error('❌ Errore API recent:', data.message);
                noNotificationsEl.innerHTML = '<div class="text-center py-3 text-danger"><i class="fas fa-exclamation-triangle me-2"></i>Errore caricamento notifiche</div>';
                noNotificationsEl.style.display = 'block';
            }
        } catch (error) {
            console.error('❌ Errore caricamento notifiche:', error);
            noNotificationsEl.innerHTML = '<div class="text-center py-3 text-danger"><i class="fas fa-exclamation-triangle me-2"></i>Errore di rete</div>';
            noNotificationsEl.style.display = 'block';
        } finally {
            loadingEl.style.display = 'none';
        }
    }

    createNotificationItem(notification) {
        const typeIcons = {
            'INFO': '📋',
            'WARNING': '⚠️',
            'SUCCESS': '✅',
            'ERROR': '❌',
            'URGENT': '🚨'
        };

        const icon = typeIcons[notification.type] || '📋';
        const timeAgo = this.formatTimeAgo(notification.created_at);
        const readClass = notification.is_read ? '' : 'unread';

        return '<li class="notification-item ' + notification.type.toLowerCase() + ' ' + readClass + '"' +
                ' onclick="notificationSystem.markAsRead(' + notification.id + ')">' +
                '<div class="notification-title">' +
                    icon + ' ' + notification.title +
                    (!notification.is_read ? '<span class="badge bg-warning text-dark ms-1">NUOVO</span>' : '') +
                '</div>' +
                '<div class="notification-message">' + notification.message + '</div>' +
                '<div class="notification-time">' + timeAgo + '</div>' +
            '</li>';
    }

    async markAsRead(notificationId) {
        try {
            const response = await fetch('/api/notifications/mark-read/' + notificationId, {
                method: 'POST',
                credentials: 'include',  // IMPORTANTE: Include i cookie di autenticazione
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                console.log('✅ Notifica ' + notificationId + ' segnata come letta');
                // Ricarica notifiche e conteggio
                this.loadNotifications();
                this.updateUnreadCount();
            } else {
                console.error('❌ Errore segnalazione lettura: ' + response.status);
            }
        } catch (error) {
            console.error('❌ Errore segnalazione lettura:', error);
        }
    }

    formatTimeAgo(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffMins < 1) return 'Ora';
        if (diffMins < 60) return diffMins + 'm fa';
        if (diffHours < 24) return diffHours + 'h fa';
        if (diffDays < 7) return diffDays + 'g fa';

        return date.toLocaleDateString('it-IT', {
            day: '2-digit',
            month: '2-digit'
        });
    }

    adjustDropdownPosition() {
        const dropdown = document.getElementById('notificationDropdownMenu');
        if (!dropdown) return;

        console.log('🔧 FORZA posizionamento dropdown all\'interno della viewport...');

        // Ottieni dimensioni viewport
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        console.log('📏 Viewport:', viewportWidth + 'x' + viewportHeight);

        // FORZA il posizionamento con position fixed
        dropdown.style.position = 'fixed';
        dropdown.style.zIndex = '9999';
        dropdown.style.top = '60px';
        dropdown.style.transform = 'none';
        dropdown.style.margin = '0';

        // Calcola posizione in base alla larghezza schermo
        if (viewportWidth <= 768) {
            // Mobile: margini laterali
            dropdown.style.left = '10px';
            dropdown.style.right = '10px';
            dropdown.style.width = 'auto';
            console.log('📱 Mobile: dropdown a tutta larghezza con margini');
        } else if (viewportWidth <= 1000) {
            // Tablet: dropdown piccolo a destra
            dropdown.style.right = '10px';
            dropdown.style.left = 'auto';
            dropdown.style.width = '320px';
            console.log('📱 Tablet: dropdown 320px a destra');
        } else {
            // Desktop: dropdown compatto ma sempre dentro
            const dropdownWidth = 380;
            const rightMargin = 20;

            if (dropdownWidth + rightMargin > viewportWidth) {
                // Schermo troppo piccolo, riduci larghezza
                dropdown.style.width = (viewportWidth - 40) + 'px';
                dropdown.style.right = '20px';
                dropdown.style.left = '20px';
                console.log('💻 Desktop piccolo: larghezza ridotta a ' + (viewportWidth - 40) + 'px');
            } else {
                // Schermo normale
                dropdown.style.width = dropdownWidth + 'px';
                dropdown.style.right = rightMargin + 'px';
                dropdown.style.left = 'auto';
                console.log('💻 Desktop: dropdown 380px compatto a destra');
            }
        }

        // Limita altezza per non uscire dal basso
        const maxHeight = viewportHeight - 80; // 60px top + 20px margin
        dropdown.style.maxHeight = maxHeight + 'px';
        dropdown.style.overflowY = 'auto';

        console.log('✅ Dropdown FORZATO all\'interno della viewport');
    }

    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
    }
}

// Inizializza il sistema notifiche quando il DOM è pronto
let notificationSystem;

function initNotificationSystem() {
    console.log('🔔 Tentativo inizializzazione sistema notifiche...');

    // Verifica che tutti gli elementi necessari esistano
    const requiredElements = [
        'notificationBadge',
        'notificationDropdown',
        'notificationDropdownMenu',
        'notificationLoading',
        'noNotifications',
        'notificationsList'
    ];

    let allElementsFound = true;
    for (const elementId of requiredElements) {
        const element = document.getElementById(elementId);
        if (!element) {
            console.error('❌ Elemento mancante: ' + elementId);
            allElementsFound = false;
        } else {
            console.log('✅ Elemento trovato: ' + elementId);
        }
    }

    if (allElementsFound) {
        console.log('✅ Tutti gli elementi trovati, inizializzazione sistema...');
        notificationSystem = new NotificationSystem();
    } else {
        console.error('❌ Elementi mancanti, riprovo tra 1 secondo...');
        setTimeout(initNotificationSystem, 1000);
    }
}

// ===== GESTIONE DATA ATTUALE =====
function updateCurrentDate() {
    const now = new Date();
    const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };

    // Formato italiano: Lunedì, 9 dicembre 2024
    const dateString = now.toLocaleDateString('it-IT', options);

    // Capitalizza la prima lettera del giorno
    const capitalizedDate = dateString.charAt(0).toUpperCase() + dateString.slice(1);

    const dateElement = document.getElementById('dateText');
    if (dateElement) {
        dateElement.textContent = capitalizedDate;
    }
}

// Prova inizializzazione quando il DOM è pronto
document.addEventListener('DOMContentLoaded', function() {
    initNotificationSystem();

    // Inizializza la data attuale
    console.log('📅 Inizializzazione data attuale...');
    updateCurrentDate();

    // Aggiorna la data ogni minuto
    setInterval(updateCurrentDate, 60000);
});

// Fallback: prova anche quando la finestra è completamente caricata
window.addEventListener('load', function() {
    if (!notificationSystem) {
        console.log('🔄 Fallback: inizializzazione dopo window.load');
        initNotificationSystem();
    }

    // Assicurati che la data sia inizializzata anche nel fallback
    updateCurrentDate();

    // Carica la versione dell'app
    loadAppVersion();

    // Evidenzia menu attivo basato sulla URL corrente
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPath.includes(href.split('/')[1])) {
            link.classList.add('active-menu');
        }
    });

    // Evidenzia dropdown attivo
    if (currentPath.includes('/operativo/')) {
        if (currentPath.includes('/sof/')) {
            document.getElementById('sofDropdown')?.classList.add('active-menu');
        } else {
            document.getElementById('naviDropdown')?.classList.add('active-menu');
        }
    }

    // ===== ANIMAZIONI AVANZATE DROPDOWN =====

    // Effetto ripple sui click
    document.querySelectorAll('.dropdown-item').forEach(item => {
        item.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s ease-out;
                pointer-events: none;
                z-index: 1;
            `;

            this.style.position = 'relative';
            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Animazione contatori rimossa

    // Caricamento contatori rimosso

    // Hover effects avanzati
    document.querySelectorAll('.dropdown-toggle').forEach(toggle => {
        toggle.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        toggle.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Caricamento contatori automatico rimosso

    // Carica JavaScript temi globali (priorità alta)
    const globalThemesScript = document.createElement('script');
    globalThemesScript.src = '/static/js/global-themes.js';
    document.head.appendChild(globalThemesScript);

    // Carica JavaScript dropdown moderni
    const modernDropdownsScript = document.createElement('script');
    modernDropdownsScript.src = '/static/js/modern-dropdowns.js';
    document.head.appendChild(modernDropdownsScript);
});

// ===== GESTIONE VERSIONE APP =====
async function loadAppVersion() {
    try {
        const response = await fetch('/api/system/config');
        const data = await response.json();

        if (data.success && data.config && data.config.app_version) {
            const versionElement = document.getElementById('app-version-display');
            if (versionElement) {
                versionElement.textContent = data.config.app_version;
            }
        }
    } catch (error) {
        console.log('Errore caricamento versione app:', error);
        // Mantieni la versione di default se c'è un errore
    }
}

// Pulisci quando la pagina viene scaricata
window.addEventListener('beforeunload', function() {
    if (notificationSystem) {
        notificationSystem.destroy();
    }
});
</script>

<!-- Sistema messaggi SNIP unificato -->
<script src="/static/js/messages.js"></script>
