#!/usr/bin/env python3
"""
Script per aggiornare tutte le query rimanenti nel file main.py
"""

import re

def update_queries_in_file():
    """Aggiorna tutte le query rimanenti nel file main.py"""
    
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🔧 AGGIORNAMENTO QUERY RIMANENTI")
    print("=" * 40)
    
    # Pattern per trovare le query SELECT che contengono ancora "Agemar" invece di "Agemar_Salerno"
    old_select_pattern = r'SELECT n\.id, n\."Nave", n\."Codice_Nave", n\."Prefisso_viaggio", n\."Agemar", n\."TSL",'
    new_select_pattern = r'SELECT n.id, n."Nave", n."Codice_Nave", n."Prefisso_viaggio", n."Agemar_Salerno", n."Agemar_GioiaTauro", n."TSL",'
    
    # Conta le occorrenze
    old_count = len(re.findall(old_select_pattern, content))
    print(f"Query da aggiornare trovate: {old_count}")
    
    if old_count > 0:
        # Sostituisci le query SELECT
        content = re.sub(old_select_pattern, new_select_pattern, content)
        
        # Pattern per aggiornare la costruzione degli array navi
        old_array_pattern = r'"agemar": row\[4\], "tsl": row\[5\], "armatore_id": row\[6\], "armatore_nome": row\[7\]'
        new_array_pattern = r'"agemar_salerno": row[4], "agemar_gioia": row[5], "tsl": row[6], "armatore_id": row[7], "armatore_nome": row[8]'
        
        array_count = len(re.findall(old_array_pattern, content))
        print(f"Array da aggiornare trovati: {array_count}")
        
        if array_count > 0:
            content = re.sub(old_array_pattern, new_array_pattern, content)
        
        # Salva il file aggiornato
        with open('main.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ {old_count} query SELECT aggiornate")
        print(f"✅ {array_count} array aggiornati")
        print("✅ File main.py aggiornato")
        
        return True
    else:
        print("✅ Tutte le query sono già aggiornate")
        return False

def update_ajax_function():
    """Aggiorna la funzione handle_ajax_search_response"""
    
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("\n🔧 AGGIORNAMENTO FUNZIONE AJAX")
    print("=" * 40)
    
    # Trova la funzione handle_ajax_search_response
    ajax_start = content.find('def handle_ajax_search_response(')
    if ajax_start == -1:
        print("❌ Funzione handle_ajax_search_response non trovata")
        return False
    
    # Trova la fine della funzione (prossima def o fine file)
    ajax_end = content.find('\ndef ', ajax_start + 1)
    if ajax_end == -1:
        ajax_end = len(content)
    
    ajax_function = content[ajax_start:ajax_end]
    
    # Verifica se contiene ancora i vecchi campi
    if '"agemar"' in ajax_function and '"agemar_salerno"' not in ajax_function:
        print("🔍 Funzione AJAX da aggiornare trovata")
        
        # Aggiorna i campi nella funzione AJAX
        updated_ajax = ajax_function.replace(
            '"agemar": nave["agemar"]',
            '"agemar_salerno": nave["agemar_salerno"], "agemar_gioia": nave["agemar_gioia"]'
        )
        
        # Aggiorna data-agemar in data-agemar-salerno e aggiungi data-agemar-gioia
        updated_ajax = updated_ajax.replace(
            'data-agemar="{nave[\'agemar\'] or \'\'}"',
            'data-agemar-salerno="{nave[\'agemar_salerno\'] or \'\'}" data-agemar-gioia="{nave[\'agemar_gioia\'] or \'\'}"'
        )
        
        # Sostituisci nel contenuto completo
        new_content = content[:ajax_start] + updated_ajax + content[ajax_end:]
        
        with open('main.py', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ Funzione AJAX aggiornata")
        return True
    else:
        print("✅ Funzione AJAX già aggiornata")
        return False

if __name__ == "__main__":
    print("🚢 AGGIORNAMENTO QUERY MAIN.PY")
    print("=" * 50)
    
    queries_updated = update_queries_in_file()
    ajax_updated = update_ajax_function()
    
    if queries_updated or ajax_updated:
        print(f"\n🎉 AGGIORNAMENTO COMPLETATO!")
        print("Il file main.py è stato aggiornato con i nuovi campi Agemar")
    else:
        print(f"\n✅ NESSUN AGGIORNAMENTO NECESSARIO")
        print("Tutti i campi sono già aggiornati")
