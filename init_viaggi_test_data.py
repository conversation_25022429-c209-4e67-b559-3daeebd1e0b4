#!/usr/bin/env python3
"""
Script per inserire dati di test per i viaggi
"""

from database import SessionLocal
from sqlalchemy import text
from datetime import datetime, date

def init_test_data():
    """Inserisce dati di test per viaggi e orari"""
    
    db = SessionLocal()
    
    try:
        print("Inserimento dati di test per viaggi...")
        
        # Verifica che esistano navi e porti di gestione
        navi_count = db.execute(text('SELECT COUNT(*) FROM "NAVI"')).scalar()
        porti_count = db.execute(text('SELECT COUNT(*) FROM "PORTI_GESTIONE"')).scalar()
        
        print(f"Navi disponibili: {navi_count}")
        print(f"Porti di gestione disponibili: {porti_count}")
        
        if navi_count == 0 or porti_count == 0:
            print("⚠️ Non ci sono abbastanza dati di base. Assicurati che esistano navi e porti di gestione.")
            return
        
        # Ottieni ID delle prime navi e porti
        nave_result = db.execute(text('SELECT id, "Prefisso_viaggio" FROM "NAVI" LIMIT 3'))
        navi = nave_result.fetchall()
        
        porto_result = db.execute(text('SELECT id_porto FROM "PORTI_GESTIONE" LIMIT 2'))
        porti = porto_result.fetchall()
        
        # Inserisci viaggi di test
        viaggi_test = [
            {
                'porto_gestione_id': porti[0][0],
                'nave_id': navi[0][0],
                'viaggio': navi[0][1],
                'data_arrivo': '2024-01-15',
                'data_partenza': '2024-01-18'
            },
            {
                'porto_gestione_id': porti[1][0] if len(porti) > 1 else porti[0][0],
                'nave_id': navi[1][0] if len(navi) > 1 else navi[0][0],
                'viaggio': navi[1][1] if len(navi) > 1 else navi[0][1],
                'data_arrivo': '2024-01-20',
                'data_partenza': '2024-01-23'
            }
        ]
        
        if len(navi) > 2:
            viaggi_test.append({
                'porto_gestione_id': porti[0][0],
                'nave_id': navi[2][0],
                'viaggio': navi[2][1],
                'data_arrivo': '2024-01-25',
                'data_partenza': '2024-01-28'
            })
        
        for viaggio in viaggi_test:
            # Verifica se il viaggio esiste già
            existing = db.execute(text("""
                SELECT id FROM "VIAGGIO" 
                WHERE nave_id = :nave_id AND data_arrivo = :data_arrivo
            """), {
                'nave_id': viaggio['nave_id'],
                'data_arrivo': viaggio['data_arrivo']
            }).fetchone()
            
            if not existing:
                db.execute(text("""
                    INSERT INTO "VIAGGIO" (porto_gestione_id, nave_id, viaggio, data_arrivo, data_partenza)
                    VALUES (:porto_gestione_id, :nave_id, :viaggio, :data_arrivo, :data_partenza)
                """), viaggio)
                print(f"✅ Viaggio inserito: {viaggio['viaggio']} - {viaggio['data_arrivo']}")
            else:
                print(f"⚠️ Viaggio già esistente: {viaggio['viaggio']} - {viaggio['data_arrivo']}")
        
        db.commit()
        
        # Conta i viaggi totali
        total_viaggi = db.execute(text('SELECT COUNT(*) FROM "VIAGGIO"')).scalar()
        print(f"📊 Totale viaggi nel database: {total_viaggi}")
        
        print("✅ Dati di test inseriti con successo!")
        
    except Exception as e:
        print(f"❌ Errore durante l'inserimento dei dati di test: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    init_test_data()
