#!/usr/bin/env python3
"""
Configurazione ottimizzata per uvicorn reload
"""

import uvicorn
import os
from pathlib import Path

def run_server():
    """Avvia server con configurazione ottimizzata"""
    
    # Configurazione reload
    reload_dirs = [
        str(Path.cwd()),  # Directory corrente
    ]
    
    # Aggiungi directory aggiuntive se esistono
    additional_dirs = ['templates', 'static', 'models']
    for dir_name in additional_dirs:
        if os.path.exists(dir_name):
            reload_dirs.append(str(Path(dir_name).absolute()))
    
    # Configurazione uvicorn
    config = {
        "app": "main:app",
        "host": "0.0.0.0",
        "port": 8002,
        "reload": True,
        "reload_dirs": reload_dirs,
        "reload_delay": 0.5,  # Delay più breve
        "reload_includes": ["*.py"],  # Solo file Python
        "reload_excludes": [
            "*.pyc",
            "__pycache__/*",
            "*.log",
            "backups/*",
            "venv/*",
            ".git/*"
        ],
        "log_level": "info",
        "access_log": True,
        "use_colors": True,
    }
    
    print("🚀 Avvio server con configurazione ottimizzata...")
    print(f"📂 Directory monitorate: {len(reload_dirs)}")
    for dir_path in reload_dirs:
        print(f"   - {dir_path}")
    
    uvicorn.run(**config)

if __name__ == "__main__":
    run_server()
