/**
 * 🧪 Test per la funzione aggiornaRigaViaggio
 * Questo file testa la logica di aggiornamento delle righe della tabella
 */

// Simula i dati di risposta dell'API
const mockApiResponse = {
    success: true,
    viaggi: [
        {
            id: 61,
            viaggio: "CTA5525A",
            nome_nave: "CATANIA",
            nome_porto: "Catania",
            porto_arrivo_nome: "Palermo",
            porto_destinazione_nome: "Napoli",
            porto_arrivo_code: "05",
            porto_destinazione_code: "09",
            data_arrivo: "2024-01-15",
            data_partenza: "2024-01-16"
        }
    ]
};

// Simula la struttura HTML della tabella
function createMockTable() {
    const tableHTML = `
        <table class="table">
            <tbody>
                <tr data-viaggio-id="61">
                    <td>
                        <span class="badge bg-secondary">TEST001</span>
                    </td>
                    <td class="cell-nave">
                        <strong>NAVE TEST</strong>
                    </td>
                    <td>
                        <span class="badge bg-primary porto-gestione-badge">
                            <i class="fas fa-anchor me-1"></i>Porto Test
                        </span>
                    </td>
                    <td class="cell-porto-arrivo">
                        <span class="badge bg-info text-dark">Porto Arrivo Test</span>
                    </td>
                    <td class="cell-porto-destinazione">
                        <span class="badge bg-warning text-dark">Porto Destinazione Test</span>
                    </td>
                    <td>
                        <span class="text-success fw-bold">01/01/2024</span>
                    </td>
                    <td>
                        <span class="text-warning fw-bold">02/01/2024</span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-warning">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>
    `;
    
    document.body.innerHTML = tableHTML;
}

// Simula la funzione fetch
function mockFetch(url) {
    console.log(`🌐 Mock fetch chiamata per: ${url}`);
    
    if (url.includes('/api/sof/da-realizzare/search')) {
        return Promise.resolve({
            json: () => Promise.resolve(mockApiResponse)
        });
    }
    
    return Promise.reject(new Error('URL non riconosciuto'));
}

// Funzione di test per aggiornaRigaViaggio
async function testAggiornaRigaViaggio(viaggioId) {
    console.log(`🧪 INIZIO TEST aggiornaRigaViaggio per ID: ${viaggioId}`);
    
    try {
        // 1. Trova la riga da aggiornare
        const riga = document.querySelector(`tr[data-viaggio-id="${viaggioId}"]`);
        if (!riga) {
            console.error(`❌ Riga viaggio ${viaggioId} non trovata`);
            return false;
        }
        console.log(`✅ Riga trovata:`, riga);

        // 2. Mostra indicatore di caricamento
        riga.style.opacity = '0.6';
        riga.style.transition = 'opacity 0.3s';
        console.log(`🔄 Indicatore di caricamento attivato`);

        // 3. Simula la chiamata API
        const response = await mockFetch('/api/sof/da-realizzare/search');
        const data = await response.json();

        if (!data.success) {
            throw new Error(data.message || 'Errore nel recupero dati');
        }
        console.log(`📡 Dati API ricevuti:`, data);

        // 4. Trova il viaggio specifico nei dati
        const viaggioAggiornato = data.viaggi.find(v => v.id === viaggioId);
        if (!viaggioAggiornato) {
            console.error(`❌ Viaggio ${viaggioId} non trovato nei dati aggiornati`);
            return false;
        }
        console.log(`🎯 Viaggio da aggiornare:`, viaggioAggiornato);

        // 5. Aggiorna le celle della riga con selettori corretti
        console.log(`📋 Inizio aggiornamento celle...`);

        // 🔄 AGGIORNA CODICE VIAGGIO (prima colonna - badge)
        const badgeViaggio = riga.querySelector('td:nth-child(1) .badge');
        if (badgeViaggio && viaggioAggiornato.viaggio) {
            const oldValue = badgeViaggio.textContent;
            badgeViaggio.textContent = viaggioAggiornato.viaggio;
            console.log(`   ✅ Codice viaggio: '${oldValue}' → '${viaggioAggiornato.viaggio}'`);
        }

        // 🔄 AGGIORNA NOME NAVE (seconda colonna - .cell-nave)
        const cellaNave = riga.querySelector('.cell-nave strong');
        if (cellaNave && viaggioAggiornato.nome_nave) {
            const oldValue = cellaNave.textContent;
            cellaNave.textContent = viaggioAggiornato.nome_nave;
            console.log(`   ✅ Nome nave: '${oldValue}' → '${viaggioAggiornato.nome_nave}'`);
        }

        // 🔄 AGGIORNA PORTO DI GESTIONE (terza colonna - badge porto)
        const badgePorto = riga.querySelector('td:nth-child(3) .porto-gestione-badge');
        if (badgePorto && viaggioAggiornato.nome_porto) {
            const oldValue = badgePorto.textContent.replace('⚓ ', '').trim();
            badgePorto.innerHTML = `<i class="fas fa-anchor me-1"></i>${viaggioAggiornato.nome_porto}`;
            badgePorto.title = viaggioAggiornato.nome_porto;
            console.log(`   ✅ Porto gestione: '${oldValue}' → '${viaggioAggiornato.nome_porto}'`);
        }

        // 🔄 AGGIORNA PORTO ARRIVO (quarta colonna - .cell-porto-arrivo)
        const cellaPortoArrivo = riga.querySelector('.cell-porto-arrivo');
        if (cellaPortoArrivo) {
            const oldValue = cellaPortoArrivo.textContent.trim();
            if (viaggioAggiornato.porto_arrivo_nome) {
                cellaPortoArrivo.innerHTML = `<span class="badge bg-info text-dark">${viaggioAggiornato.porto_arrivo_nome}</span>`;
                cellaPortoArrivo.title = `Codice: ${viaggioAggiornato.porto_arrivo_code || 'N/A'}`;
                console.log(`   ✅ Porto arrivo: '${oldValue}' → '${viaggioAggiornato.porto_arrivo_nome}'`);
            } else {
                cellaPortoArrivo.innerHTML = `<span class="text-muted">N/A</span>`;
                cellaPortoArrivo.title = "Codice: N/A";
                console.log(`   ✅ Porto arrivo: '${oldValue}' → 'N/A'`);
            }
        }

        // 🔄 AGGIORNA PORTO DESTINAZIONE (quinta colonna - .cell-porto-destinazione)
        const cellaPortoDestinazione = riga.querySelector('.cell-porto-destinazione');
        if (cellaPortoDestinazione) {
            const oldValue = cellaPortoDestinazione.textContent.trim();
            if (viaggioAggiornato.porto_destinazione_nome) {
                cellaPortoDestinazione.innerHTML = `<span class="badge bg-warning text-dark">${viaggioAggiornato.porto_destinazione_nome}</span>`;
                cellaPortoDestinazione.title = `Codice: ${viaggioAggiornato.porto_destinazione_code || 'N/A'}`;
                console.log(`   ✅ Porto destinazione: '${oldValue}' → '${viaggioAggiornato.porto_destinazione_nome}'`);
            } else {
                cellaPortoDestinazione.innerHTML = `<span class="text-muted">N/A</span>`;
                cellaPortoDestinazione.title = "Codice: N/A";
                console.log(`   ✅ Porto destinazione: '${oldValue}' → 'N/A'`);
            }
        }

        // 🔄 AGGIORNA DATE
        if (viaggioAggiornato.data_arrivo) {
            const cellaDataArrivo = riga.querySelector('td:nth-child(6)');
            if (cellaDataArrivo) {
                const dataArrivo = new Date(viaggioAggiornato.data_arrivo);
                const dataFormattata = dataArrivo.toLocaleDateString('it-IT');
                const oldValue = cellaDataArrivo.textContent.trim();
                cellaDataArrivo.innerHTML = `<span class="text-success fw-bold">${dataFormattata}</span>`;
                console.log(`   ✅ Data arrivo: '${oldValue}' → '${dataFormattata}'`);
            }
        }

        if (viaggioAggiornato.data_partenza) {
            const cellaDataPartenza = riga.querySelector('td:nth-child(7)');
            if (cellaDataPartenza) {
                const dataPartenza = new Date(viaggioAggiornato.data_partenza);
                const dataFormattata = dataPartenza.toLocaleDateString('it-IT');
                const oldValue = cellaDataPartenza.textContent.trim();
                cellaDataPartenza.innerHTML = `<span class="text-warning fw-bold">${dataFormattata}</span>`;
                console.log(`   ✅ Data partenza: '${oldValue}' → '${dataFormattata}'`);
            }
        }

        // 6. Ripristina l'opacità con animazione
        setTimeout(() => {
            riga.style.opacity = '1';
            riga.style.backgroundColor = '#d4edda'; // Verde chiaro per indicare aggiornamento
            console.log(`🎨 Animazione di successo attivata`);

            // Rimuovi l'evidenziazione dopo 2 secondi
            setTimeout(() => {
                riga.style.backgroundColor = '';
                riga.style.transition = '';
                console.log(`🎨 Animazione completata`);
            }, 2000);
        }, 100);

        console.log(`✅ TEST COMPLETATO CON SUCCESSO per viaggio ${viaggioId}`);
        return true;

    } catch (error) {
        console.error(`❌ ERRORE NEL TEST per viaggio ${viaggioId}:`, error);

        // Ripristina l'opacità in caso di errore
        const riga = document.querySelector(`tr[data-viaggio-id="${viaggioId}"]`);
        if (riga) {
            riga.style.opacity = '1';
            riga.style.backgroundColor = '#f8d7da'; // Rosso chiaro per indicare errore
            console.log(`🎨 Animazione di errore attivata`);

            setTimeout(() => {
                riga.style.backgroundColor = '';
                console.log(`🎨 Animazione di errore completata`);
            }, 3000);
        }

        return false;
    }
}

// Funzione per eseguire il test
function runTest() {
    console.log('🚀 INIZIO TEST SISTEMA AGGIORNAMENTO RIGA');
    
    // Crea la tabella mock
    createMockTable();
    console.log('📋 Tabella mock creata');
    
    // Esegui il test
    testAggiornaRigaViaggio(61).then(result => {
        if (result) {
            console.log('🎉 TEST SUPERATO! Il sistema di aggiornamento funziona correttamente.');
        } else {
            console.log('❌ TEST FALLITO! Ci sono problemi nel sistema di aggiornamento.');
        }
    });
}

// Esporta le funzioni per uso esterno
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testAggiornaRigaViaggio,
        runTest,
        mockApiResponse
    };
}

// Se eseguito in un browser, esegui automaticamente il test
if (typeof window !== 'undefined') {
    // Aspetta che il DOM sia caricato
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runTest);
    } else {
        runTest();
    }
}
