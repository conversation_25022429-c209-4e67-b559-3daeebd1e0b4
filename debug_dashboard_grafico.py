#!/usr/bin/env python3
"""
Debug per verificare perché il grafico non appare nel dashboard
"""

import requests
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8002"

def debug_dashboard_elements():
    """Debug elementi dashboard"""
    logger.info("🔍 DEBUG DASHBOARD ELEMENTI")
    
    try:
        # Testa se il file JavaScript è accessibile
        response = requests.get(f"{BASE_URL}/static/js/dashboard.js")
        logger.info(f"   dashboard.js Status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            
            # Cerca elementi chiave
            elements = [
                ("initNaviPerPortoChart function", "initNaviPerPortoChart" in content),
                ("fetch API call", "/api/dashboard/navi-per-porto" in content),
                ("naviPerPortoChart canvas", "naviPerPortoChart" in content),
                ("Chart.js new Chart", "new Chart(" in content),
                ("Console log SOF", "SOF realizzati" in content)
            ]
            
            logger.info("   Elementi JavaScript trovati:")
            for name, found in elements:
                logger.info(f"     {name}: {'✅' if found else '❌'}")
                
            # Cerca la chiamata alla funzione
            if "initNaviPerPortoChart();" in content:
                logger.info("   ✅ Funzione chiamata nel DOMContentLoaded")
            else:
                logger.error("   ❌ Funzione NON chiamata nel DOMContentLoaded")
                
        return response.status_code == 200
        
    except Exception as e:
        logger.error(f"   ❌ Errore: {e}")
        return False

def debug_html_structure():
    """Debug struttura HTML"""
    logger.info("🔍 DEBUG STRUTTURA HTML")
    
    try:
        # Testa se il template è accessibile
        response = requests.get(f"{BASE_URL}/templates/operativo/dashboard.html")
        logger.info(f"   Template Status: {response.status_code}")
        
        if response.status_code == 404:
            logger.info("   Template non accessibile direttamente (normale)")
            
        # Testa se Chart.js è caricato
        response = requests.get(f"{BASE_URL}/static/js/dashboard.js")
        if response.status_code == 200:
            content = response.text
            
            # Verifica se Chart.js è referenziato
            if "Chart" in content:
                logger.info("   ✅ Chart.js referenziato nel JavaScript")
            else:
                logger.error("   ❌ Chart.js NON referenziato")
                
        return True
        
    except Exception as e:
        logger.error(f"   ❌ Errore: {e}")
        return False

def debug_console_errors():
    """Suggerimenti per debug console"""
    logger.info("🔍 DEBUG CONSOLE BROWSER")
    logger.info("   Per verificare errori JavaScript:")
    logger.info("   1. Apri http://localhost:8002/dashboard/operativo")
    logger.info("   2. Premi F12 per aprire Developer Tools")
    logger.info("   3. Vai nel tab 'Console'")
    logger.info("   4. Cerca errori in rosso")
    logger.info("   5. Cerca i log che iniziano con '📋 Caricamento dati SOF...'")
    logger.info("")
    logger.info("   Possibili errori da cercare:")
    logger.info("   - 'Chart is not defined' → Chart.js non caricato")
    logger.info("   - '404 /api/dashboard/navi-per-porto' → API non raggiungibile")
    logger.info("   - 'Canvas naviPerPortoChart non trovato' → HTML non corretto")
    logger.info("   - 'Errore API navi per porto' → Problema database")

def debug_api_direct():
    """Test diretto API senza autenticazione"""
    logger.info("🔍 DEBUG API DIRETTA")
    
    # Suggerimenti per test manuale
    logger.info("   Per testare l'API manualmente:")
    logger.info("   1. Fai login su http://localhost:8002")
    logger.info("   2. Apri una nuova tab")
    logger.info("   3. Vai su http://localhost:8002/api/dashboard/navi-per-porto")
    logger.info("   4. Dovresti vedere JSON con dati SOF")
    logger.info("")
    logger.info("   Se vedi errore 401: problema autenticazione")
    logger.info("   Se vedi errore 500: problema database/query")
    logger.info("   Se vedi JSON vuoto: nessun SOF realizzato nel database")

def debug_database_content():
    """Suggerimenti per verificare contenuto database"""
    logger.info("🔍 DEBUG CONTENUTO DATABASE")
    logger.info("   Per verificare se ci sono SOF realizzati:")
    logger.info("   1. Connettiti al database PostgreSQL")
    logger.info("   2. Esegui: SELECT COUNT(*) FROM \"VIAGGIO\" WHERE visibile = 'no';")
    logger.info("   3. Se il risultato è 0, non ci sono SOF realizzati")
    logger.info("   4. Per creare dati di test, cambia alcuni viaggi:")
    logger.info("      UPDATE \"VIAGGIO\" SET visibile = 'no' WHERE id IN (1,2,3);")
    logger.info("")
    logger.info("   Query completa per debug:")
    logger.info("   SELECT pg.nome_porto, COUNT(*) as sof_count")
    logger.info("   FROM \"VIAGGIO\" v")
    logger.info("   LEFT JOIN \"PORTI_GESTIONE\" pg ON v.porto_gestione_id = pg.id_porto")
    logger.info("   WHERE v.visibile = 'no'")
    logger.info("   GROUP BY pg.nome_porto;")

def main():
    """Esegue tutti i debug"""
    logger.info("🚀 DEBUG GRAFICO DASHBOARD NON VISUALIZZATO")
    logger.info("=" * 60)
    
    # Debug 1: JavaScript
    js_ok = debug_dashboard_elements()
    
    # Debug 2: HTML
    html_ok = debug_html_structure()
    
    # Debug 3: Console
    debug_console_errors()
    
    # Debug 4: API
    debug_api_direct()
    
    # Debug 5: Database
    debug_database_content()
    
    # Riepilogo
    logger.info("=" * 60)
    logger.info("📋 RIEPILOGO DEBUG:")
    logger.info(f"   JavaScript accessibile: {'✅' if js_ok else '❌'}")
    logger.info(f"   HTML struttura: {'✅' if html_ok else '❌'}")
    
    logger.info("=" * 60)
    logger.info("🎯 PROSSIMI PASSI:")
    logger.info("   1. Verifica console browser per errori JavaScript")
    logger.info("   2. Testa API manualmente dopo login")
    logger.info("   3. Verifica se ci sono SOF realizzati nel database")
    logger.info("   4. Se necessario, crea dati di test")
    logger.info("")
    logger.info("💡 CAUSA PIÙ PROBABILE:")
    logger.info("   Il database non contiene SOF realizzati (visibile='no')")
    logger.info("   Il grafico appare solo se ci sono dati da mostrare")

if __name__ == "__main__":
    main()
