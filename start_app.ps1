# Script PowerShell per avviare l'applicazione SNIP con sistema 2FA SMS

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   AVVIO APPLICAZIONE SNIP CON 2FA SMS" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Verifica ambiente virtuale
$venvPath = "venv_new\Scripts\python.exe"
if (-not (Test-Path $venvPath)) {
    Write-Host "ERRORE: Ambiente virtuale non trovato!" -ForegroundColor Red
    Write-Host "Crea l'ambiente virtuale con: python -m venv venv_new" -ForegroundColor Yellow
    Read-Host "Premi Enter per uscire"
    exit 1
}

Write-Host "✅ Ambiente virtuale trovato: $venvPath" -ForegroundColor Green
Write-Host ""

# Verifica policy di esecuzione
$policy = Get-ExecutionPolicy -Scope CurrentUser
if ($policy -eq "Restricted") {
    Write-Host "⚠️  Policy di esecuzione ristretta. Cambio in RemoteSigned..." -ForegroundColor Yellow
    try {
        Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
        Write-Host "✅ Policy di esecuzione aggiornata" -ForegroundColor Green
    } catch {
        Write-Host "❌ Impossibile cambiare policy di esecuzione" -ForegroundColor Red
        Write-Host "Esegui PowerShell come amministratore e riprova" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "🔐 Avvio sistema 2FA e applicazione..." -ForegroundColor Magenta
Write-Host "📱 Preparati a ricevere un SMS al numero 3805127005" -ForegroundColor Yellow
Write-Host ""
Write-Host "----------------------------------------" -ForegroundColor Gray

# Avvia l'applicazione
try {
    & $venvPath "main.py"
} catch {
    Write-Host ""
    Write-Host "❌ Errore durante l'avvio: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    Write-Host ""
    Write-Host "Applicazione terminata." -ForegroundColor Gray
    Read-Host "Premi Enter per uscire"
}
