#!/usr/bin/env python3
"""
Debug diretto del database per vedere le configurazioni
"""

import requests
import json
import sys

# Configurazione
BASE_URL = "http://localhost:8002"
LOGIN_URL = f"{BASE_URL}/login"
CONFIG_URL = f"{BASE_URL}/admin/config"

# Credenziali
TEST_CREDENTIALS = {
    "email": "<EMAIL>",
    "password": "Ettore2025!"
}

def login_and_get_session():
    """Effettua login e ottiene sessione autenticata"""
    print("🔐 Login...")
    
    session = requests.Session()
    
    login_data = {
        "username": TEST_CREDENTIALS["email"],
        "password": TEST_CREDENTIALS["password"]
    }
    
    response = session.post(LOGIN_URL, data=login_data, allow_redirects=False)
    
    if response.status_code == 303:
        redirect_url = response.headers.get('location')
        if redirect_url and redirect_url.startswith('/'):
            redirect_url = BASE_URL + redirect_url
            session.get(redirect_url)
    
    access_token = session.cookies.get('access_token')
    if access_token:
        print(f"✅ Login riuscito")
        return session
    else:
        print("❌ Login fallito")
        return None

def debug_database():
    """Debug completo del database"""
    print("\n🔍 DEBUG DATABASE CONFIGURAZIONI")
    print("=" * 60)
    
    session = login_and_get_session()
    if not session:
        return False
    
    # Ottieni tutte le configurazioni dal database
    print("\n📊 1. Tutte le configurazioni nel database...")
    response = session.get(CONFIG_URL)
    
    if response.status_code != 200:
        print(f"❌ Errore accesso database: {response.status_code}")
        print(f"   Risposta: {response.text}")
        return False
    
    configs = response.json()
    
    print(f"   📋 Totale configurazioni: {len(configs)}")
    
    # Filtra configurazioni email
    email_configs = [c for c in configs if c["config_key"].startswith("email_")]
    
    print(f"\n📧 2. Configurazioni email nel database:")
    print(f"   📋 Totale configurazioni email: {len(email_configs)}")
    
    for config in email_configs:
        key = config["config_key"]
        value = config["config_value"]
        
        # Nascondi password per sicurezza
        if "password" in key.lower():
            display_value = "***NASCOSTA***" if value else "VUOTA"
        else:
            display_value = value
        
        print(f"      {key}: {display_value}")
        print(f"         ID: {config['id']}")
        print(f"         Tipo: {config['config_type']}")
        print(f"         Attiva: {config['is_active']}")
        print(f"         Creata: {config['created_at']}")
        print(f"         Aggiornata: {config['updated_at']}")
        print()
    
    # Cerca specificamente la password
    password_config = None
    for config in email_configs:
        if config["config_key"] == "email_smtp_password":
            password_config = config
            break
    
    print(f"\n🔑 3. Configurazione password SMTP:")
    if password_config:
        print(f"   ✅ TROVATA!")
        print(f"      ID: {password_config['id']}")
        print(f"      Valore: {'***PRESENTE***' if password_config['config_value'] else 'VUOTO'}")
        print(f"      Lunghezza: {len(password_config['config_value']) if password_config['config_value'] else 0} caratteri")
        print(f"      Tipo: {password_config['config_type']}")
        print(f"      Attiva: {password_config['is_active']}")
        print(f"      Ultima modifica: {password_config['updated_at']}")
    else:
        print(f"   ❌ NON TROVATA!")
    
    # Verifica altre configurazioni che potrebbero interferire
    print(f"\n🔍 4. Altre configurazioni che potrebbero interferire:")
    other_configs = [c for c in configs if not c["config_key"].startswith("email_") and not c["config_key"].startswith("port_") and not c["config_key"].startswith("security_")]
    
    for config in other_configs:
        if "email" in config["config_key"].lower() or "smtp" in config["config_key"].lower() or "password" in config["config_key"].lower():
            print(f"      {config['config_key']}: {config['config_value']}")
    
    return True

def main():
    """Funzione principale"""
    print("🚀 DEBUG DATABASE CONFIGURAZIONI SMTP")
    print("=" * 60)
    
    # Verifica server
    try:
        response = requests.get(BASE_URL, timeout=5)
        print("✅ Server raggiungibile")
    except:
        print("❌ Server non raggiungibile")
        return 1
    
    # Esegui debug
    if debug_database():
        print("\n✅ DEBUG COMPLETATO")
        return 0
    else:
        print("\n❌ DEBUG FALLITO")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
