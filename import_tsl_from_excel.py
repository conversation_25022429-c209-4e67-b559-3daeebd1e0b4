#!/usr/bin/env python3
"""
Script per importare i dati T.S.L. dal file Excel nav.xlsx nel database
Colonna A: Nome nave
Colonna B: T.S.L.
"""

import pandas as pd
from sqlalchemy import text
from database import SessionLocal
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def import_tsl_from_excel():
    """Importa i dati T.S.L. dal file Excel nel database"""
    
    try:
        print("📊 IMPORTAZIONE T.S.L. DA EXCEL")
        print("=" * 50)
        
        # Leggi il file Excel
        print("\n1. Lettura file Excel...")
        
        try:
            df = pd.read_excel('nav.xlsx', header=None, names=['nome_nave', 'tsl'])
            print(f"   ✅ File Excel letto con successo")
            print(f"   📋 Righe trovate: {len(df)}")
        except FileNotFoundError:
            print("   ❌ File nav.xlsx non trovato!")
            return False
        except Exception as e:
            print(f"   ❌ Errore lettura Excel: {str(e)}")
            return False
        
        # Pulisci i dati
        print("\n2. Pulizia dati...")
        
        # Rimuovi righe vuote
        df = df.dropna(subset=['nome_nave'])
        
        # Converti T.S.L. in numerico, gestendo errori
        df['tsl'] = pd.to_numeric(df['tsl'], errors='coerce')
        
        # Rimuovi spazi extra dai nomi
        df['nome_nave'] = df['nome_nave'].astype(str).str.strip().str.upper()
        
        print(f"   ✅ Dati puliti")
        print(f"   📋 Righe valide: {len(df)}")
        
        # Mostra prime 5 righe
        print("\n   📋 Prime 5 righe:")
        for i, row in df.head().iterrows():
            tsl_str = f"{row['tsl']:.2f}" if pd.notna(row['tsl']) else "N/A"
            print(f"      {i+1}. {row['nome_nave']} → {tsl_str}")
        
        # Connetti al database
        print("\n3. Connessione database...")
        
        db = SessionLocal()
        
        # Recupera tutte le navi dal database
        print("\n4. Recupero navi dal database...")
        
        result = db.execute(text('''
            SELECT id, "Nave", "Codice_Nave", "TSL"
            FROM "NAVI"
            ORDER BY "Nave"
        '''))
        
        navi_db = {}
        for row in result:
            nome_db = row[1].strip().upper() if row[1] else ""
            navi_db[nome_db] = {
                'id': row[0],
                'nome_originale': row[1],
                'codice': row[2],
                'tsl_attuale': row[3]
            }
        
        print(f"   ✅ Navi nel database: {len(navi_db)}")
        
        # Processo di matching e aggiornamento
        print("\n5. Matching e aggiornamento...")
        
        aggiornate = 0
        non_trovate = 0
        errori = 0
        saltate = 0
        
        for i, row in df.iterrows():
            nome_excel = row['nome_nave']
            tsl_excel = row['tsl']
            
            # Salta se T.S.L. non è valido
            if pd.isna(tsl_excel):
                print(f"   ⚠️ Riga {i+1}: T.S.L. non valido per '{nome_excel}' - saltata")
                saltate += 1
                continue
            
            # Cerca corrispondenza nel database
            if nome_excel in navi_db:
                nave_db = navi_db[nome_excel]
                
                try:
                    # Aggiorna T.S.L. nel database
                    db.execute(text('''
                        UPDATE "NAVI"
                        SET "TSL" = :tsl
                        WHERE id = :id
                    '''), {
                        'tsl': float(tsl_excel),
                        'id': nave_db['id']
                    })
                    
                    tsl_precedente = nave_db['tsl_attuale']
                    tsl_prec_str = f"{tsl_precedente:.2f}" if tsl_precedente else "NULL"
                    
                    print(f"   ✅ {nome_excel} ({nave_db['codice']}): {tsl_prec_str} → {tsl_excel:.2f}")
                    aggiornate += 1
                    
                except Exception as e:
                    print(f"   ❌ Errore aggiornamento {nome_excel}: {str(e)}")
                    errori += 1
            else:
                print(f"   ❌ Nave non trovata: '{nome_excel}'")
                non_trovate += 1
        
        # Commit delle modifiche
        if aggiornate > 0:
            print(f"\n6. Salvataggio modifiche...")
            db.commit()
            print(f"   ✅ {aggiornate} navi aggiornate nel database")
        else:
            print(f"\n6. Nessuna modifica da salvare")
        
        # Riepilogo
        print(f"\n🎯 RIEPILOGO IMPORTAZIONE")
        print(f"   📊 Righe Excel processate: {len(df)}")
        print(f"   ✅ Navi aggiornate: {aggiornate}")
        print(f"   ❌ Navi non trovate: {non_trovate}")
        print(f"   ⚠️ Righe saltate (T.S.L. non valido): {saltate}")
        print(f"   💥 Errori: {errori}")
        
        # Mostra navi non trovate se ce ne sono poche
        if non_trovate > 0 and non_trovate <= 10:
            print(f"\n📋 Navi non trovate nel database:")
            for i, row in df.iterrows():
                nome_excel = row['nome_nave']
                if nome_excel not in navi_db and pd.notna(row['tsl']):
                    print(f"   - {nome_excel}")
        
        # Verifica finale
        print(f"\n7. Verifica finale...")
        
        result = db.execute(text('''
            SELECT COUNT(*) as totale,
                   COUNT("TSL") as con_tsl,
                   AVG("TSL") as media_tsl
            FROM "NAVI"
            WHERE "TSL" IS NOT NULL
        '''))
        
        stats = result.fetchone()
        print(f"   📊 Navi con T.S.L.: {stats[1]}")
        print(f"   📊 T.S.L. medio: {stats[2]:.2f} tonnellate")
        
        db.close()
        
        if aggiornate > 0:
            print(f"\n🎉 IMPORTAZIONE COMPLETATA CON SUCCESSO!")
            return True
        else:
            print(f"\n⚠️ NESSUNA NAVE AGGIORNATA")
            return False
            
    except Exception as e:
        print(f"\n💥 ERRORE GENERALE: {str(e)}")
        if 'db' in locals():
            db.rollback()
            db.close()
        return False

def show_matching_preview():
    """Mostra un'anteprima del matching senza modificare il database"""
    
    try:
        print("🔍 ANTEPRIMA MATCHING NAVI")
        print("=" * 40)
        
        # Leggi Excel
        df = pd.read_excel('nav.xlsx', header=None, names=['nome_nave', 'tsl'])
        df = df.dropna(subset=['nome_nave'])
        df['tsl'] = pd.to_numeric(df['tsl'], errors='coerce')
        df['nome_nave'] = df['nome_nave'].astype(str).str.strip().str.upper()
        
        # Leggi database
        db = SessionLocal()
        result = db.execute(text('SELECT "Nave" FROM "NAVI" ORDER BY "Nave"'))
        navi_db = set(row[0].strip().upper() for row in result if row[0])
        db.close()
        
        print(f"\n📊 Statistiche:")
        print(f"   Excel: {len(df)} righe")
        print(f"   Database: {len(navi_db)} navi")
        
        # Trova corrispondenze
        trovate = 0
        non_trovate = []
        
        for i, row in df.iterrows():
            nome_excel = row['nome_nave']
            if nome_excel in navi_db:
                trovate += 1
            else:
                non_trovate.append(nome_excel)
        
        print(f"\n🎯 Risultati matching:")
        print(f"   ✅ Corrispondenze trovate: {trovate}")
        print(f"   ❌ Non trovate: {len(non_trovate)}")
        
        if non_trovate and len(non_trovate) <= 20:
            print(f"\n📋 Navi non trovate:")
            for nome in non_trovate[:20]:
                print(f"   - {nome}")
            if len(non_trovate) > 20:
                print(f"   ... e altre {len(non_trovate) - 20}")
        
    except Exception as e:
        print(f"❌ Errore anteprima: {str(e)}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "preview":
        show_matching_preview()
    else:
        print("🚢 IMPORTAZIONE T.S.L. DA EXCEL")
        print("\nOpzioni:")
        print("  python import_tsl_from_excel.py          - Esegui importazione")
        print("  python import_tsl_from_excel.py preview  - Mostra anteprima")
        print()
        
        risposta = input("Vuoi procedere con l'importazione? (s/N): ").lower()
        if risposta in ['s', 'si', 'y', 'yes']:
            import_tsl_from_excel()
        else:
            print("Importazione annullata.")
