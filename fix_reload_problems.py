#!/usr/bin/env python3
"""
Script per risolvere automaticamente i problemi di reload di uvicorn
"""

import os
import sys
import time
import psutil
import logging
import subprocess
from pathlib import Path

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ReloadFixer:
    """Classe per risolvere problemi di reload"""
    
    def __init__(self):
        self.fixes_applied = []
        
    def add_fix(self, fix_description):
        """Aggiunge una fix applicata"""
        self.fixes_applied.append(fix_description)
        logger.info(f"✅ {fix_description}")
    
    def kill_uvicorn_processes(self):
        """Termina tutti i processi uvicorn"""
        logger.info("🔄 Terminazione processi uvicorn esistenti...")
        
        killed_count = 0
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                proc_info = proc.info
                should_kill = False

                if proc_info['name'] and 'uvicorn' in proc_info['name'].lower():
                    should_kill = True
                elif proc_info['cmdline'] and any('uvicorn' in str(arg) for arg in proc_info['cmdline']):
                    should_kill = True

                if should_kill:
                    logger.info(f"🛑 Terminando processo PID {proc_info['pid']}")
                    proc.terminate()

                    # Aspetta terminazione
                    try:
                        proc.wait(timeout=5)
                    except psutil.TimeoutExpired:
                        logger.warning(f"⚠️ Forzando kill PID {proc_info['pid']}")
                        proc.kill()

                    killed_count += 1

            except (psutil.NoSuchProcess, psutil.AccessDenied, TypeError):
                continue
        
        if killed_count > 0:
            self.add_fix(f"Terminati {killed_count} processi uvicorn")
            time.sleep(2)  # Aspetta che i processi si chiudano completamente
        else:
            logger.info("ℹ️ Nessun processo uvicorn da terminare")
    
    def clear_python_cache(self):
        """Pulisce cache Python"""
        logger.info("🧹 Pulizia cache Python...")
        
        cache_dirs = []
        
        # Trova directory __pycache__
        for root, dirs, files in os.walk('.'):
            if '__pycache__' in dirs:
                cache_dirs.append(os.path.join(root, '__pycache__'))
        
        # Rimuovi cache
        removed_count = 0
        for cache_dir in cache_dirs:
            try:
                import shutil
                shutil.rmtree(cache_dir)
                removed_count += 1
            except Exception as e:
                logger.warning(f"⚠️ Impossibile rimuovere {cache_dir}: {e}")
        
        if removed_count > 0:
            self.add_fix(f"Rimossi {removed_count} directory __pycache__")
        
        # Rimuovi file .pyc
        pyc_files = list(Path('.').rglob('*.pyc'))
        for pyc_file in pyc_files:
            try:
                pyc_file.unlink()
            except Exception:
                pass
        
        if pyc_files:
            self.add_fix(f"Rimossi {len(pyc_files)} file .pyc")
    
    def fix_file_permissions(self):
        """Corregge permessi file"""
        logger.info("🔧 Correzione permessi file...")
        
        critical_files = ['main.py', 'models.py', 'database.py', 'config.py']
        fixed_count = 0
        
        for filename in critical_files:
            if os.path.exists(filename):
                try:
                    # Su Windows, assicurati che il file non sia read-only
                    if os.name == 'nt':
                        import stat
                        file_path = Path(filename)
                        current_mode = file_path.stat().st_mode
                        if not (current_mode & stat.S_IWRITE):
                            file_path.chmod(current_mode | stat.S_IWRITE)
                            fixed_count += 1
                    
                except Exception as e:
                    logger.warning(f"⚠️ Impossibile correggere permessi per {filename}: {e}")
        
        if fixed_count > 0:
            self.add_fix(f"Corretti permessi per {fixed_count} file")
    
    def create_reload_config(self):
        """Crea configurazione ottimizzata per reload"""
        logger.info("⚙️ Creazione configurazione reload...")
        
        config_content = '''#!/usr/bin/env python3
"""
Configurazione ottimizzata per uvicorn reload
"""

import uvicorn
import os
from pathlib import Path

def run_server():
    """Avvia server con configurazione ottimizzata"""
    
    # Configurazione reload
    reload_dirs = [
        str(Path.cwd()),  # Directory corrente
    ]
    
    # Aggiungi directory aggiuntive se esistono
    additional_dirs = ['templates', 'static', 'models']
    for dir_name in additional_dirs:
        if os.path.exists(dir_name):
            reload_dirs.append(str(Path(dir_name).absolute()))
    
    # Configurazione uvicorn
    config = {
        "app": "main:app",
        "host": "0.0.0.0",
        "port": 8002,
        "reload": True,
        "reload_dirs": reload_dirs,
        "reload_delay": 0.5,  # Delay più breve
        "reload_includes": ["*.py"],  # Solo file Python
        "reload_excludes": [
            "*.pyc",
            "__pycache__/*",
            "*.log",
            "backups/*",
            "venv/*",
            ".git/*"
        ],
        "log_level": "info",
        "access_log": True,
        "use_colors": True,
    }
    
    print("🚀 Avvio server con configurazione ottimizzata...")
    print(f"📂 Directory monitorate: {len(reload_dirs)}")
    for dir_path in reload_dirs:
        print(f"   - {dir_path}")
    
    uvicorn.run(**config)

if __name__ == "__main__":
    run_server()
'''
        
        config_file = Path('run_optimized.py')
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            self.add_fix("Creato script run_optimized.py")
            
        except Exception as e:
            logger.error(f"❌ Errore creazione configurazione: {e}")
    
    def install_watchdog(self):
        """Installa watchdog se non presente"""
        logger.info("📦 Verifica installazione watchdog...")
        
        try:
            import watchdog
            logger.info("✅ Watchdog già installato")
        except ImportError:
            logger.info("📥 Installazione watchdog...")
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", "watchdog"
                ])
                self.add_fix("Installato watchdog per file monitoring")
            except subprocess.CalledProcessError as e:
                logger.error(f"❌ Errore installazione watchdog: {e}")
    
    def create_startup_script(self):
        """Crea script di avvio migliorato"""
        logger.info("📝 Creazione script avvio migliorato...")
        
        if os.name == 'nt':  # Windows
            script_content = '''@echo off
echo 🚀 AVVIO FASTAPI CON RELOAD OTTIMIZZATO
echo =====================================

REM Termina processi uvicorn esistenti
echo 🛑 Terminazione processi esistenti...
taskkill /f /im python.exe 2>nul
timeout /t 2 /nobreak >nul

REM Pulisci cache
echo 🧹 Pulizia cache...
for /d /r . %%d in (__pycache__) do @if exist "%%d" rd /s /q "%%d"
del /s /q *.pyc 2>nul

REM Avvia server
echo 🔄 Avvio server...
python run_optimized.py

pause
'''
            script_file = 'start_optimized.bat'
        else:  # Linux/Mac
            script_content = '''#!/bin/bash
echo "🚀 AVVIO FASTAPI CON RELOAD OTTIMIZZATO"
echo "====================================="

# Termina processi uvicorn esistenti
echo "🛑 Terminazione processi esistenti..."
pkill -f uvicorn 2>/dev/null || true
sleep 2

# Pulisci cache
echo "🧹 Pulizia cache..."
find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
find . -name "*.pyc" -delete 2>/dev/null || true

# Avvia server
echo "🔄 Avvio server..."
python run_optimized.py
'''
            script_file = 'start_optimized.sh'
        
        try:
            with open(script_file, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            # Rendi eseguibile su Linux/Mac
            if os.name != 'nt':
                os.chmod(script_file, 0o755)
            
            self.add_fix(f"Creato script avvio: {script_file}")
            
        except Exception as e:
            logger.error(f"❌ Errore creazione script: {e}")
    
    def run_fixes(self):
        """Esegue tutte le correzioni"""
        logger.info("🔧 AVVIO CORREZIONI RELOAD")
        logger.info("=" * 50)
        
        # Esegui correzioni
        self.kill_uvicorn_processes()
        self.clear_python_cache()
        self.fix_file_permissions()
        self.install_watchdog()
        self.create_reload_config()
        self.create_startup_script()
        
        # Report finale
        self.print_summary()
    
    def print_summary(self):
        """Stampa riassunto correzioni"""
        logger.info("=" * 50)
        logger.info("📋 RIASSUNTO CORREZIONI")
        logger.info("=" * 50)
        
        if self.fixes_applied:
            logger.info(f"✅ CORREZIONI APPLICATE: {len(self.fixes_applied)}")
            for i, fix in enumerate(self.fixes_applied, 1):
                logger.info(f"  {i}. {fix}")
        else:
            logger.info("ℹ️ Nessuna correzione necessaria")
        
        logger.info("")
        logger.info("🚀 PROSSIMI PASSI:")
        logger.info("1. Usa il nuovo script: python run_optimized.py")
        logger.info("2. Oppure: ./start_optimized.sh (Linux/Mac)")
        logger.info("3. Oppure: start_optimized.bat (Windows)")
        logger.info("=" * 50)

def main():
    """Funzione principale"""
    print("🔧 CORREZIONE PROBLEMI RELOAD UVICORN")
    print("=" * 50)
    print("Risolve automaticamente i problemi di auto-reload")
    print("=" * 50)
    
    fixer = ReloadFixer()
    fixer.run_fixes()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Correzione interrotta dall'utente")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Errore durante correzione: {e}")
        sys.exit(1)
