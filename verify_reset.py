#!/usr/bin/env python3
"""
Script per verificare che tutti i valori Agemar_GioiaTauro siano azzerati
"""

from sqlalchemy import text
from database import SessionLocal
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def verify_reset():
    """Verifica che tutti i valori Agemar_GioiaTauro siano azzerati"""
    db = SessionLocal()
    
    try:
        # Conta le navi totali
        result = db.execute(text('SELECT COUNT(*) FROM "NAVI"'))
        total_navi = result.scalar()
        
        # Conta le navi con valori NULL
        result = db.execute(text('''
            SELECT COUNT(*) 
            FROM "NAVI" 
            WHERE "Agemar_GioiaTauro" IS NULL
        '''))
        count_null = result.scalar()
        
        # Conta le navi con valori zero
        result = db.execute(text('''
            SELECT COUNT(*) 
            FROM "NAVI" 
            WHERE "Agemar_GioiaTauro" = 0
        '''))
        count_zero = result.scalar()
        
        # Conta le navi con valori diversi da NULL e zero
        result = db.execute(text('''
            SELECT COUNT(*) 
            FROM "NAVI" 
            WHERE "Agemar_GioiaTauro" IS NOT NULL AND "Agemar_GioiaTauro" != 0
        '''))
        count_with_values = result.scalar()
        
        print("🔍 VERIFICA AZZERAMENTO AGEMAR_GIOIATAURO")
        print("=" * 50)
        print(f"📊 Navi totali nel database: {total_navi}")
        print(f"📊 Navi con Agemar_GioiaTauro = NULL: {count_null}")
        print(f"📊 Navi con Agemar_GioiaTauro = 0: {count_zero}")
        print(f"📊 Navi con valori Agemar_GioiaTauro: {count_with_values}")
        
        if count_with_values == 0:
            print(f"\n✅ AZZERAMENTO CONFERMATO!")
            print(f"   Tutti i campi Agemar_GioiaTauro sono vuoti (NULL o 0)")
            print(f"   Percentuale azzerata: 100%")
        else:
            print(f"\n⚠️ ATTENZIONE!")
            print(f"   {count_with_values} navi hanno ancora valori Agemar_GioiaTauro")
            
            # Mostra esempi di navi con valori rimasti
            result = db.execute(text('''
                SELECT "Nave", "Agemar_GioiaTauro" 
                FROM "NAVI" 
                WHERE "Agemar_GioiaTauro" IS NOT NULL AND "Agemar_GioiaTauro" != 0
                ORDER BY "Agemar_GioiaTauro" DESC
                LIMIT 10
            '''))
            
            examples = result.fetchall()
            
            if examples:
                print(f"\n📋 ESEMPI DI NAVI CON VALORI RIMASTI:")
                for nave in examples:
                    print(f"   • {nave[0]}: {nave[1]}")
        
        # Verifica distribuzione dei valori
        result = db.execute(text('''
            SELECT 
                CASE 
                    WHEN "Agemar_GioiaTauro" IS NULL THEN 'NULL'
                    WHEN "Agemar_GioiaTauro" = 0 THEN 'ZERO'
                    ELSE 'CON_VALORE'
                END as tipo_valore,
                COUNT(*) as conteggio
            FROM "NAVI"
            GROUP BY 
                CASE 
                    WHEN "Agemar_GioiaTauro" IS NULL THEN 'NULL'
                    WHEN "Agemar_GioiaTauro" = 0 THEN 'ZERO'
                    ELSE 'CON_VALORE'
                END
            ORDER BY conteggio DESC
        '''))
        
        distribution = result.fetchall()
        
        print(f"\n📈 DISTRIBUZIONE VALORI AGEMAR_GIOIATAURO:")
        for tipo, conteggio in distribution:
            percentuale = (conteggio / total_navi * 100) if total_navi > 0 else 0
            print(f"   • {tipo}: {conteggio} navi ({percentuale:.1f}%)")
        
        return count_with_values == 0
        
    except Exception as e:
        logger.error(f"Errore verifica azzeramento: {e}")
        return False
        
    finally:
        db.close()

if __name__ == "__main__":
    verify_reset()
