# PROMPT COMPLETO PER RICREARE SNIP (Sistema Navale Integrato Portuale)

## OVERVIEW GENERALE
Crea un'applicazione web completa per la gestione portuale chiamata SNIP (Sistema Navale Integrato Portuale). L'applicazione deve gestire viaggi navali, equipaggi, documentazione SOF (Statement of Facts), import/export e operazioni portuali con un design moderno a tema marittimo.

## STACK TECNOLOGICO
- **Backend**: FastAPI (Python 3.8+)
- **Database**: PostgreSQL
- **Frontend**: HTML5, CSS3, JavaScript ES6+
- **UI Framework**: Bootstrap 5.3.2
- **Icone**: FontAwesome 6.4.0
- **Font**: Google Fonts (Poppins)
- **Librerie Python**: SQLAlchemy, Passlib (bcrypt), python-docx, psycopg2

## STRUTTURA DATABASE POSTGRESQL

### Tabella AGENTE
```sql
-- Crea tipo ENUM per ruoli
CREATE TYPE ruolo_enum AS ENUM ('ADMIN', 'SUPER_ADMIN', 'USER', 'VISITOR');

CREATE TABLE "AGENTE" (
    id SERIAL PRIMARY KEY,
    "Nome" VARCHAR(100) NOT NULL,
    "Cognome" VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    reparto VARCHAR(50) NOT NULL CHECK (reparto IN ('OPERATIVO', 'AMMINISTRAZIONE', 'SHORTSEA', 'CONTABILITA')),
    ruolo ruolo_enum DEFAULT 'USER',
    visibile VARCHAR(2) DEFAULT 'no' CHECK (visibile IN ('si', 'no')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Tabella ARMATORI
```sql
CREATE TABLE "ARMATORI" (
    id SERIAL PRIMARY KEY,
    "Armatore" VARCHAR(255) NOT NULL,
    "Codice_Armatore" VARCHAR(50) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Tabella NAVI
```sql
CREATE TABLE "NAVI" (
    id SERIAL PRIMARY KEY,
    "Nave" VARCHAR(255) NOT NULL,
    "Codice_Nave" VARCHAR(50) UNIQUE NOT NULL,
    "Prefisso_viaggio" VARCHAR(20) NOT NULL,
    armatore_id INTEGER REFERENCES "ARMATORI"(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Tabella PORTI_GESTIONE
```sql
CREATE TABLE "PORTI_GESTIONE" (
    id_porto SERIAL PRIMARY KEY,
    nome_porto VARCHAR(255) NOT NULL,
    codice_porto VARCHAR(10) UNIQUE NOT NULL,
    paese VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Tabella ATLAS
```sql
CREATE TABLE "ATLAS" (
    id SERIAL PRIMARY KEY,
    "ID_COD" VARCHAR(10) UNIQUE NOT NULL,
    "PORTI" VARCHAR(255) NOT NULL,
    "STATO" VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Tabella VIAGGIO
```sql
CREATE TABLE "VIAGGIO" (
    id SERIAL PRIMARY KEY,
    viaggio VARCHAR(100) NOT NULL,
    nave_id INTEGER REFERENCES "NAVI"(id) ON DELETE CASCADE,
    porto_gestione_id INTEGER REFERENCES "PORTI_GESTIONE"(id_porto) ON DELETE SET NULL,
    data_arrivo DATE,
    data_partenza DATE,
    visibile VARCHAR(2) DEFAULT 'si' CHECK (visibile IN ('si', 'no')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Tabella ORARI
```sql
CREATE TABLE "ORARI" (
    id SERIAL PRIMARY KEY,
    viaggio_id INTEGER REFERENCES "VIAGGIO"(id) ON DELETE CASCADE,
    porto_arrivo VARCHAR(255),
    sbe TIMESTAMP,
    pilota_arrivo TIMESTAMP,
    all_fast TIMESTAMP,
    tug_arrivo INTEGER DEFAULT 0,
    draft DECIMAL(5,2),
    soc TIMESTAMP,
    porto_destinazione VARCHAR(255),
    pilota_partenza TIMESTAMP,
    tug_partenza INTEGER DEFAULT 0,
    foc TIMESTAMP,
    fo DECIMAL(10,2),
    do DECIMAL(10,2),
    lo DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_orari_per_viaggio UNIQUE (viaggio_id)
);
```

### Tabella IMPORT
```sql
CREATE TABLE "IMPORT" (
    id SERIAL PRIMARY KEY,
    viaggio_id INTEGER REFERENCES "VIAGGIO"(id) ON DELETE CASCADE,
    pol VARCHAR(255) NOT NULL,
    pod VARCHAR(255) NOT NULL,
    qt DECIMAL(10,2) NOT NULL DEFAULT 0,
    type VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Tabella EXPORT
```sql
CREATE TABLE "EXPORT" (
    id SERIAL PRIMARY KEY,
    viaggio_id INTEGER REFERENCES "VIAGGIO"(id) ON DELETE CASCADE,
    pol VARCHAR(255) NOT NULL,
    pod VARCHAR(255) NOT NULL,
    qt DECIMAL(10,2) NOT NULL DEFAULT 0,
    type VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Tabella SOF_DOCUMENTS
```sql
CREATE TABLE "SOF_DOCUMENTS" (
    id SERIAL PRIMARY KEY,
    viaggio_id INTEGER NOT NULL REFERENCES "VIAGGIO"(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER NOT NULL,
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    statistics JSONB,
    CONSTRAINT unique_sof_per_viaggio UNIQUE (viaggio_id)
);
```

## STRUTTURA DIRECTORY
```
SNIP/
├── main.py                 # FastAPI app principale
├── database.py            # Configurazione database
├── models.py              # Modelli SQLAlchemy
├── requirements.txt       # Dipendenze Python
├── sof_documents/         # Directory storage SOF
├── static/
│   ├── css/
│   │   └── custom.css
│   └── js/
│       ├── alerts.js
│       ├── viaggio-dettaglio.js
│       ├── import-viaggio.js
│       ├── export-viaggio.js
│       ├── sof-viaggio.js
│       └── sof-realizzati.js
└── templates/
    ├── login.html
    ├── register.html
    ├── registration_success.html
    ├── base_operativo.html
    └── operativo/
        ├── dashboard.html
        ├── navi.html
        ├── armatori.html
        ├── porti.html
        ├── sof_da_realizzare.html
        ├── sof_realizzati.html
        └── viaggio_dettaglio.html
    └── amministrazione/
        └── dashboard.html
    └── shortsea/
        └── dashboard.html
    └── contabilita/
        └── dashboard.html
```

## REQUIREMENTS.TXT
```
fastapi==0.104.1
uvicorn==0.24.0
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
jinja2==3.1.2
python-docx==1.1.0
```

## DATABASE.PY
```python
from sqlalchemy import create_engine, Column, Integer, String, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import os

# URL del database PostgreSQL
DATABASE_URL = "postgresql://re77:271077@localhost:5432/AGENTE"

# Crea l'engine
engine = create_engine(DATABASE_URL)

# Crea la sessione
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base per i modelli
Base = declarative_base()

# Dependency per ottenere la sessione del database
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Modello per tabella ATLAS
class Atlas(Base):
    __tablename__ = "ATLAS"
    
    id = Column(Integer, primary_key=True, index=True)
    ID_COD = Column(String(10), unique=True, nullable=False)
    PORTI = Column(String(255), nullable=False)
    STATO = Column(String(100), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
```

## MODELS.PY
```python
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Date, DECIMAL, ForeignKey, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import enum

Base = declarative_base()

class RepartoEnum(enum.Enum):
    OPERATIVO = "OPERATIVO"
    AMMINISTRAZIONE = "AMMINISTRAZIONE"
    SHORTSEA = "SHORTSEA"
    CONTABILITA = "CONTABILITA"

class Agente(Base):
    __tablename__ = "AGENTE"

    id = Column(Integer, primary_key=True, index=True)
    Nome = Column(String(100), nullable=False)
    Cognome = Column(String(100), nullable=False)
    email = Column(String(255), unique=True, nullable=False)
    password = Column(String(255), nullable=False)
    reparto = Column(Enum(RepartoEnum), nullable=False)
    ruolo = Column(Enum(RuoloEnum), default=RuoloEnum.USER)
    visibile = Column(String(2), default='no')
    created_at = Column(DateTime, default=datetime.utcnow)

class Armatore(Base):
    __tablename__ = "ARMATORI"

    id = Column(Integer, primary_key=True, index=True)
    Armatore = Column(String(255), nullable=False)
    Codice_Armatore = Column(String(50), unique=True, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relazione con navi
    navi = relationship("Navi", back_populates="armatore")

class Navi(Base):
    __tablename__ = "NAVI"

    id = Column(Integer, primary_key=True, index=True)
    Nave = Column(String(255), nullable=False)
    Codice_Nave = Column(String(50), unique=True, nullable=False)
    Prefisso_viaggio = Column(String(20), nullable=False)
    armatore_id = Column(Integer, ForeignKey("ARMATORI.id"))
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relazioni
    armatore = relationship("Armatore", back_populates="navi")
    viaggi = relationship("Viaggio", back_populates="nave")

class PortiGestione(Base):
    __tablename__ = "PORTI_GESTIONE"

    id_porto = Column(Integer, primary_key=True, index=True)
    nome_porto = Column(String(255), nullable=False)
    codice_porto = Column(String(10), unique=True, nullable=False)
    paese = Column(String(100))
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relazione con viaggi
    viaggi = relationship("Viaggio", back_populates="porto_gestione")

class Viaggio(Base):
    __tablename__ = "VIAGGIO"

    id = Column(Integer, primary_key=True, index=True)
    viaggio = Column(String(100), nullable=False)
    nave_id = Column(Integer, ForeignKey("NAVI.id"))
    porto_gestione_id = Column(Integer, ForeignKey("PORTI_GESTIONE.id_porto"))
    data_arrivo = Column(Date)
    data_partenza = Column(Date)
    visibile = Column(String(2), default='si')
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relazioni
    nave = relationship("Navi", back_populates="viaggi")
    porto_gestione = relationship("PortiGestione", back_populates="viaggi")
    orari = relationship("Orari", back_populates="viaggio", uselist=False)
    imports = relationship("Import", back_populates="viaggio")
    exports = relationship("Export", back_populates="viaggio")

class Orari(Base):
    __tablename__ = "ORARI"

    id = Column(Integer, primary_key=True, index=True)
    viaggio_id = Column(Integer, ForeignKey("VIAGGIO.id"))
    porto_arrivo = Column(String(255))
    sbe = Column(DateTime)
    pilota_arrivo = Column(DateTime)
    all_fast = Column(DateTime)
    tug_arrivo = Column(Integer, default=0)
    draft = Column(DECIMAL(5,2))
    soc = Column(DateTime)
    porto_destinazione = Column(String(255))
    pilota_partenza = Column(DateTime)
    tug_partenza = Column(Integer, default=0)
    foc = Column(DateTime)
    fo = Column(DECIMAL(10,2))
    do = Column(DECIMAL(10,2))
    lo = Column(DECIMAL(10,2))
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relazione
    viaggio = relationship("Viaggio", back_populates="orari")

class Import(Base):
    __tablename__ = "IMPORT"

    id = Column(Integer, primary_key=True, index=True)
    viaggio_id = Column(Integer, ForeignKey("VIAGGIO.id"))
    pol = Column(String(255), nullable=False)
    pod = Column(String(255), nullable=False)
    qt = Column(DECIMAL(10,2), nullable=False, default=0)
    type = Column(String(255))
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relazione
    viaggio = relationship("Viaggio", back_populates="imports")

class Export(Base):
    __tablename__ = "EXPORT"

    id = Column(Integer, primary_key=True, index=True)
    viaggio_id = Column(Integer, ForeignKey("VIAGGIO.id"))
    pol = Column(String(255), nullable=False)
    pod = Column(String(255), nullable=False)
    qt = Column(DECIMAL(10,2), nullable=False, default=0)
    type = Column(String(255))
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relazione
    viaggio = relationship("Viaggio", back_populates="exports")
```

## MAIN.PY - STRUTTURA PRINCIPALE
```python
from fastapi import FastAPI, Request, Form, Depends, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse, FileResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from sqlalchemy.orm import Session
from passlib.context import CryptContext
from database import get_db
from models import *
import logging
import traceback
import json
import os
from datetime import datetime
from sqlalchemy.sql import text
from docx import Document
from docx.shared import Inches

# Configurazione logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

app = FastAPI()

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Templates
templates = Jinja2Templates(directory="templates")

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto", bcrypt__rounds=12)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

# ROUTES AUTENTICAZIONE
@app.get("/", response_class=HTMLResponse)
def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

@app.get("/login", response_class=HTMLResponse)
def login_page_alt(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

@app.get("/register", response_class=HTMLResponse)
def register_page(request: Request):
    return templates.TemplateResponse("register.html", {"request": request})

@app.post("/register", response_class=HTMLResponse)
def register(request: Request, nome: str = Form(...), cognome: str = Form(...),
            email: str = Form(...), password: str = Form(...), reparto: str = Form(...),
            db: Session = Depends(get_db)):
    try:
        # Verifica email esistente
        if db.query(Agente).filter(Agente.email == email).first():
            return templates.TemplateResponse("register.html",
                {"request": request, "error": "Email già registrata"})

        # Crea nuovo agente
        hashed_password = get_password_hash(password)
        from models import RuoloEnum

        new_agente = Agente(
            Nome=nome, Cognome=cognome, email=email,
            password=hashed_password, reparto=reparto,
            ruolo=RuoloEnum.USER  # Impostato automaticamente a USER
            # visibile userà il default 'no' dal database
        )
        db.add(new_agente)
        db.commit()

        return templates.TemplateResponse("registration_success.html",
            {"request": request, "nome": nome, "cognome": cognome,
             "email": email, "reparto": reparto, "ruolo": "USER"})
    except Exception as e:
        return templates.TemplateResponse("register.html",
            {"request": request, "error": f"Errore durante la registrazione: {str(e)}"})

@app.post("/login", response_class=HTMLResponse)
def login(request: Request, username: str = Form(...), password: str = Form(...),
          db: Session = Depends(get_db)):
    try:
        # Cerca utente
        result = db.execute(text('SELECT * FROM "AGENTE" WHERE email = :email'), {"email": username})
        user_data = result.fetchone()

        if not user_data:
            return templates.TemplateResponse("login.html",
                {"request": request, "error": "Utente non trovato"})

        # Verifica password
        if not verify_password(password, user_data.password):
            return templates.TemplateResponse("login.html",
                {"request": request, "error": "Password non valida"})

        # Verifica account bloccato
        if user_data.bloccato:
            return templates.TemplateResponse("login.html",
                {"request": request, "error": "Account bloccato"})

        # Redirect dashboard
        dashboard_url = f"/dashboard/{user_data.reparto.lower()}"
        return RedirectResponse(url=dashboard_url, status_code=303)

    except Exception as e:
        return templates.TemplateResponse("login.html",
            {"request": request, "error": f"Errore durante il login: {str(e)}"})

# ROUTES DASHBOARD
@app.get("/dashboard/operativo", response_class=HTMLResponse)
def dashboard_operativo(request: Request, db: Session = Depends(get_db)):
    try:
        # Statistiche dashboard
        total_viaggi = db.execute(text('SELECT COUNT(*) FROM "VIAGGIO"')).scalar()
        viaggi_visibili = db.execute(text('SELECT COUNT(*) FROM "VIAGGIO" WHERE visibile = \'si\'')).scalar()
        viaggi_completati = db.execute(text('SELECT COUNT(*) FROM "VIAGGIO" WHERE visibile = \'no\'')).scalar()
        total_navi = db.execute(text('SELECT COUNT(*) FROM "NAVI"')).scalar()

        stats = {
            "total_viaggi": total_viaggi,
            "viaggi_visibili": viaggi_visibili,
            "viaggi_completati": viaggi_completati,
            "total_navi": total_navi
        }

        return templates.TemplateResponse("operativo/dashboard.html",
            {"request": request, "stats": stats})
    except Exception as e:
        logger.error(f"Errore dashboard operativo: {e}")
        return templates.TemplateResponse("operativo/dashboard.html",
            {"request": request, "error": str(e)})

@app.get("/dashboard/amministrazione", response_class=HTMLResponse)
def dashboard_amministrazione(request: Request):
    return templates.TemplateResponse("amministrazione/dashboard.html", {"request": request})

@app.get("/dashboard/shortsea", response_class=HTMLResponse)
def dashboard_shortsea(request: Request):
    return templates.TemplateResponse("shortsea/dashboard.html", {"request": request})

@app.get("/dashboard/contabilita", response_class=HTMLResponse)
def dashboard_contabilita(request: Request):
    return templates.TemplateResponse("contabilita/dashboard.html", {"request": request})

# ROUTES SOF
@app.get("/operativo/sof/da-realizzare", response_class=HTMLResponse)
def sof_da_realizzare(request: Request, db: Session = Depends(get_db)):
    try:
        # Viaggi visibili (SOF da realizzare)
        viaggi = db.execute(text('''
            SELECT v.id, v.viaggio, n."Nave", v.data_arrivo, v.data_partenza
            FROM "VIAGGIO" v
            LEFT JOIN "NAVI" n ON v.nave_id = n.id
            WHERE v.visibile = 'si'
            ORDER BY v.data_arrivo DESC
        ''')).fetchall()

        return templates.TemplateResponse("operativo/sof_da_realizzare.html",
            {"request": request, "viaggi": viaggi})
    except Exception as e:
        logger.error(f"Errore SOF da realizzare: {e}")
        return templates.TemplateResponse("operativo/sof_da_realizzare.html",
            {"request": request, "error": str(e)})

@app.get("/operativo/sof/realizzati", response_class=HTMLResponse)
def sof_realizzati(request: Request, db: Session = Depends(get_db)):
    try:
        # Viaggi completati (SOF realizzati)
        viaggi = db.execute(text('''
            SELECT v.id, v.viaggio, n."Nave", v.data_arrivo, v.data_partenza,
                   s.filename, s.file_size, s.generated_at
            FROM "VIAGGIO" v
            LEFT JOIN "NAVI" n ON v.nave_id = n.id
            LEFT JOIN "SOF_DOCUMENTS" s ON v.id = s.viaggio_id
            WHERE v.visibile = 'no'
            ORDER BY v.data_arrivo DESC
        ''')).fetchall()

        return templates.TemplateResponse("operativo/sof_realizzati.html",
            {"request": request, "viaggi": viaggi})
    except Exception as e:
        logger.error(f"Errore SOF realizzati: {e}")
        return templates.TemplateResponse("operativo/sof_realizzati.html",
            {"request": request, "error": str(e)})

# FUNZIONE GENERAZIONE SOF DOCX
def generate_sof_docx(viaggio_data, orari_data, import_data, export_data):
    """Genera documento SOF in formato DOCX"""
    doc = Document()

    # Titolo
    title = doc.add_heading('STATEMENT OF FACTS (SOF)', 0)
    title.alignment = 1  # Centro

    # Informazioni viaggio
    doc.add_heading('INFORMAZIONI VIAGGIO', level=1)
    viaggio_table = doc.add_table(rows=4, cols=2)
    viaggio_table.style = 'Table Grid'

    viaggio_table.cell(0, 0).text = 'Viaggio:'
    viaggio_table.cell(0, 1).text = str(viaggio_data.get('viaggio', ''))
    viaggio_table.cell(1, 0).text = 'Nave:'
    viaggio_table.cell(1, 1).text = str(viaggio_data.get('nave', ''))
    viaggio_table.cell(2, 0).text = 'Data Arrivo:'
    viaggio_table.cell(2, 1).text = str(viaggio_data.get('data_arrivo', ''))
    viaggio_table.cell(3, 0).text = 'Data Partenza:'
    viaggio_table.cell(3, 1).text = str(viaggio_data.get('data_partenza', ''))

    # Orari
    if orari_data:
        doc.add_heading('ORARI OPERAZIONI', level=1)
        orari_table = doc.add_table(rows=8, cols=2)
        orari_table.style = 'Table Grid'

        orari_table.cell(0, 0).text = 'SBE:'
        orari_table.cell(0, 1).text = str(orari_data.get('sbe', ''))
        orari_table.cell(1, 0).text = 'Pilota Arrivo:'
        orari_table.cell(1, 1).text = str(orari_data.get('pilota_arrivo', ''))
        orari_table.cell(2, 0).text = 'All Fast:'
        orari_table.cell(2, 1).text = str(orari_data.get('all_fast', ''))
        orari_table.cell(3, 0).text = 'SOC:'
        orari_table.cell(3, 1).text = str(orari_data.get('soc', ''))
        orari_table.cell(4, 0).text = 'Pilota Partenza:'
        orari_table.cell(4, 1).text = str(orari_data.get('pilota_partenza', ''))
        orari_table.cell(5, 0).text = 'FOC:'
        orari_table.cell(5, 1).text = str(orari_data.get('foc', ''))
        orari_table.cell(6, 0).text = 'Draft:'
        orari_table.cell(6, 1).text = str(orari_data.get('draft', ''))
        orari_table.cell(7, 0).text = 'Tug Arrivo:'
        orari_table.cell(7, 1).text = str(orari_data.get('tug_arrivo', ''))

    # Import
    if import_data:
        doc.add_heading('IMPORT', level=1)
        import_table = doc.add_table(rows=len(import_data)+1, cols=4)
        import_table.style = 'Table Grid'

        # Header
        import_table.cell(0, 0).text = 'POL'
        import_table.cell(0, 1).text = 'POD'
        import_table.cell(0, 2).text = 'Quantità'
        import_table.cell(0, 3).text = 'Tipo'

        # Dati
        for i, imp in enumerate(import_data, 1):
            import_table.cell(i, 0).text = str(imp.get('pol', ''))
            import_table.cell(i, 1).text = str(imp.get('pod', ''))
            import_table.cell(i, 2).text = str(imp.get('qt', ''))
            import_table.cell(i, 3).text = str(imp.get('type', ''))

    # Export
    if export_data:
        doc.add_heading('EXPORT', level=1)
        export_table = doc.add_table(rows=len(export_data)+1, cols=4)
        export_table.style = 'Table Grid'

        # Header
        export_table.cell(0, 0).text = 'POL'
        export_table.cell(0, 1).text = 'POD'
        export_table.cell(0, 2).text = 'Quantità'
        export_table.cell(0, 3).text = 'Tipo'

        # Dati
        for i, exp in enumerate(export_data, 1):
            export_table.cell(i, 0).text = str(exp.get('pol', ''))
            export_table.cell(i, 1).text = str(exp.get('pod', ''))
            export_table.cell(i, 2).text = str(exp.get('qt', ''))
            export_table.cell(i, 3).text = str(exp.get('type', ''))

    return doc

# ENDPOINT DOWNLOAD SOF
@app.post("/operativo/sof/viaggio/{viaggio_id}/sof/download")
def download_sof(viaggio_id: int, db: Session = Depends(get_db)):
    try:
        # Controlla se esiste già un SOF salvato
        existing_sof = db.execute(text("""
            SELECT file_path, filename, file_size, statistics
            FROM "SOF_DOCUMENTS"
            WHERE viaggio_id = :viaggio_id
        """), {"viaggio_id": viaggio_id}).fetchone()

        if existing_sof and os.path.exists(existing_sof[0]):
            # SOF già esistente, restituisci quello salvato
            logger.info(f"Restituendo SOF già salvato per viaggio {viaggio_id}: {existing_sof[1]}")

            # Assicurati che il viaggio sia impostato come non visibile
            viaggio_result = db.execute(text('SELECT visibile FROM "VIAGGIO" WHERE id = :viaggio_id'),
                                      {"viaggio_id": viaggio_id}).fetchone()

            if viaggio_result and viaggio_result[0] == 'si':
                db.execute(text("""
                    UPDATE "VIAGGIO"
                    SET visibile = 'no'
                    WHERE id = :viaggio_id
                """), {"viaggio_id": viaggio_id})
                db.commit()
                logger.info(f"Viaggio {viaggio_id} impostato come non visibile (ha SOF salvato)")

            return FileResponse(
                path=existing_sof[0],
                filename=existing_sof[1],
                media_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                headers={
                    "Content-Disposition": f"attachment; filename={existing_sof[1]}",
                    "X-SOF-Statistics": json.dumps(existing_sof[3]) if existing_sof[3] else "{}"
                }
            )
        else:
            # Genera nuovo SOF
            # [Qui va la logica completa di generazione SOF con tutti i dati]
            # Salva il file nella directory sof_documents/
            # Registra nel database SOF_DOCUMENTS
            # Imposta viaggio come non visibile
            pass

    except Exception as e:
        logger.error(f"Errore download SOF: {e}")
        raise HTTPException(status_code=500, detail=str(e))
```

## TEMPLATE LOGIN.HTML - DESIGN MODERNO
```html
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚢 SNIP - Sistema Navale Integrato Portuale</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background:
                linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%),
                url('/static/images/cargo-ship.jpg') center/cover no-repeat fixed;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        /* Sfondo animato con onde */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23ffffff" fill-opacity="0.1" d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,122.7C672,117,768,139,864,149.3C960,160,1056,160,1152,138.7C1248,117,1344,75,1392,53.3L1440,32L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>') no-repeat bottom;
            background-size: cover;
            animation: wave 6s ease-in-out infinite;
        }

        @keyframes wave {
            0%, 100% { transform: translateX(0px); }
            50% { transform: translateX(-50px); }
        }

        /* Particelle fluttuanti */
        .floating-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 15s infinite linear;
        }

        .particle:nth-child(1) { width: 20px; height: 20px; left: 10%; animation-delay: 0s; }
        .particle:nth-child(2) { width: 15px; height: 15px; left: 20%; animation-delay: 2s; }
        .particle:nth-child(3) { width: 25px; height: 25px; left: 30%; animation-delay: 4s; }
        .particle:nth-child(4) { width: 18px; height: 18px; left: 40%; animation-delay: 6s; }
        .particle:nth-child(5) { width: 22px; height: 22px; left: 50%; animation-delay: 8s; }

        @keyframes float {
            0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
        }

        /* Container principale */
        .login-container {
            position: relative;
            z-index: 10;
            max-width: 450px;
            width: 90%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.2);
            padding: 40px 35px;
            animation: slideUp 0.8s ease-out;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Header con logo */
        .header-section {
            text-align: center;
            margin-bottom: 35px;
        }

        .logo-container {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .logo-icon {
            font-size: 2.5rem;
            color: white;
        }

        .app-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }

        .app-subtitle {
            font-size: 0.95rem;
            color: #718096;
            font-weight: 400;
            margin-bottom: 0;
        }

        /* Form styling */
        .form-group {
            position: relative;
            margin-bottom: 25px;
        }

        .form-label {
            font-weight: 500;
            color: #4a5568;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 12px 16px 12px 45px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
        }

        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #a0aec0;
            font-size: 1.1rem;
            transition: color 0.3s ease;
        }

        .form-control:focus + .input-icon {
            color: #667eea;
        }

        /* Pulsante login */
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 14px 24px;
            font-weight: 600;
            font-size: 1rem;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        /* Alert personalizzato */
        .custom-alert {
            background: rgba(248, 113, 113, 0.1);
            border: 1px solid rgba(248, 113, 113, 0.2);
            border-radius: 12px;
            padding: 12px 16px;
            margin-bottom: 20px;
            color: #dc2626;
            font-size: 0.9rem;
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        /* Link registrazione */
        .register-link {
            text-align: center;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }

        .register-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .register-link a:hover {
            color: #764ba2;
            text-decoration: underline;
        }

        /* Responsive */
        @media (max-width: 480px) {
            .login-container {
                margin: 20px;
                padding: 30px 25px;
            }

            .app-title {
                font-size: 1.5rem;
            }

            .logo-container {
                width: 70px;
                height: 70px;
            }

            .logo-icon {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Particelle fluttuanti -->
    <div class="floating-particles">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <!-- Container principale -->
    <div class="login-container">
        <!-- Header con logo -->
        <div class="header-section">
            <div class="logo-container">
                <i class="fas fa-anchor logo-icon"></i>
            </div>
            <h1 class="app-title">SNIP</h1>
            <p class="app-subtitle">Sistema Navale Integrato Portuale</p>
        </div>

        <!-- Alert errori -->
        {% if error %}
        <div class="custom-alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {{ error }}
        </div>
        {% endif %}

        <!-- Form di login -->
        <form method="post" action="/login" id="loginForm">
            <div class="form-group">
                <label for="username" class="form-label">
                    <i class="fas fa-envelope me-2"></i>Indirizzo Email
                </label>
                <input type="email" class="form-control" id="username" name="username" required autofocus
                       placeholder="<EMAIL>">
                <i class="fas fa-envelope input-icon"></i>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">
                    <i class="fas fa-lock me-2"></i>Password
                </label>
                <input type="password" class="form-control" id="password" name="password" required
                       placeholder="••••••••">
                <i class="fas fa-lock input-icon"></i>
            </div>

            <button type="submit" class="btn btn-login">
                <i class="fas fa-sign-in-alt me-2"></i>
                Accedi al Sistema
            </button>
        </form>

        <!-- Link registrazione -->
        <div class="register-link">
            <a href="/register">
                <i class="fas fa-user-plus me-2"></i>
                Non hai un account? Registrati qui
            </a>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Animazione form
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('loginForm');
            const inputs = form.querySelectorAll('.form-control');

            // Animazione focus input
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                });

                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });
            });

            // Animazione submit
            form.addEventListener('submit', function(e) {
                const submitBtn = form.querySelector('.btn-login');
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Accesso in corso...';
                submitBtn.disabled = true;
            });
        });
    </script>
</body>
</html>
```

## DASHBOARD OPERATIVO - TEMPLATE BASE
```html
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚢 SNIP - Dashboard Operativo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
        }

        .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: white !important;
            transform: translateY(-2px);
        }

        .card {
            border: none;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .stat-card .card-body {
            padding: 2rem;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .sidebar {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .sidebar-link {
            display: block;
            padding: 12px 16px;
            color: #4a5568;
            text-decoration: none;
            border-radius: 8px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }

        .sidebar-link:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateX(5px);
        }

        .sidebar-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-anchor me-2"></i>SNIP
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard/operativo">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/operativo/sof/da-realizzare">
                            <i class="fas fa-file-alt me-2"></i>SOF da Realizzare
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/operativo/sof/realizzati">
                            <i class="fas fa-check-circle me-2"></i>SOF Realizzati
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/operativo/navi">
                            <i class="fas fa-ship me-2"></i>Navi
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/logout">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3">
                <div class="sidebar">
                    <h5 class="mb-3">
                        <i class="fas fa-cogs me-2"></i>Operativo
                    </h5>
                    <a href="/dashboard/operativo" class="sidebar-link active">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a href="/operativo/sof/da-realizzare" class="sidebar-link">
                        <i class="fas fa-file-alt me-2"></i>SOF da Realizzare
                    </a>
                    <a href="/operativo/sof/realizzati" class="sidebar-link">
                        <i class="fas fa-check-circle me-2"></i>SOF Realizzati
                    </a>
                    <a href="/operativo/navi" class="sidebar-link">
                        <i class="fas fa-ship me-2"></i>Gestione Navi
                    </a>
                    <a href="/operativo/armatori" class="sidebar-link">
                        <i class="fas fa-building me-2"></i>Armatori
                    </a>
                    <a href="/operativo/porti" class="sidebar-link">
                        <i class="fas fa-anchor me-2"></i>Porti
                    </a>
                </div>
            </div>

            <!-- Content Area -->
            <div class="col-md-9">
                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <div class="stat-number">{{ stats.total_viaggi or 0 }}</div>
                                <div class="stat-label">Viaggi Totali</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <div class="stat-number">{{ stats.viaggi_visibili or 0 }}</div>
                                <div class="stat-label">SOF da Realizzare</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <div class="stat-number">{{ stats.viaggi_completati or 0 }}</div>
                                <div class="stat-label">SOF Completati</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <div class="stat-number">{{ stats.total_navi or 0 }}</div>
                                <div class="stat-label">Navi Gestite</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-file-alt me-2"></i>SOF da Realizzare
                                </h5>
                                <p class="card-text">Gestisci i Statement of Facts in attesa di completamento.</p>
                                <a href="/operativo/sof/da-realizzare" class="btn btn-primary">
                                    <i class="fas fa-arrow-right me-2"></i>Vai ai SOF
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-ship me-2"></i>Gestione Navi
                                </h5>
                                <p class="card-text">Amministra la flotta e i dettagli delle imbarcazioni.</p>
                                <a href="/operativo/navi" class="btn btn-primary">
                                    <i class="fas fa-arrow-right me-2"></i>Gestisci Navi
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
```

## SPECIFICHE DESIGN E UI

### TEMA MARITTIMO
- **Colori primari**: Blu oceano (#667eea), Viola profondo (#764ba2)
- **Sfondo**: Immagine cargo-ship.jpg con overlay gradiente trasparente
- **Icone**: FontAwesome con tema nautico (anchor, ship, waves)
- **Animazioni**: Onde, particelle fluttuanti, effetti acqua
- **Typography**: Poppins (moderno e leggibile)

### GLASSMORPHISM
- **Background**: `rgba(255, 255, 255, 0.95)` con `backdrop-filter: blur(20px)`
- **Bordi**: `border-radius: 16px-24px` per elementi principali
- **Ombre**: `box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1)`
- **Transizioni**: `transition: all 0.3s ease` per fluidità

### ANIMAZIONI
- **Slide-up**: Elementi che entrano dal basso
- **Hover effects**: Transform translateY(-2px) e scale(1.05)
- **Loading states**: Spinner con rotazione
- **Particelle**: Float animation per sfondo dinamico

### RESPONSIVE DESIGN
- **Mobile first**: Design ottimizzato per dispositivi mobili
- **Breakpoints**: 480px (mobile), 768px (tablet), 1200px (desktop)
- **Grid system**: Bootstrap 5 con layout flessibili
- **Touch friendly**: Pulsanti e link di dimensioni adeguate

## FUNZIONALITÀ CORE RICHIESTE

### SISTEMA SOF (STATEMENT OF FACTS)
1. **Generazione DOCX**: Documento Word con tutti i dati viaggio
2. **Storage permanente**: Directory `sof_documents/` con file salvati
3. **Logica intelligente**:
   - Viaggi visibili → Genera nuovo SOF
   - Viaggi completati → Scarica SOF salvato
4. **Database tracking**: Tabella SOF_DOCUMENTS per metadati

### GESTIONE VIAGGI
1. **Stati viaggio**: `visibile = 'si'` (attivo) / `'no'` (completato)
2. **Dati completi**: Orari, import/export, navi, porti
3. **Interfaccia dettaglio**: Form per inserimento/modifica dati
4. **Validazione**: Controlli client-side e server-side

### DASHBOARD MULTI-REPARTO
1. **Operativo**: Gestione SOF, viaggi, navi
2. **Amministrazione**: Gestione utenti, configurazioni
3. **Shortsea**: Operazioni costiere specifiche
4. **Contabilità**: Report finanziari e fatturazione

### AUTENTICAZIONE
1. **Login sicuro**: Hash bcrypt per password
2. **Registrazione**: Form con validazione email
3. **Controllo accessi**: Redirect basato su reparto
4. **Sessioni**: Gestione stato utente

## ISTRUZIONI IMPLEMENTAZIONE

### SETUP INIZIALE
1. **Crea database PostgreSQL** con nome "AGENTE"
2. **Esegui script SQL** per creare tutte le tabelle
3. **Installa dipendenze**: `pip install -r requirements.txt`
4. **Crea directory**: `mkdir sof_documents`
5. **Avvia server**: `uvicorn main:app --reload`

### CONFIGURAZIONE DATABASE
```python
# Modifica DATABASE_URL in database.py
DATABASE_URL = "postgresql://username:password@localhost:5432/AGENTE"
```

### DATI DI TEST
Inserire dati di esempio per:
- Almeno 3 agenti (uno per reparto)
- 5-10 navi con armatori
- 10-15 viaggi con stati diversi
- Porti principali italiani
- Dati import/export di esempio

### TESTING
1. **Test login**: Verifica autenticazione
2. **Test SOF**: Genera e scarica documenti
3. **Test responsive**: Mobile, tablet, desktop
4. **Test navigazione**: Tutti i link funzionanti

## DELIVERABLE FINALE

### STRUTTURA COMPLETA
```
SNIP/
├── main.py (FastAPI app completa)
├── database.py (Configurazione DB)
├── models.py (Modelli SQLAlchemy)
├── requirements.txt (Dipendenze)
├── sof_documents/ (Storage SOF)
├── static/
│   ├── css/custom.css
│   └── js/ (File JavaScript)
└── templates/
    ├── login.html (Design moderno)
    ├── register.html (Design moderno)
    └── operativo/ (Dashboard e pagine)
```

### FUNZIONALITÀ GARANTITE
✅ **Login/Registrazione** con design glassmorphism
✅ **Dashboard multi-reparto** responsive
✅ **Sistema SOF completo** con storage permanente
✅ **Gestione viaggi** con tutti i dati
✅ **Design moderno** tema marittimo
✅ **Animazioni fluide** e interazioni
✅ **Database completo** con relazioni
✅ **Responsive design** mobile-first

### REQUISITI SODDISFATTI
- ✅ Stack FastAPI + PostgreSQL + Bootstrap 5
- ✅ Design glassmorphism con tema marittimo
- ✅ Sistema SOF con generazione DOCX
- ✅ Storage permanente documenti
- ✅ Dashboard multi-reparto funzionali
- ✅ Autenticazione sicura
- ✅ Responsive design completo
- ✅ Animazioni e interazioni moderne
- ✅ Database schema completo
- ✅ Gestione errori robusta

L'applicazione SNIP sarà completamente funzionante e pronta per l'uso in ambiente di produzione, con tutte le funzionalità richieste implementate secondo le specifiche moderne di design e usabilità.
```
