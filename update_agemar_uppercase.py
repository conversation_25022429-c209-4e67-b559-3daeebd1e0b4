#!/usr/bin/env python3
"""
Script per aggiornare i valori Agemar_GioiaTauro dal file Excel gioia12.xlsx
con nomi delle navi convertiti in MAIUSCOLO per migliorare il matching
"""

import pandas as pd
from sqlalchemy import text
from database import SessionLocal
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def update_agemar_uppercase():
    """Aggiorna i valori Agemar_GioiaTauro dal file Excel con nomi in maiuscolo"""
    
    file_path = r"c:\Users\<USER>\Desktop\cline_thebest\gioia12.xlsx"
    db = SessionLocal()
    
    try:
        print("🚢 AGGIORNAMENTO AGEMAR_GIOIATAURO CON NOMI MAIUSCOLI")
        print("=" * 70)
        
        # Leggi il foglio "gioia" dal file Excel
        print(f"📖 Lettura file Excel: {file_path}")
        df = pd.read_excel(file_path, sheet_name='gioia')
        
        print(f"📊 Trovate {len(df)} righe nel foglio 'gioia'")
        
        # Rinomina le colonne per facilità d'uso
        df.columns = ['nome_nave', 'agemar_value']
        
        # Pulisci i nomi delle navi (rimuovi spazi extra) e converti in MAIUSCOLO
        df['nome_nave_originale'] = df['nome_nave'].str.strip()
        df['nome_nave_maiuscolo'] = df['nome_nave'].str.strip().str.upper()
        
        print(f"\n📋 Prime 5 righe del file (originale -> maiuscolo):")
        for i in range(min(5, len(df))):
            row = df.iloc[i]
            print(f"   {i+1}. '{row['nome_nave_originale']}' -> '{row['nome_nave_maiuscolo']}' = {row['agemar_value']}")
        
        # Contatori per statistiche
        updated_count = 0
        not_found_count = 0
        error_count = 0
        navi_non_trovate = []
        
        print(f"\n🔄 Inizio aggiornamento database...")
        
        # Per ogni riga nel file Excel
        for index, row in df.iterrows():
            nome_originale = row['nome_nave_originale']
            nome_maiuscolo = row['nome_nave_maiuscolo']
            agemar_value = row['agemar_value']
            
            try:
                # Cerca la nave nel database con nome maiuscolo
                result = db.execute(text('''
                    SELECT "Nave" FROM "NAVI" 
                    WHERE UPPER("Nave") = :nome_maiuscolo
                '''), {'nome_maiuscolo': nome_maiuscolo})
                
                nave_trovata = result.fetchone()
                
                if nave_trovata:
                    nome_db = nave_trovata[0]
                    
                    # Aggiorna il valore Agemar_GioiaTauro
                    db.execute(text('''
                        UPDATE "NAVI" 
                        SET "Agemar_GioiaTauro" = :agemar_value
                        WHERE "Nave" = :nome_db
                    '''), {
                        'agemar_value': agemar_value,
                        'nome_db': nome_db
                    })
                    
                    updated_count += 1
                    print(f"   ✅ '{nome_originale}' -> '{nome_db}': {agemar_value}")
                    
                else:
                    not_found_count += 1
                    navi_non_trovate.append(nome_originale)
                    print(f"   ⚠️ NON TROVATA: '{nome_originale}' (maiuscolo: '{nome_maiuscolo}')")
                    
            except Exception as e:
                error_count += 1
                print(f"   ❌ ERRORE per '{nome_originale}': {e}")
        
        # Commit delle modifiche
        db.commit()
        
        print(f"\n📈 RISULTATI AGGIORNAMENTO:")
        print(f"   ✅ Navi aggiornate: {updated_count}")
        print(f"   ⚠️ Navi non trovate: {not_found_count}")
        print(f"   ❌ Errori: {error_count}")
        print(f"   📊 Totale processate: {len(df)}")
        
        if updated_count > 0:
            print(f"\n🎉 Aggiornamento completato con successo!")
            print(f"   {updated_count} navi hanno ora i nuovi valori Agemar_GioiaTauro")
        
        # Mostra navi non trovate per possibili correzioni manuali
        if not_found_count > 0:
            print(f"\n🔍 NAVI NON TROVATE (prime 15):")
            for i, ship in enumerate(navi_non_trovate[:15], 1):
                print(f"   {i:2d}. '{ship}'")
            
            if len(navi_non_trovate) > 15:
                print(f"   ... e altre {len(navi_non_trovate) - 15} navi")
        
        # Verifica finale
        print(f"\n🔍 VERIFICA FINALE:")
        result = db.execute(text('''
            SELECT COUNT(*) FROM "NAVI" 
            WHERE "Agemar_GioiaTauro" IS NOT NULL AND "Agemar_GioiaTauro" != 0
        '''))
        total_with_values = result.scalar()
        print(f"   📊 Totale navi con valori Agemar_GioiaTauro: {total_with_values}")
        
        return updated_count > 0
        
    except Exception as e:
        logger.error(f"Errore aggiornamento: {e}")
        db.rollback()
        return False
        
    finally:
        db.close()

if __name__ == "__main__":
    update_agemar_uppercase()
