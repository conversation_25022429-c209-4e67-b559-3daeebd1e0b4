#!/usr/bin/env python3
"""
Sistema centralizzato per la gestione degli errori in SNIP
Fornisce messaggi di errore chiari e consistenti per gli utenti
"""

from fastapi import HTTPException
from fastapi.responses import JSONResponse
from sqlalchemy.exc import IntegrityError, OperationalError
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class SNIPError(Exception):
    """Eccezione base per errori SNIP"""
    def __init__(self, message: str, error_code: str = "GENERIC_ERROR", status_code: int = 500):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        super().__init__(self.message)

class ValidationError(SNIPError):
    """Errore di validazione dati"""
    def __init__(self, message: str, field: Optional[str] = None):
        self.field = field
        super().__init__(message, "VALIDATION_ERROR", 400)

class DatabaseError(SNIPError):
    """Errore database"""
    def __init__(self, message: str, original_error: Optional[Exception] = None):
        self.original_error = original_error
        super().__init__(message, "DATABASE_ERROR", 500)

class AuthenticationError(SNIPError):
    """Errore di autenticazione"""
    def __init__(self, message: str = "Accesso non autorizzato"):
        super().__init__(message, "AUTH_ERROR", 401)

class PermissionError(SNIPError):
    """Errore di permessi"""
    def __init__(self, message: str = "Permessi insufficienti"):
        super().__init__(message, "PERMISSION_ERROR", 403)

class FileError(SNIPError):
    """Errore gestione file"""
    def __init__(self, message: str, file_name: Optional[str] = None):
        self.file_name = file_name
        super().__init__(message, "FILE_ERROR", 400)

def handle_database_error(e: Exception) -> JSONResponse:
    """Gestisce errori database con messaggi user-friendly"""
    error_message = "Errore del database"
    
    if isinstance(e, IntegrityError):
        if "UNIQUE constraint failed" in str(e):
            error_message = "Questo record esiste già nel sistema"
        elif "FOREIGN KEY constraint failed" in str(e):
            error_message = "Impossibile completare l'operazione: riferimento a dati inesistenti"
        elif "NOT NULL constraint failed" in str(e):
            error_message = "Campi obbligatori mancanti"
        else:
            error_message = "Violazione delle regole di integrità dei dati"
    
    elif isinstance(e, OperationalError):
        if "database is locked" in str(e):
            error_message = "Database temporaneamente non disponibile, riprova tra poco"
        elif "no such table" in str(e):
            error_message = "Errore di configurazione database"
        else:
            error_message = "Errore operativo del database"
    
    logger.error(f"Database error: {str(e)}")
    
    return JSONResponse({
        "success": False,
        "message": error_message,
        "error_code": "DATABASE_ERROR"
    }, status_code=500)

def handle_validation_error(field: str, value: Any, expected_type: str) -> ValidationError:
    """Crea errore di validazione con messaggio chiaro"""
    if value is None or value == "":
        return ValidationError(f"Il campo '{field}' è obbligatorio", field)
    
    if expected_type == "email":
        return ValidationError(f"Il campo '{field}' deve contenere un indirizzo email valido", field)
    elif expected_type == "number":
        return ValidationError(f"Il campo '{field}' deve contenere un numero valido", field)
    elif expected_type == "date":
        return ValidationError(f"Il campo '{field}' deve contenere una data valida", field)
    elif expected_type == "phone":
        return ValidationError(f"Il campo '{field}' deve contenere un numero di telefono valido", field)
    else:
        return ValidationError(f"Il campo '{field}' non è valido", field)

def handle_file_error(filename: Optional[str], error_type: str) -> FileError:
    """Crea errore file con messaggio chiaro"""
    if error_type == "missing":
        return FileError("File non fornito")
    elif error_type == "format":
        return FileError(f"Formato file non supportato: {filename}", filename)
    elif error_type == "size":
        return FileError(f"File troppo grande: {filename}", filename)
    elif error_type == "empty":
        return FileError(f"File vuoto: {filename}", filename)
    elif error_type == "corrupted":
        return FileError(f"File corrotto o non leggibile: {filename}", filename)
    else:
        return FileError(f"Errore nel file: {filename}", filename)

def create_error_response(error: SNIPError) -> JSONResponse:
    """Crea risposta JSON standardizzata per errori SNIP"""
    response_data = {
        "success": False,
        "message": error.message,
        "error_code": error.error_code
    }
    
    # Aggiungi informazioni specifiche per tipo di errore
    if isinstance(error, ValidationError) and error.field:
        response_data["field"] = error.field
    elif isinstance(error, FileError) and error.file_name:
        response_data["file_name"] = error.file_name
    
    return JSONResponse(response_data, status_code=error.status_code)

def create_success_response(message: str, data: Optional[Dict[str, Any]] = None) -> JSONResponse:
    """Crea risposta JSON standardizzata per successo"""
    response_data = {
        "success": True,
        "message": message
    }
    
    if data:
        response_data.update(data)
    
    return JSONResponse(response_data)

def log_error(error: Exception, context: str = "", user_id: Optional[int] = None):
    """Log centralizzato degli errori con contesto"""
    error_info = {
        "error_type": type(error).__name__,
        "error_message": str(error),
        "context": context
    }
    
    if user_id:
        error_info["user_id"] = user_id
    
    logger.error(f"SNIP Error: {error_info}")

# Messaggi di errore comuni
ERROR_MESSAGES = {
    "INVALID_CREDENTIALS": "Email o password non corretti",
    "SESSION_EXPIRED": "Sessione scaduta, effettua nuovamente il login",
    "INSUFFICIENT_PERMISSIONS": "Non hai i permessi necessari per questa operazione",
    "RESOURCE_NOT_FOUND": "Risorsa non trovata",
    "DUPLICATE_ENTRY": "Questo elemento esiste già",
    "INVALID_INPUT": "Dati inseriti non validi",
    "FILE_TOO_LARGE": "File troppo grande",
    "UNSUPPORTED_FORMAT": "Formato non supportato",
    "DATABASE_UNAVAILABLE": "Database temporaneamente non disponibile",
    "NETWORK_ERROR": "Errore di connessione",
    "MAINTENANCE_MODE": "Sistema in manutenzione",
    "RATE_LIMIT_EXCEEDED": "Troppe richieste, riprova più tardi"
}

def get_user_friendly_message(error_code: str) -> str:
    """Ottiene messaggio user-friendly per codice errore"""
    return ERROR_MESSAGES.get(error_code, "Si è verificato un errore imprevisto")

# Decorator per gestione automatica errori
def handle_errors(func):
    """Decorator per gestione automatica degli errori"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except SNIPError as e:
            return create_error_response(e)
        except IntegrityError as e:
            return handle_database_error(e)
        except OperationalError as e:
            return handle_database_error(e)
        except Exception as e:
            log_error(e, f"Unexpected error in {func.__name__}")
            return JSONResponse({
                "success": False,
                "message": "Si è verificato un errore imprevisto",
                "error_code": "UNEXPECTED_ERROR"
            }, status_code=500)
    
    return wrapper

# Validatori comuni
def validate_email(email: str) -> bool:
    """Valida formato email"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_phone(phone: str) -> bool:
    """Valida formato telefono"""
    import re
    # Accetta formati: +39 123 456789, 123-456-789, 123.456.789, 123456789
    pattern = r'^[\+]?[0-9\s\-\.]{8,15}$'
    return re.match(pattern, phone) is not None

def validate_required_fields(data: Dict[str, Any], required_fields: list) -> None:
    """Valida che i campi obbligatori siano presenti"""
    missing_fields = []
    
    for field in required_fields:
        if field not in data or not data[field] or str(data[field]).strip() == "":
            missing_fields.append(field)
    
    if missing_fields:
        raise ValidationError(f"Campi obbligatori mancanti: {', '.join(missing_fields)}")

def sanitize_input(value: str, max_length: int = 255) -> str:
    """Sanitizza input utente"""
    if not value:
        return ""
    
    # Rimuovi caratteri pericolosi
    sanitized = str(value).strip()
    
    # Limita lunghezza
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length]
    
    return sanitized
