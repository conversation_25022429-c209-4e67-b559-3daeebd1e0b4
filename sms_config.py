"""
Configurazione per l'invio di notifiche 2FA (SMS ed Email)
Supporta diversi provider SMS e servizi email
"""

import os
from typing import Dict, Any

# Configurazioni per l'invio email
EMAIL_CONFIG = {
    'smtp_server': 'smtp.gmail.com',
    'smtp_port': 587,
    'email_address': '<EMAIL>',
    'email_password': 'sxfv xkjf afur vuhr',  # App password Gmail
    'admin_email': '<EMAIL>'
}

# Configurazioni per diversi provider SMS
SMS_PROVIDERS = {
    # TextBelt (gratuito ma limitato)
    'textbelt': {
        'url': 'https://textbelt.com/text',
        'method': 'POST',
        'data_format': {
            'phone': '{phone}',
            'message': '{message}',
            'key': 'textbelt'  # Chiave gratuita limitata
        },
        'success_check': lambda r: r.json().get('success', False)
    },
    
    # Twilio (servizio professionale - richiede account)
    'twilio': {
        'url': 'https://api.twilio.com/2010-04-01/Accounts/{account_sid}/Messages.json',
        'method': 'POST',
        'auth': ('account_sid', 'auth_token'),  # Da configurare nelle variabili d'ambiente
        'data_format': {
            'From': '{from_number}',
            'To': '{phone}',
            'Body': '{message}'
        },
        'success_check': lambda r: r.status_code == 201
    },
    
    # SMS.it (provider italiano)
    'smsit': {
        'url': 'https://api.sms.it/api/v1/send',
        'method': 'POST',
        'headers': {
            'Authorization': 'Bearer {api_key}'
        },
        'data_format': {
            'recipient': '{phone}',
            'message': '{message}',
            'sender': 'SNIP'
        },
        'success_check': lambda r: r.json().get('status') == 'success'
    }
}

# Configurazione predefinita
DEFAULT_PROVIDER = 'textbelt'
ADMIN_PHONE = "**********"

# Metodo di notifica predefinito (email o sms)
DEFAULT_NOTIFICATION_METHOD = 'email'

# Messaggi personalizzabili
MESSAGES = {
    'startup_code': 'Codice di avvio applicazione SNIP: {code}',
    'security_alert': 'Tentativo di accesso non autorizzato all\'applicazione SNIP',
    'email_subject': 'Codice di Sicurezza SNIP - Avvio Applicazione',
    'email_body': '''
Ciao,

È stato richiesto l'avvio dell'applicazione SNIP.

Il tuo codice di sicurezza è: {code}

Inserisci questo codice per completare l'accesso.

Se non hai richiesto tu questo accesso, ignora questa email.

Saluti,
Sistema di Sicurezza SNIP
'''
}

def load_env_file():
    """Carica il file .env se esiste"""
    try:
        env_path = '.env'
        if os.path.exists(env_path):
            with open(env_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
    except Exception:
        pass  # Ignora errori di caricamento

def get_notification_config() -> Dict[str, Any]:
    """Ottiene la configurazione per le notifiche (SMS/Email) dalle variabili d'ambiente o usa i default"""
    # Carica il file .env se esiste
    load_env_file()

    return {
        # Metodo di notifica
        'notification_method': os.getenv('NOTIFICATION_METHOD', DEFAULT_NOTIFICATION_METHOD),

        # Configurazione SMS
        'provider': os.getenv('SMS_PROVIDER', DEFAULT_PROVIDER),
        'admin_phone': os.getenv('ADMIN_PHONE', ADMIN_PHONE),
        'twilio_account_sid': os.getenv('TWILIO_ACCOUNT_SID'),
        'twilio_auth_token': os.getenv('TWILIO_AUTH_TOKEN'),
        'twilio_from_number': os.getenv('TWILIO_FROM_NUMBER'),
        'smsit_api_key': os.getenv('SMSIT_API_KEY'),

        # Configurazione Email
        'smtp_server': os.getenv('SMTP_SERVER', EMAIL_CONFIG['smtp_server']),
        'smtp_port': int(os.getenv('SMTP_PORT', EMAIL_CONFIG['smtp_port'])),
        'email_address': os.getenv('EMAIL_ADDRESS', EMAIL_CONFIG['email_address']),
        'email_password': os.getenv('EMAIL_PASSWORD', EMAIL_CONFIG['email_password']),
        'admin_email': os.getenv('ADMIN_EMAIL', EMAIL_CONFIG['admin_email']),

        # Opzioni generali
        'disable_2fa': os.getenv('DISABLE_2FA', '').lower() in ('true', '1', 'yes', 'on'),
    }

# Manteniamo la funzione originale per compatibilità
def get_sms_config() -> Dict[str, Any]:
    """Mantiene compatibilità con il codice esistente"""
    return get_notification_config()
