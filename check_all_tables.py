#!/usr/bin/env python3
"""
Verifica tutte le tabelle nel database AGENTE
"""

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

def check_all_tables():
    """Verifica tutte le tabelle nel database"""
    print("VERIFICA TUTTE LE TABELLE DATABASE AGENTE")
    print("=" * 50)
    
    # Connessione database
    db_url = "postgresql://re77:271077@localhost:5432/AGENTE"
    engine = create_engine(db_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    try:
        with SessionLocal() as db:
            # Query per ottenere tutte le tabelle
            tables_query = text("""
                SELECT 
                    table_name,
                    (SELECT COUNT(*) FROM information_schema.columns 
                     WHERE table_name = t.table_name AND table_schema = 'public') as column_count
                FROM information_schema.tables t
                WHERE table_schema = 'public' 
                AND table_type = 'BASE TABLE'
                ORDER BY table_name
            """)
            
            result = db.execute(tables_query)
            tables = result.fetchall()
            
            print(f"Tabelle trovate: {len(tables)}")
            print("-" * 50)
            
            total_rows = 0
            
            for i, (table_name, column_count) in enumerate(tables, 1):
                try:
                    # Conta righe in ogni tabella
                    count_query = text(f'SELECT COUNT(*) FROM "{table_name}"')
                    row_count = db.execute(count_query).scalar()
                    total_rows += row_count
                    
                    print(f"{i:2d}. {table_name:<25} | Colonne: {column_count:2d} | Righe: {row_count:4d}")
                    
                except Exception as e:
                    print(f"{i:2d}. {table_name:<25} | ERRORE: {e}")
            
            print("-" * 50)
            print(f"TOTALE TABELLE: {len(tables)}")
            print(f"TOTALE RIGHE: {total_rows:,}")
            
            # Verifica tabelle nel backup attuale
            print("\nTABELLE NEL BACKUP ATTUALE:")
            print("-" * 30)
            
            backup_tables = [
                "AGENTE",
                "PORTI_GESTIONE", 
                "SYSTEM_CONFIG",
                "ARMATORE",
                "NAVI",
                "VIAGGIO",
                "ATLAS",
                "SOF_DOCUMENTS"
            ]
            
            table_names = [table[0] for table in tables]
            
            for table in backup_tables:
                if table in table_names:
                    print(f"✓ {table} - Inclusa")
                else:
                    print(f"✗ {table} - Non trovata")
            
            # Tabelle mancanti nel backup
            missing_tables = [table for table in table_names if table not in backup_tables]
            
            if missing_tables:
                print(f"\nTABELLE MANCANTI NEL BACKUP ({len(missing_tables)}):")
                print("-" * 40)
                
                for table in missing_tables:
                    # Conta righe tabella mancante
                    try:
                        count_query = text(f'SELECT COUNT(*) FROM "{table}"')
                        row_count = db.execute(count_query).scalar()
                        print(f"✗ {table:<25} | Righe: {row_count:4d}")
                    except Exception as e:
                        print(f"✗ {table:<25} | ERRORE: {e}")
            
            return table_names
            
    except Exception as e:
        print(f"ERRORE connessione database: {e}")
        return []

def generate_complete_backup_list():
    """Genera lista completa per backup"""
    print("\nLISTA COMPLETA PER BACKUP:")
    print("=" * 40)
    
    tables = check_all_tables()
    
    if tables:
        print("\nCODICE PER BACKUP COMPLETO:")
        print("-" * 30)
        print("# Backup tutte le tabelle esistenti")
        
        for table in tables:
            print(f'self._backup_table_if_exists_copy(f, "{table}")')
        
        print(f"\n# Totale: {len(tables)} tabelle")

def main():
    generate_complete_backup_list()

if __name__ == "__main__":
    main()
