# 📋 SOF ARCHIVIATI - Documentazione Completa

## 🎯 Panoramica

La funzionalità **SOF Archiviati** permette di visualizzare, gestire e rigenerare i SOF (Statement of Facts) che sono stati archiviati dal sistema. I SOF archiviati vengono salvati come file JSON nella cartella `Archivio` e possono essere importati e visualizzati attraverso un'interfaccia web dedicata.

## 🗂️ Struttura File Archivio

### Formato Nome File
```
{NOME_NAVE}_{DDMMYYYY}.json
```

**Esempi:**
- `CATANIA_06062025.json` → Nave CATANIA del 06/06/2025
- `PALERMO_15052025.json` → Nave PALERMO del 15/05/2025

### Struttura JSON
```json
{
  "metadata": {
    "archiviato_il": "2025-05-20T10:30:00",
    "archiviato_da": "<EMAIL>",
    "filename": "CATANIA_06062025.json",
    "viaggio_id": 123
  },
  "viaggio": {
    "id": 123,
    "codice_viaggio": "V20250606001",
    "data_arrivo": "2025-06-06T08:00:00",
    "data_partenza": "2025-06-07T18:00:00",
    "nome_nave": "CATANIA",
    "porto_gestione": "Catania",
    "prefisso_viaggio": "PV001",
    "agemar": "AG1234"
  },
  "import_data": [
    {
      "pol": "Catania",
      "pod": "Palermo", 
      "qt": 150,
      "type": "Container",
      "created_at": "2025-06-06T09:00:00"
    }
  ],
  "export_data": [
    {
      "pol": "Palermo",
      "pod": "Napoli",
      "qt": 200,
      "type": "Trailer", 
      "created_at": "2025-06-06T10:00:00"
    }
  ],
  "orari": [
    {
      "porto": "Catania",
      "arrivo": "2025-06-06T08:00:00",
      "partenza": "2025-06-06T12:00:00",
      "note": "Scalo principale"
    }
  ]
}
```

## 🌐 Interfaccia Web

### URL di Accesso
```
http://localhost:8002/operativo/sof/archiviati
```

### Funzionalità Principali

#### 📊 Dashboard Statistiche
- **File Archiviati**: Numero totale di file JSON
- **Navi Diverse**: Conteggio navi uniche
- **Record Import**: Somma di tutti i record import
- **Record Export**: Somma di tutti i record export

#### 📅 Filtro per Data
- **Range Automatico**: Mostra prima e ultima data disponibile
- **Filtro Personalizzato**: Seleziona intervallo date specifico
- **Reset Filtri**: Ripristina visualizzazione completa

#### 📋 Lista File Archiviati
Ogni card mostra:
- **Nome Nave** e **Data Viaggio**
- **Codice Viaggio** e **Porto Gestione**
- **Contatori Import/Export**
- **Informazioni File** (nome, dimensione, archiviato da)
- **Azioni**: Visualizza Dettagli, Scarica SOF

## 🔧 API Endpoints

### 1. Pagina Principale
```http
GET /operativo/sof/archiviati
```
**Descrizione**: Carica la pagina principale con lista file archiviati
**Accesso**: Reparto OPERATIVO

### 2. Dettagli SOF Archiviato
```http
GET /api/sof/archiviati/{filename}/dettagli
```
**Descrizione**: Ottiene i dettagli completi di un file archiviato
**Parametri**: 
- `filename`: Nome del file JSON (es. "CATANIA_06062025.json")
**Risposta**:
```json
{
  "success": true,
  "data": { /* Contenuto completo del file JSON */ },
  "message": "Dettagli caricati con successo"
}
```

### 3. Download SOF Archiviato
```http
GET /api/sof/archiviati/{filename}/download
```
**Descrizione**: Rigenera e scarica il SOF da file archiviato
**Parametri**:
- `filename`: Nome del file JSON
**Risposta**: File DOCX per il download

## 📱 Funzionalità JavaScript

### Filtri e Ricerca
```javascript
// Filtra per data
function filtraPerData()

// Reset filtri
function resetFiltri()

// Calcola statistiche
function calcolaStatistiche()
```

### Visualizzazione Dettagli
```javascript
// Apre modal con dettagli completi
function visualizzaDettagli(filename)

// Genera HTML per dettagli
function generaHTMLDettagli(data)

// Genera tabelle per orari, import, export
function generaTabellaOrari(orari)
function generaTabellaImport(importData)
function generaTabellaExport(exportData)
```

### Download SOF
```javascript
// Scarica SOF rigenerato
function scaricaSOF(filename)
```

## 🎨 Interfaccia Utente

### Tema Marittimo
La pagina supporta il tema marittimo con:
- **Colori**: Blu navy (#1e3a8a) e oro (#ffd700)
- **Card Animate**: Effetti hover con elevazione
- **Pulsanti Colorati**: Gradienti per azioni diverse
- **Statistiche Visive**: Card colorate per metriche

### Responsive Design
- **Desktop**: Layout a 3 colonne per le card
- **Tablet**: Layout a 2 colonne
- **Mobile**: Layout a 1 colonna
- **Touch-Friendly**: Pulsanti ottimizzati per touch

## 🔐 Sicurezza e Permessi

### Controllo Accessi
- **Reparto OPERATIVO**: Accesso completo
- **Altri Reparti**: Accesso negato
- **Autenticazione**: Richiesta per tutte le operazioni

### Validazione File
- **Estensione**: Solo file .json
- **Formato Nome**: Validazione pattern NOME_DDMMYYYY.json
- **Contenuto JSON**: Validazione struttura dati
- **Sicurezza Path**: Prevenzione path traversal

## 📈 Statistiche e Metriche

### Calcoli Automatici
- **Range Date**: Min/max automatico dai file
- **Conteggi**: Aggiornamento dinamico con filtri
- **Aggregazioni**: Somme per import/export
- **Navi Uniche**: Set per eliminare duplicati

### Performance
- **Caricamento Lazy**: Solo metadati iniziali
- **Dettagli On-Demand**: Caricamento al click
- **Cache Browser**: Ottimizzazione risorse statiche

## 🛠️ Manutenzione

### Gestione File
- **Cartella Archivio**: `./Archivio/` nella root del progetto
- **Backup**: Raccomandato backup periodico
- **Pulizia**: Rimozione file obsoleti se necessario

### Monitoraggio
- **Log**: Tutte le operazioni sono loggate
- **Errori**: Gestione errori con messaggi utente
- **Performance**: Monitoraggio tempi di risposta

## 🚀 Utilizzo Pratico

### Workflow Tipico
1. **Archiviazione**: SOF viene archiviato da pagina "SOF Realizzati"
2. **File JSON**: Creato automaticamente in cartella Archivio
3. **Visualizzazione**: File appare in lista SOF Archiviati
4. **Consultazione**: Utente può vedere dettagli completi
5. **Rigenerazione**: Possibilità di scaricare SOF aggiornato

### Casi d'Uso
- **Audit**: Verifica SOF storici
- **Ristampa**: Rigenerazione documenti persi
- **Analisi**: Studio dati storici viaggi
- **Backup**: Recupero da archivio

## 📋 Checklist Implementazione

### ✅ Completato
- [x] Route `/operativo/sof/archiviati`
- [x] Template HTML responsive
- [x] JavaScript per interazioni
- [x] API dettagli e download
- [x] Supporto tema marittimo
- [x] Filtri per data
- [x] Statistiche aggregate
- [x] Modal dettagli completo
- [x] Rigenerazione SOF
- [x] Validazione sicurezza
- [x] Gestione errori
- [x] Documentazione completa

### 🎯 Funzionalità Chiave
- **Import da JSON**: ✅ Lettura file archivio
- **Filtro Date**: ✅ Range automatico + manuale
- **Visualizzazione**: ✅ Card responsive con dettagli
- **Dettagli Completi**: ✅ Modal con tab (orari, import, export)
- **Download SOF**: ✅ Rigenerazione DOCX da archivio
- **Statistiche**: ✅ Conteggi e aggregazioni dinamiche

## 🎉 Risultato Finale

La funzionalità **SOF Archiviati** è completamente implementata e operativa, offrendo:

- 📂 **Gestione Completa** dei file archiviati
- 🔍 **Ricerca e Filtri** avanzati
- 📊 **Statistiche** in tempo reale  
- 👁️ **Visualizzazione Dettagliata** di tutti i dati
- 📥 **Rigenerazione SOF** da archivio
- 🎨 **Interfaccia Moderna** con tema marittimo
- 📱 **Design Responsive** per tutti i dispositivi
- 🔐 **Sicurezza** e controllo accessi

**La pagina è accessibile su: http://localhost:8002/operativo/sof/archiviati**
