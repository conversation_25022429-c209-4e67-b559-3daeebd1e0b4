#!/usr/bin/env python3
"""
Script per risolvere il problema del vincolo di chiave esterna sui porti
"""

import sys
import os
from datetime import datetime

# Aggiungi il percorso corrente al path
sys.path.append('.')

def check_atlas_table():
    """Verifica il contenuto della tabella ATLAS"""
    try:
        from database import get_db
        from sqlalchemy import text
        
        db = next(get_db())
        
        print("🗺️ VERIFICA TABELLA ATLAS")
        print("=" * 50)
        
        # Verifica struttura tabella ATLAS
        result = db.execute(text("""
            SELECT "ID_COD", "PORTI", "STATO" 
            FROM "ATLAS" 
            ORDER BY "ID_COD"
            LIMIT 20
        """)).fetchall()
        
        print(f"📋 Primi 20 record ATLAS:")
        for row in result:
            print(f"   {row[0]}: {row[1]} ({row[2]})")
        
        # Cerca specificamente CATANIA
        catania_result = db.execute(text("""
            SELECT "ID_COD", "PORTI", "STATO" 
            FROM "ATLAS" 
            WHERE UPPER("PORTI") LIKE '%CATANIA%' OR UPPER("ID_COD") LIKE '%CAT%'
        """)).fetchall()
        
        print(f"\n🔍 Ricerca CATANIA:")
        if catania_result:
            for row in catania_result:
                print(f"   ✅ {row[0]}: {row[1]} ({row[2]})")
        else:
            print("   ❌ Nessun porto CATANIA trovato")
        
        # Cerca SALERNO
        salerno_result = db.execute(text("""
            SELECT "ID_COD", "PORTI", "STATO" 
            FROM "ATLAS" 
            WHERE UPPER("PORTI") LIKE '%SALERNO%' OR UPPER("ID_COD") LIKE '%SAL%'
        """)).fetchall()
        
        print(f"\n🔍 Ricerca SALERNO:")
        if salerno_result:
            for row in salerno_result:
                print(f"   ✅ {row[0]}: {row[1]} ({row[2]})")
        else:
            print("   ❌ Nessun porto SALERNO trovato")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Errore verifica ATLAS: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_viaggio_43_current_values():
    """Verifica i valori attuali del viaggio 43"""
    try:
        from database import get_db
        from sqlalchemy import text
        
        db = next(get_db())
        
        print("\n📋 VALORI ATTUALI VIAGGIO 43")
        print("=" * 50)
        
        result = db.execute(text("""
            SELECT v.id, v.viaggio, v.porto_arrivo, v.porto_destinazione,
                   a1."PORTI" as porto_arrivo_nome,
                   a2."PORTI" as porto_destinazione_nome
            FROM "VIAGGIO" v
            LEFT JOIN "ATLAS" a1 ON v.porto_arrivo = a1."ID_COD"
            LEFT JOIN "ATLAS" a2 ON v.porto_destinazione = a2."ID_COD"
            WHERE v.id = 43
        """)).fetchone()
        
        if result:
            print(f"ID: {result[0]}")
            print(f"Nome viaggio: {result[1]}")
            print(f"Porto arrivo (codice): {result[2]}")
            print(f"Porto arrivo (nome): {result[4]}")
            print(f"Porto destinazione (codice): {result[3]}")
            print(f"Porto destinazione (nome): {result[5]}")
        else:
            print("❌ Viaggio 43 non trovato")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Errore verifica viaggio 43: {e}")
        return False

def suggest_fix():
    """Suggerisce come risolvere il problema"""
    print("\n🛠️ SOLUZIONI CONSIGLIATE")
    print("=" * 50)
    
    print("1. 🔧 MODIFICA ENDPOINT UPDATE")
    print("   Modificare l'endpoint per:")
    print("   - Accettare nomi porti invece di codici")
    print("   - Convertire automaticamente nomi in codici ATLAS")
    print("   - Validare che i porti esistano")
    
    print("\n2. 📋 AGGIUNTA PORTI MANCANTI")
    print("   Se CATANIA/SALERNO non esistono in ATLAS:")
    print("   - Aggiungere i record mancanti")
    print("   - Usare codici standard (es. CAT, SAL)")
    
    print("\n3. 🔄 RIMOZIONE VINCOLO")
    print("   Rimuovere il vincolo di chiave esterna se non necessario")
    print("   (sconsigliato per integrità dati)")
    
    print("\n4. 🎯 FRONTEND FIX")
    print("   Modificare il frontend per inviare codici invece di nomi")

def implement_fix():
    """Implementa la soluzione consigliata"""
    try:
        from database import get_db
        from sqlalchemy import text
        
        db = next(get_db())
        
        print("\n🔧 IMPLEMENTAZIONE FIX")
        print("=" * 50)
        
        # Verifica se CATANIA esiste
        catania_check = db.execute(text("""
            SELECT "ID_COD" FROM "ATLAS" 
            WHERE UPPER("PORTI") LIKE '%CATANIA%'
        """)).fetchone()
        
        if not catania_check:
            print("➕ Aggiunta CATANIA ad ATLAS...")
            try:
                db.execute(text("""
                    INSERT INTO "ATLAS" ("ID_COD", "PORTI", "STATO")
                    VALUES ('CAT', 'CATANIA', 'ITALIA')
                """))
                print("   ✅ CATANIA aggiunta con codice CAT")
            except Exception as e:
                print(f"   ❌ Errore aggiunta CATANIA: {e}")
        else:
            print(f"   ✅ CATANIA già presente: {catania_check[0]}")
        
        # Verifica se SALERNO esiste
        salerno_check = db.execute(text("""
            SELECT "ID_COD" FROM "ATLAS" 
            WHERE UPPER("PORTI") LIKE '%SALERNO%'
        """)).fetchone()
        
        if not salerno_check:
            print("➕ Aggiunta SALERNO ad ATLAS...")
            try:
                db.execute(text("""
                    INSERT INTO "ATLAS" ("ID_COD", "PORTI", "STATO")
                    VALUES ('SAL', 'SALERNO', 'ITALIA')
                """))
                print("   ✅ SALERNO aggiunta con codice SAL")
            except Exception as e:
                print(f"   ❌ Errore aggiunta SALERNO: {e}")
        else:
            print(f"   ✅ SALERNO già presente: {salerno_check[0]}")
        
        db.commit()
        print("\n✅ Fix implementato con successo")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Errore implementazione fix: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fix():
    """Testa che il fix funzioni"""
    try:
        from main import update_viaggio
        from database import get_db
        
        print("\n🧪 TEST FIX")
        print("=" * 50)
        
        db = next(get_db())
        
        # Trova i codici corretti per CATANIA e SALERNO
        catania_code = db.execute(text("""
            SELECT "ID_COD" FROM "ATLAS" 
            WHERE UPPER("PORTI") LIKE '%CATANIA%'
            LIMIT 1
        """)).fetchone()
        
        salerno_code = db.execute(text("""
            SELECT "ID_COD" FROM "ATLAS" 
            WHERE UPPER("PORTI") LIKE '%SALERNO%'
            LIMIT 1
        """)).fetchone()
        
        if catania_code and salerno_code:
            print(f"📋 Codici trovati:")
            print(f"   CATANIA: {catania_code[0]}")
            print(f"   SALERNO: {salerno_code[0]}")
            
            # Test con codici corretti
            try:
                response = update_viaggio(
                    viaggio_id=43,
                    porto_gestione_id=1,
                    nave_id=1,
                    viaggio="ECT3225A_FIXED",
                    data_arrivo="2025-06-12",
                    data_partenza="2025-06-13",
                    porto_arrivo=catania_code[0],
                    porto_destinazione=salerno_code[0],
                    db=db
                )
                
                print("✅ Test update riuscito!")
                print(f"   Risposta: {type(response)}")
                
            except Exception as e:
                print(f"❌ Test update fallito: {e}")
        else:
            print("❌ Codici porti non trovati")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Errore test fix: {e}")
        return False

def main():
    """Esegue la diagnosi e il fix completo"""
    print("🔧 FIX PROBLEMA VINCOLO CHIAVE ESTERNA PORTI")
    print("=" * 60)
    print(f"🕐 Timestamp: {datetime.now()}")
    print()
    
    steps = [
        ("Verifica tabella ATLAS", check_atlas_table),
        ("Verifica viaggio 43", check_viaggio_43_current_values),
        ("Implementa fix", implement_fix),
        ("Test fix", test_fix),
    ]
    
    results = []
    
    for step_name, step_func in steps:
        print(f"\n🔧 {step_name}")
        print("-" * 30)
        try:
            result = step_func()
            results.append((step_name, result))
            if not result:
                print("⚠️ Step fallito, continuo comunque...")
        except Exception as e:
            print(f"❌ ERRORE: {e}")
            results.append((step_name, False))
    
    # Mostra sempre i suggerimenti
    suggest_fix()
    
    print(f"\n📊 RIEPILOGO")
    print("=" * 50)
    for step_name, result in results:
        status = "✅ OK" if result else "❌ ERRORE"
        print(f"{step_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\nTotale: {passed}/{total} step completati")

if __name__ == "__main__":
    main()
