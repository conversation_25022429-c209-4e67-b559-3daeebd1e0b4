#!/usr/bin/env python3
"""
Script per azzerare tutti i valori Agemar_GioiaTauro nella tabella NAVI
"""

from sqlalchemy import text
from database import SessionLocal
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_current_values():
    """Controlla i valori attuali di Agemar_GioiaTauro"""
    db = SessionLocal()
    
    try:
        # Conta le navi con Agemar_GioiaTauro impostato
        result = db.execute(text('''
            SELECT COUNT(*) 
            FROM "NAVI" 
            WHERE "Agemar_GioiaTauro" IS NOT NULL AND "Agemar_GioiaTauro" != 0
        '''))
        
        count_with_values = result.scalar()
        
        # Conta le navi totali
        result = db.execute(text('SELECT COUNT(*) FROM "NAVI"'))
        total_navi = result.scalar()
        
        # Mostra alcuni esempi di navi con valori
        result = db.execute(text('''
            SELECT "Nave", "Agemar_GioiaTauro" 
            FROM "NAVI" 
            WHERE "Agemar_GioiaTauro" IS NOT NULL AND "Agemar_GioiaTauro" != 0
            ORDER BY "Agemar_GioiaTauro" DESC
            LIMIT 10
        '''))
        
        examples = result.fetchall()
        
        logger.info(f"📊 STATO ATTUALE AGEMAR GIOIA TAURO:")
        logger.info(f"   • Navi con valori: {count_with_values}")
        logger.info(f"   • Navi totali: {total_navi}")
        logger.info(f"   • Percentuale con valori: {(count_with_values/total_navi*100):.1f}%")
        
        if examples:
            logger.info(f"\n📋 ESEMPI DI NAVI CON VALORI ATTUALI:")
            for nave in examples:
                logger.info(f"   • {nave[0]}: {nave[1]:.2f} €")
        
        return count_with_values, total_navi
        
    except Exception as e:
        logger.error(f"Errore controllo valori attuali: {e}")
        return 0, 0
        
    finally:
        db.close()

def reset_agemar_values():
    """Azzera tutti i valori Agemar_GioiaTauro"""
    db = SessionLocal()
    
    try:
        logger.info("🔄 Inizio azzeramento valori Agemar_GioiaTauro...")
        
        # Azzera tutti i valori impostandoli a NULL
        result = db.execute(text('''
            UPDATE "NAVI" 
            SET "Agemar_GioiaTauro" = NULL
            WHERE "Agemar_GioiaTauro" IS NOT NULL
        '''))
        
        rows_affected = result.rowcount
        
        # Commit delle modifiche
        db.commit()
        
        logger.info(f"✅ Azzeramento completato!")
        logger.info(f"📊 Righe modificate: {rows_affected}")
        
        return rows_affected
        
    except Exception as e:
        db.rollback()
        logger.error(f"❌ Errore durante l'azzeramento: {e}")
        return 0
        
    finally:
        db.close()

def verify_reset():
    """Verifica che l'azzeramento sia avvenuto correttamente"""
    db = SessionLocal()
    
    try:
        # Conta le navi con Agemar_GioiaTauro ancora impostato
        result = db.execute(text('''
            SELECT COUNT(*) 
            FROM "NAVI" 
            WHERE "Agemar_GioiaTauro" IS NOT NULL AND "Agemar_GioiaTauro" != 0
        '''))
        
        count_with_values = result.scalar()
        
        # Conta le navi con valori NULL
        result = db.execute(text('''
            SELECT COUNT(*) 
            FROM "NAVI" 
            WHERE "Agemar_GioiaTauro" IS NULL
        '''))
        
        count_null = result.scalar()
        
        # Conta le navi totali
        result = db.execute(text('SELECT COUNT(*) FROM "NAVI"'))
        total_navi = result.scalar()
        
        logger.info(f"\n🔍 VERIFICA POST-AZZERAMENTO:")
        logger.info(f"   • Navi con valori rimasti: {count_with_values}")
        logger.info(f"   • Navi con valori NULL: {count_null}")
        logger.info(f"   • Navi totali: {total_navi}")
        
        if count_with_values == 0:
            logger.info(f"✅ AZZERAMENTO RIUSCITO! Tutti i valori sono stati rimossi.")
        else:
            logger.warning(f"⚠️ ATTENZIONE: {count_with_values} navi hanno ancora valori impostati!")
            
            # Mostra le navi che hanno ancora valori
            result = db.execute(text('''
                SELECT "Nave", "Agemar_GioiaTauro" 
                FROM "NAVI" 
                WHERE "Agemar_GioiaTauro" IS NOT NULL AND "Agemar_GioiaTauro" != 0
                LIMIT 5
            '''))
            
            remaining = result.fetchall()
            
            if remaining:
                logger.info(f"   Esempi di navi con valori rimasti:")
                for nave in remaining:
                    logger.info(f"     • {nave[0]}: {nave[1]}")
        
        return count_with_values == 0
        
    except Exception as e:
        logger.error(f"Errore verifica azzeramento: {e}")
        return False
        
    finally:
        db.close()

def main():
    """Funzione principale"""
    print("🗑️ AZZERAMENTO AGEMAR GIOIA TAURO")
    print("=" * 50)
    
    # Controlla i valori attuali
    count_with_values, total_navi = check_current_values()
    
    if count_with_values == 0:
        print("\n✅ Non ci sono valori da azzerare. Tutti i campi Agemar_GioiaTauro sono già vuoti.")
        return
    
    # Chiedi conferma prima di procedere
    print(f"\n⚠️ ATTENZIONE: Stai per azzerare {count_with_values} valori Agemar_GioiaTauro!")
    print("   Questa operazione rimuoverà TUTTI i dati dal campo Agemar_GioiaTauro.")
    print("   L'operazione NON è reversibile senza un backup del database.")
    
    response = input(f"\n❓ Sei sicuro di voler procedere con l'azzeramento? (CONFERMA/N): ")
    
    if response.upper() != 'CONFERMA':
        print("❌ Operazione annullata dall'utente")
        return
    
    # Seconda conferma per sicurezza
    response2 = input(f"\n❓ ULTIMA CONFERMA: Azzerare {count_with_values} valori Agemar_GioiaTauro? (SI/N): ")
    
    if response2.upper() != 'SI':
        print("❌ Operazione annullata dall'utente")
        return
    
    # Esegui l'azzeramento
    rows_affected = reset_agemar_values()
    
    if rows_affected > 0:
        # Verifica l'azzeramento
        success = verify_reset()
        
        if success:
            print(f"\n🎉 AZZERAMENTO COMPLETATO CON SUCCESSO!")
            print(f"✅ {rows_affected} valori Agemar_GioiaTauro sono stati azzerati")
            print(f"📊 Tutti i campi Agemar_GioiaTauro sono ora vuoti (NULL)")
        else:
            print(f"\n⚠️ AZZERAMENTO PARZIALE")
            print(f"📊 {rows_affected} righe sono state modificate, ma potrebbero rimanere alcuni valori")
    else:
        print(f"\n❌ AZZERAMENTO FALLITO")
        print(f"Nessuna riga è stata modificata")

if __name__ == "__main__":
    main()
