<!DOCTYPE html>
<html>
<head>
    <title>Test JavaScript Syntax</title>
</head>
<body>
    <h1>Test JavaScript Syntax</h1>
    <div id="result"></div>
    
    <script>
        console.log('Testing JavaScript syntax...');
        
        // Test se il file dashboard.js può essere caricato senza errori
        fetch('/static/js/dashboard.js')
            .then(response => response.text())
            .then(jsCode => {
                console.log('JavaScript file loaded successfully');
                
                // Prova a valutare il codice per vedere se ci sono errori di sintassi
                try {
                    // Non eseguiamo il codice, solo lo parseamo
                    new Function(jsCode);
                    console.log('✅ JavaScript syntax is valid!');
                    document.getElementById('result').innerHTML = '<p style="color: green;">✅ JavaScript syntax is valid!</p>';
                } catch (error) {
                    console.error('❌ JavaScript syntax error:', error);
                    document.getElementById('result').innerHTML = '<p style="color: red;">❌ JavaScript syntax error: ' + error.message + '</p>';
                }
            })
            .catch(error => {
                console.error('Failed to load JavaScript file:', error);
                document.getElementById('result').innerHTML = '<p style="color: red;">Failed to load JavaScript file</p>';
            });
    </script>
</body>
</html>
