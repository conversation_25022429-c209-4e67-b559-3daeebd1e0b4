<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Modifica Viaggio</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>🧪 Test Modifica Viaggio</h1>
        <p>Questo file testa la funzionalità di aggiornamento dinamico delle righe dopo la modifica di un viaggio.</p>
        
        <div class="card">
            <div class="card-header">
                <h5>Test API</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary" onclick="testAPI()">
                    <i class="fas fa-play"></i> Test API /api/sof/da-realizzare/search
                </button>
                <button class="btn btn-success ms-2" onclick="testAggiornaRiga()">
                    <i class="fas fa-sync"></i> Test Aggiorna Riga (ID: 61)
                </button>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5>Risultati Test</h5>
            </div>
            <div class="card-body">
                <pre id="risultati" class="bg-light p-3" style="max-height: 400px; overflow-y: auto;"></pre>
            </div>
        </div>

        <!-- Tabella di test simulata -->
        <div class="card mt-4">
            <div class="card-header">
                <h5>Tabella Test Simulata</h5>
            </div>
            <div class="card-body">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Codice</th>
                            <th>Nave</th>
                            <th>Porto Gestione</th>
                            <th>Porto Arrivo</th>
                            <th>Porto Destinazione</th>
                            <th>Data Arrivo</th>
                            <th>Data Partenza</th>
                            <th>Azioni</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr data-viaggio-id="61">
                            <td>
                                <span class="badge bg-secondary">TEST001</span>
                            </td>
                            <td class="cell-nave">
                                <strong>NAVE TEST</strong>
                            </td>
                            <td>
                                <span class="badge bg-primary porto-gestione-badge">
                                    <i class="fas fa-anchor me-1"></i>Porto Test
                                </span>
                            </td>
                            <td class="cell-porto-arrivo">
                                <span class="badge bg-info text-dark">Porto Arrivo Test</span>
                            </td>
                            <td class="cell-porto-destinazione">
                                <span class="badge bg-warning text-dark">Porto Destinazione Test</span>
                            </td>
                            <td>
                                <span class="text-success fw-bold">01/01/2024</span>
                            </td>
                            <td>
                                <span class="text-warning fw-bold">02/01/2024</span>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-warning" onclick="testModifica()">
                                    <i class="fas fa-edit"></i> Test Modifica
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function log(message) {
            const risultati = document.getElementById('risultati');
            const timestamp = new Date().toLocaleTimeString();
            risultati.textContent += `[${timestamp}] ${message}\n`;
            risultati.scrollTop = risultati.scrollHeight;
            console.log(message);
        }

        async function testAPI() {
            log('🚀 Inizio test API /api/sof/da-realizzare/search');
            
            try {
                const response = await fetch('/api/sof/da-realizzare/search');
                const data = await response.json();
                
                log(`✅ API Response: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success) {
                    log(`📊 Trovati ${data.viaggi.length} viaggi`);
                    if (data.viaggi.length > 0) {
                        log(`🔍 Primo viaggio: ${JSON.stringify(data.viaggi[0], null, 2)}`);
                    }
                } else {
                    log(`❌ Errore API: ${data.message}`);
                }
            } catch (error) {
                log(`💥 Errore nella chiamata API: ${error.message}`);
            }
        }

        async function testAggiornaRiga() {
            log('🔄 Inizio test aggiornamento riga viaggio ID: 61');
            
            // Simula la funzione aggiornaRigaViaggio
            try {
                const viaggioId = 61;
                const riga = document.querySelector(`tr[data-viaggio-id="${viaggioId}"]`);
                
                if (!riga) {
                    log(`❌ Riga viaggio ${viaggioId} non trovata`);
                    return;
                }
                
                log('🎯 Riga trovata, inizio aggiornamento...');
                
                // Mostra indicatore di caricamento
                riga.style.opacity = '0.6';
                riga.style.transition = 'opacity 0.3s';
                
                // Chiama l'API
                const response = await fetch('/api/sof/da-realizzare/search');
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.message || 'Errore nel recupero dati');
                }
                
                // Trova il viaggio specifico
                const viaggioAggiornato = data.viaggi.find(v => v.id === viaggioId);
                if (!viaggioAggiornato) {
                    log(`❌ Viaggio ${viaggioId} non trovato nei dati aggiornati`);
                    return;
                }
                
                log(`📋 Dati viaggio trovati: ${JSON.stringify(viaggioAggiornato, null, 2)}`);
                
                // Aggiorna le celle (versione semplificata per test)
                const badgeViaggio = riga.querySelector('td:nth-child(1) .badge');
                if (badgeViaggio && viaggioAggiornato.viaggio) {
                    badgeViaggio.textContent = viaggioAggiornato.viaggio;
                    log(`✅ Codice viaggio aggiornato: '${viaggioAggiornato.viaggio}'`);
                }
                
                const cellaNave = riga.querySelector('.cell-nave strong');
                if (cellaNave && viaggioAggiornato.nome_nave) {
                    cellaNave.textContent = viaggioAggiornato.nome_nave;
                    log(`✅ Nome nave aggiornato: '${viaggioAggiornato.nome_nave}'`);
                }
                
                // Ripristina l'opacità con animazione
                setTimeout(() => {
                    riga.style.opacity = '1';
                    riga.style.backgroundColor = '#d4edda';
                    
                    setTimeout(() => {
                        riga.style.backgroundColor = '';
                        riga.style.transition = '';
                    }, 2000);
                }, 100);
                
                log(`✅ Test aggiornamento riga completato con successo`);
                
            } catch (error) {
                log(`💥 Errore nel test aggiornamento riga: ${error.message}`);
                
                // Ripristina l'opacità in caso di errore
                const riga = document.querySelector(`tr[data-viaggio-id="61"]`);
                if (riga) {
                    riga.style.opacity = '1';
                    riga.style.backgroundColor = '#f8d7da';
                    
                    setTimeout(() => {
                        riga.style.backgroundColor = '';
                    }, 3000);
                }
            }
        }

        function testModifica() {
            log('🔧 Simulazione modifica viaggio...');
            log('📝 In un sistema reale, qui si aprirebbe il modale di modifica');
            log('💾 Dopo il salvataggio, verrebbe chiamata la funzione aggiornaRigaViaggio()');
            
            // Simula il salvataggio e l'aggiornamento
            setTimeout(() => {
                testAggiornaRiga();
            }, 1000);
        }

        // Log iniziale
        log('🧪 Test Modifica Viaggio inizializzato');
        log('🌐 Server dovrebbe essere in esecuzione su http://localhost:8002');
    </script>
</body>
</html>
