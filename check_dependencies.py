#!/usr/bin/env python3
"""
Script di verifica dipendenze per SNIP
Controlla che tutte le librerie necessarie siano installate correttamente
"""

import sys
import importlib
import subprocess

def print_header():
    """Stampa l'header del controllo"""
    print("=" * 60)
    print("🔍 SNIP - Verifica Dipendenze")
    print("📦 Controllo Librerie Python")
    print("=" * 60)
    print()

def check_python_version():
    """Verifica la versione di Python"""
    print("🐍 Versione Python:")
    version = sys.version_info
    print(f"   Python {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("   ❌ Versione non supportata (richiesto Python 3.8+)")
        return False
    else:
        print("   ✅ Versione supportata")
        return True

def check_library(name, import_name=None, version_attr=None):
    """Controlla se una libreria è installata"""
    if import_name is None:
        import_name = name
    
    try:
        module = importlib.import_module(import_name)
        
        # Prova a ottenere la versione
        version = "N/A"
        if version_attr:
            version = getattr(module, version_attr, "N/A")
        elif hasattr(module, '__version__'):
            version = module.__version__
        
        print(f"   ✅ {name} ({version})")
        return True
        
    except ImportError as e:
        print(f"   ❌ {name} - NON INSTALLATO")
        print(f"      Errore: {e}")
        return False

def check_core_dependencies():
    """Controlla le dipendenze principali"""
    print("🔧 Dipendenze Core:")
    
    dependencies = [
        ("FastAPI", "fastapi"),
        ("Uvicorn", "uvicorn"),
        ("SQLAlchemy", "sqlalchemy"),
        ("psycopg2", "psycopg2"),
        ("Passlib", "passlib"),
        ("Jinja2", "jinja2"),
        ("python-multipart", "multipart"),
    ]
    
    all_ok = True
    for name, import_name in dependencies:
        if not check_library(name, import_name):
            all_ok = False
    
    return all_ok

def check_document_dependencies():
    """Controlla le dipendenze per documenti"""
    print("\n📄 Dipendenze Documenti:")

    dependencies = [
        ("python-docx", "docx"),
        ("lxml", "lxml"),
        ("reportlab", "reportlab"),
        ("openpyxl", "openpyxl"),
        ("pandas", "pandas"),
    ]

    all_ok = True
    for name, import_name in dependencies:
        if not check_library(name, import_name):
            all_ok = False

    return all_ok

def check_utility_dependencies():
    """Controlla le dipendenze utility"""
    print("\n🛠️ Dipendenze Utility:")
    
    dependencies = [
        ("pydantic", "pydantic"),
        ("python-dateutil", "dateutil"),
        ("typing-extensions", "typing_extensions"),
    ]
    
    all_ok = True
    for name, import_name in dependencies:
        if not check_library(name, import_name):
            all_ok = False
    
    return all_ok

def check_critical_imports():
    """Controlla import critici specifici"""
    print("\n🎯 Import Critici:")
    
    critical_tests = [
        ("lxml.etree", "from lxml import etree"),
        ("docx.Document", "from docx import Document"),
        ("fastapi.FastAPI", "from fastapi import FastAPI"),
        ("sqlalchemy.create_engine", "from sqlalchemy import create_engine"),
    ]
    
    all_ok = True
    for name, import_statement in critical_tests:
        try:
            exec(import_statement)
            print(f"   ✅ {name}")
        except Exception as e:
            print(f"   ❌ {name} - ERRORE: {e}")
            all_ok = False
    
    return all_ok

def check_database_driver():
    """Controlla il driver del database"""
    print("\n🗄️ Driver Database:")
    
    try:
        import psycopg2
        print(f"   ✅ psycopg2 ({psycopg2.__version__})")
        
        # Test connessione (senza connettersi realmente)
        try:
            # Prova a creare una stringa di connessione
            conn_string = "postgresql://test:test@localhost:5432/test"
            print(f"   ✅ Formato connessione PostgreSQL supportato")
            return True
        except Exception as e:
            print(f"   ⚠️ Problema formato connessione: {e}")
            return True  # Non è critico
            
    except ImportError:
        print("   ❌ psycopg2 non installato")
        return False

def suggest_fixes(failed_checks):
    """Suggerisce come risolvere i problemi"""
    if not failed_checks:
        return
    
    print("\n🔧 SUGGERIMENTI PER RISOLVERE I PROBLEMI:")
    print("=" * 60)
    
    print("\n1️⃣ Reinstalla tutte le dipendenze:")
    print("   pip install -r requirements.txt --force-reinstall")
    
    print("\n2️⃣ Aggiorna pip:")
    print("   python -m pip install --upgrade pip")
    
    print("\n3️⃣ Per problemi specifici:")
    print("   # lxml/python-docx:")
    print("   pip install --force-reinstall lxml python-docx")
    print("   # psycopg2:")
    print("   pip install --force-reinstall psycopg2-binary")
    print("   # FastAPI:")
    print("   pip install --force-reinstall fastapi uvicorn")
    
    print("\n4️⃣ Se i problemi persistono:")
    print("   # Ricrea virtual environment:")
    print("   rm -rf venv  # o rmdir /s venv su Windows")
    print("   python -m venv venv")
    print("   # Attiva venv e reinstalla")

def main():
    """Funzione principale"""
    print_header()
    
    failed_checks = []
    
    # Controlli
    if not check_python_version():
        failed_checks.append("Python Version")
    
    if not check_core_dependencies():
        failed_checks.append("Core Dependencies")
    
    if not check_document_dependencies():
        failed_checks.append("Document Dependencies")
    
    if not check_utility_dependencies():
        failed_checks.append("Utility Dependencies")
    
    if not check_critical_imports():
        failed_checks.append("Critical Imports")
    
    if not check_database_driver():
        failed_checks.append("Database Driver")
    
    # Risultato finale
    print("\n" + "=" * 60)
    if not failed_checks:
        print("🎉 TUTTE LE DIPENDENZE SONO INSTALLATE CORRETTAMENTE!")
        print("✅ L'applicazione SNIP dovrebbe funzionare senza problemi")
    else:
        print("❌ ALCUNI CONTROLLI SONO FALLITI:")
        for check in failed_checks:
            print(f"   • {check}")
        
        suggest_fixes(failed_checks)
    
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ Controllo interrotto dall'utente")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Errore durante il controllo: {e}")
        sys.exit(1)
