#!/usr/bin/env python3
"""
Script semplice per aggiornare tutti gli utenti con tema 'light' a 'maritime'
"""

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import os

def main():
    database_url = os.getenv('DATABASE_URL', 'postgresql://re77:271077@localhost:5432/AGENTE')
    engine = create_engine(database_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    with SessionLocal() as db:
        print('🔄 Aggiornamento utenti da light a maritime...')
        
        # Prima verifica quanti utenti hanno tema light
        result = db.execute(text('SELECT COUNT(*) FROM "AGENTE" WHERE tema_preferito = \'light\''))
        count_before = result.fetchone()[0]
        print(f'📊 Utenti con tema light: {count_before}')
        
        if count_before > 0:
            # Aggiorna i temi
            result = db.execute(text('UPDATE "AGENTE" SET tema_preferito = \'maritime\' WHERE tema_preferito = \'light\''))
            rows_affected = result.rowcount if hasattr(result, 'rowcount') else 0
            db.commit()
            print(f'✅ Aggiornati {rows_affected} utenti')
        else:
            print('✅ Nessun utente da aggiornare')
        
        # Verifica finale
        result = db.execute(text('SELECT tema_preferito, COUNT(*) as count FROM "AGENTE" GROUP BY tema_preferito ORDER BY tema_preferito'))
        theme_stats = result.fetchall()
        print('📊 Statistiche finali:')
        for theme, count in theme_stats:
            print(f'   - {theme}: {count} utenti')

if __name__ == "__main__":
    main()
