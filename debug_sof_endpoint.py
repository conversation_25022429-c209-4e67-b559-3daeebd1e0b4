#!/usr/bin/env python3

# Script per testare direttamente le parti dell'endpoint SOF
# per identificare dove si verifica l'errore 500

import sys
import os
sys.path.append('.')

def test_database_queries():
    """Test delle query del database usate nell'endpoint SOF"""
    print("🔍 TEST QUERY DATABASE SOF")
    print("=" * 40)
    
    try:
        from database import get_db
        from sqlalchemy import text
        
        db = next(get_db())
        viaggio_id = 35  # ID di test
        
        print(f"📊 Test query per viaggio {viaggio_id}...")
        
        # 1. Test query viaggio
        print("\n1️⃣ Query viaggio...")
        viaggio_check = db.execute(text("""
            SELECT id, viaggio, nave_id, visibile FROM "VIAGGIO" WHERE id = :viaggio_id
        """), {"viaggio_id": viaggio_id}).fetchone()
        
        if viaggio_check:
            print(f"   ✅ Viaggio trovato: {viaggio_check}")
            viaggio_nome = viaggio_check[1]
            nave_id = viaggio_check[2]
        else:
            print("   ❌ Viaggio non trovato")
            return False
        
        # 2. Test query nave
        print("\n2️⃣ Query nave...")
        nave_data = db.execute(text("""
            SELECT "Nave" FROM "NAVI" WHERE id = :nave_id
        """), {"nave_id": nave_id}).fetchone()
        
        if nave_data:
            print(f"   ✅ Nave trovata: {nave_data[0]}")
            nave_nome = nave_data[0]
        else:
            print("   ❌ Nave non trovata")
            nave_nome = "N/A"
        
        # 3. Test query orari
        print("\n3️⃣ Query orari...")
        orari_data = db.execute(text("""
            SELECT
                COALESCE(a1."PORTI", o.porto_arrivo) as porto_arrivo_nome,
                o.sbe, o.pilota_arrivo, o.all_fast, o.tug_arrivo, o.draft, o.soc,
                COALESCE(a2."PORTI", o.porto_di_destinazione) as porto_destinazione_nome,
                o.pilota_partenza, o.tug_partenza, o.foc, o.fo, o."do", o.lo
            FROM "ORARI" o
            LEFT JOIN "ATLAS" a1 ON o.porto_arrivo = a1."ID_COD"
            LEFT JOIN "ATLAS" a2 ON o.porto_di_destinazione = a2."ID_COD"
            WHERE o.viaggio_id = :viaggio_id
        """), {"viaggio_id": viaggio_id}).fetchone()
        
        if orari_data:
            print(f"   ✅ Orari trovati: {len(orari_data)} campi")
        else:
            print("   ⚠️ Nessun orario trovato")
            orari_data = None
        
        # 4. Test query import
        print("\n4️⃣ Query import...")
        import_data = db.execute(text("""
            SELECT pol, pod, qt, type, created_at
            FROM "IMPORT"
            WHERE viaggio_id = :viaggio_id
            ORDER BY pol, pod, type
        """), {"viaggio_id": viaggio_id}).fetchall()
        
        print(f"   ✅ Import trovati: {len(import_data)} record")
        
        # 5. Test query export
        print("\n5️⃣ Query export...")
        export_data = db.execute(text("""
            SELECT pol, pod, qt, type, created_at
            FROM "EXPORT"
            WHERE viaggio_id = :viaggio_id
            ORDER BY pol, pod, type
        """), {"viaggio_id": viaggio_id}).fetchall()
        
        print(f"   ✅ Export trovati: {len(export_data)} record")
        
        db.close()
        
        return {
            "viaggio_id": viaggio_id,
            "viaggio_nome": viaggio_nome,
            "nave_nome": nave_nome,
            "orari_data": orari_data,
            "import_data": import_data,
            "export_data": export_data
        }
        
    except Exception as e:
        print(f"❌ Errore query database: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sof_generation(data):
    """Test della generazione SOF con dati reali"""
    print("\n🔧 TEST GENERAZIONE SOF")
    print("=" * 40)
    
    try:
        from main import generate_sof_docx
        
        print("📄 Generazione SOF con dati reali...")
        
        docx_path = generate_sof_docx(
            data["viaggio_id"],
            data["viaggio_nome"], 
            data["nave_nome"],
            data["orari_data"],
            data["import_data"],
            data["export_data"]
        )
        
        if os.path.exists(docx_path):
            file_size = os.path.getsize(docx_path)
            print(f"   ✅ SOF generato: {docx_path}")
            print(f"   📊 Dimensione: {file_size} bytes")
            
            # Cleanup
            os.remove(docx_path)
            print("   🗑️ File test rimosso")
            return True
        else:
            print("   ❌ File SOF non generato")
            return False
            
    except Exception as e:
        print(f"❌ Errore generazione SOF: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sof_database_operations(data):
    """Test delle operazioni database dell'endpoint SOF"""
    print("\n💾 TEST OPERAZIONI DATABASE SOF")
    print("=" * 40)
    
    try:
        from database import get_db
        from sqlalchemy import text
        import json
        
        db = next(get_db())
        viaggio_id = data["viaggio_id"]
        
        # Test controllo SOF esistente
        print("1️⃣ Test controllo SOF esistente...")
        existing_sof = db.execute(text("""
            SELECT file_path, filename, file_size, statistics
            FROM "SOF_DOCUMENTS"
            WHERE viaggio_id = :viaggio_id
        """), {"viaggio_id": viaggio_id}).fetchone()
        
        if existing_sof:
            print(f"   ℹ️ SOF esistente trovato: {existing_sof[1]}")
        else:
            print("   ℹ️ Nessun SOF esistente")
        
        # Test calcolo statistiche
        print("\n2️⃣ Test calcolo statistiche...")
        statistics = {
            "import_records": len(data["import_data"]),
            "export_records": len(data["export_data"]),
            "total_qt": sum([float(row[2]) for row in list(data["import_data"]) + list(data["export_data"])]),
            "unique_ports": len(set([row[0] for row in data["import_data"]] + [row[1] for row in data["import_data"]] +
                                   [row[0] for row in data["export_data"]] + [row[1] for row in data["export_data"]]))
        }
        print(f"   ✅ Statistiche calcolate: {statistics}")
        
        # Test verifica colonne SOF_DOCUMENTS
        print("\n3️⃣ Test struttura tabella SOF_DOCUMENTS...")
        try:
            db.execute(text("""
                ALTER TABLE "SOF_DOCUMENTS"
                ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            """))
            print("   ✅ Colonna created_at verificata")
        except Exception as e:
            print(f"   ⚠️ Errore colonna created_at: {e}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Errore operazioni database: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚀 DEBUG COMPLETO ENDPOINT SOF")
    print("=" * 50)
    
    # Test 1: Query database
    data = test_database_queries()
    if not data:
        print("\n❌ Test query database fallito")
        return False
    
    # Test 2: Generazione SOF
    sof_ok = test_sof_generation(data)
    if not sof_ok:
        print("\n❌ Test generazione SOF fallito")
        return False
    
    # Test 3: Operazioni database
    db_ok = test_sof_database_operations(data)
    if not db_ok:
        print("\n❌ Test operazioni database fallito")
        return False
    
    print(f"\n{'='*50}")
    print("✅ TUTTI I TEST PASSATI!")
    print("💡 Il problema potrebbe essere:")
    print("   - Gestione delle eccezioni nell'endpoint")
    print("   - Operazioni di file system")
    print("   - Sistema di notifiche")
    print("   - Commit delle transazioni")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
