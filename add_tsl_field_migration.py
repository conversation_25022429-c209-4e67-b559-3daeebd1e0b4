#!/usr/bin/env python3
"""
Migrazione per aggiungere il campo T.S.L. alla tabella NAVI
"""

from sqlalchemy import text
from database import SessionLocal, engine
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_tsl_field():
    """Aggiunge il campo TSL alla tabella NAVI"""
    db = SessionLocal()
    
    try:
        logger.info("🚢 Inizio migrazione: Aggiunta campo T.S.L. alla tabella NAVI")
        
        # 1. Verifica se la tabella NAVI esiste
        result = db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'NAVI'
            )
        """))
        
        if not result.scalar():
            logger.error("❌ Tabella NAVI non trovata!")
            return False
        
        logger.info("✅ Tabella NAVI trovata")
        
        # 2. Verifica se il campo TSL esiste già
        result = db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns 
                WHERE table_name = 'NAVI' AND column_name = 'TSL'
            )
        """))
        
        if result.scalar():
            logger.info("⚠️ Campo TSL già esistente nella tabella NAVI")
            return True
        
        # 3. Aggiungi il campo TSL
        logger.info("📝 Aggiunta campo TSL...")
        db.execute(text("""
            ALTER TABLE "NAVI" 
            ADD COLUMN "TSL" NUMERIC(12,2)
        """))
        
        db.commit()
        logger.info("✅ Campo TSL aggiunto con successo!")
        
        # 4. Verifica che il campo sia stato aggiunto
        result = db.execute(text("""
            SELECT column_name, data_type, numeric_precision, numeric_scale
            FROM information_schema.columns 
            WHERE table_name = 'NAVI' AND column_name = 'TSL'
        """))
        
        column_info = result.fetchone()
        if column_info:
            logger.info(f"✅ Verifica campo TSL: {column_info[0]} - {column_info[1]}({column_info[2]},{column_info[3]})")
        else:
            logger.error("❌ Errore: Campo TSL non trovato dopo l'aggiunta!")
            return False
        
        # 5. Mostra la struttura aggiornata della tabella
        logger.info("📋 Struttura aggiornata tabella NAVI:")
        result = db.execute(text("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'NAVI'
            ORDER BY ordinal_position
        """))
        
        columns = result.fetchall()
        for col in columns:
            nullable = "NULL" if col[2] == "YES" else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            logger.info(f"   - {col[0]}: {col[1]} {nullable}{default}")
        
        # 6. Conta le navi esistenti
        result = db.execute(text('SELECT COUNT(*) FROM "NAVI"'))
        count = result.scalar()
        logger.info(f"📊 Numero di navi nella tabella: {count}")
        
        if count > 0:
            logger.info("💡 Suggerimento: Puoi ora aggiornare i valori T.S.L. per le navi esistenti")
            
            # Mostra alcune navi di esempio
            result = db.execute(text("""
                SELECT id, "Nave", "Codice_Nave", "TSL" 
                FROM "NAVI" 
                LIMIT 5
            """))
            
            navi = result.fetchall()
            logger.info("📋 Prime 5 navi (con nuovo campo TSL):")
            for nave in navi:
                tsl_value = nave[3] if nave[3] is not None else "NULL"
                logger.info(f"   ID {nave[0]}: {nave[1]} ({nave[2]}) - TSL: {tsl_value}")
        
        logger.info("🎉 Migrazione completata con successo!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore durante la migrazione: {str(e)}")
        db.rollback()
        return False
        
    finally:
        db.close()

def rollback_tsl_field():
    """Rimuove il campo TSL dalla tabella NAVI (rollback)"""
    db = SessionLocal()
    
    try:
        logger.info("🔄 Rollback: Rimozione campo T.S.L. dalla tabella NAVI")
        
        # Verifica se il campo esiste
        result = db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns 
                WHERE table_name = 'NAVI' AND column_name = 'TSL'
            )
        """))
        
        if not result.scalar():
            logger.info("⚠️ Campo TSL non esistente, nessun rollback necessario")
            return True
        
        # Rimuovi il campo
        db.execute(text("""
            ALTER TABLE "NAVI" 
            DROP COLUMN "TSL"
        """))
        
        db.commit()
        logger.info("✅ Campo TSL rimosso con successo!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore durante il rollback: {str(e)}")
        db.rollback()
        return False
        
    finally:
        db.close()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        success = rollback_tsl_field()
    else:
        success = add_tsl_field()
    
    if success:
        logger.info("✅ Operazione completata con successo!")
        sys.exit(0)
    else:
        logger.error("❌ Operazione fallita!")
        sys.exit(1)
