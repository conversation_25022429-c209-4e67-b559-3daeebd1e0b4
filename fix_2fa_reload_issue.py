#!/usr/bin/env python3
"""
Risolve il problema del sistema 2FA con auto-reload di uvicorn
Crea un meccanismo per distinguere avvio iniziale da riavvii automatici
"""

import os
import sys
import time
import json
import logging
import tempfile
from pathlib import Path
from datetime import datetime, timedelta

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TwoFactorReloadManager:
    """Manager per gestire 2FA con auto-reload"""
    
    def __init__(self):
        self.session_file = Path(tempfile.gettempdir()) / "snip_2fa_session.json"
        self.session_duration = timedelta(hours=8)  # Sessione valida per 8 ore
        
    def create_session_token(self):
        """Crea un token di sessione dopo verifica 2FA"""
        session_data = {
            "authenticated": True,
            "timestamp": datetime.now().isoformat(),
            "expires": (datetime.now() + self.session_duration).isoformat(),
            "process_id": os.getpid(),
            "user": os.getenv("USERNAME", "unknown")
        }
        
        try:
            with open(self.session_file, 'w') as f:
                json.dump(session_data, f, indent=2)
            
            logger.info(f"✅ Token sessione 2FA creato: {self.session_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Errore creazione token sessione: {e}")
            return False
    
    def is_session_valid(self):
        """Verifica se esiste una sessione 2FA valida"""
        try:
            if not self.session_file.exists():
                logger.info("ℹ️ Nessun token sessione trovato")
                return False
            
            with open(self.session_file, 'r') as f:
                session_data = json.load(f)
            
            # Verifica scadenza
            expires = datetime.fromisoformat(session_data['expires'])
            if datetime.now() > expires:
                logger.info("⏰ Token sessione scaduto")
                self.cleanup_session()
                return False
            
            # Verifica autenticazione
            if not session_data.get('authenticated', False):
                logger.info("❌ Sessione non autenticata")
                return False
            
            logger.info("✅ Sessione 2FA valida trovata")
            return True
            
        except Exception as e:
            logger.error(f"❌ Errore verifica sessione: {e}")
            self.cleanup_session()
            return False
    
    def cleanup_session(self):
        """Pulisce il token di sessione"""
        try:
            if self.session_file.exists():
                self.session_file.unlink()
                logger.info("🧹 Token sessione rimosso")
        except Exception as e:
            logger.error(f"❌ Errore pulizia sessione: {e}")
    
    def is_reload_context(self):
        """Determina se siamo in un contesto di reload automatico"""
        # Controlla variabili d'ambiente di uvicorn
        reload_indicators = [
            "UVICORN_RELOADER",
            "RUN_MAIN",
            "WERKZEUG_RUN_MAIN"
        ]
        
        for indicator in reload_indicators:
            if os.getenv(indicator):
                logger.info(f"🔄 Rilevato contesto reload: {indicator}")
                return True
        
        # Controlla se il processo padre è uvicorn
        try:
            import psutil
            current_process = psutil.Process()
            parent = current_process.parent()
            
            if parent and 'uvicorn' in parent.name().lower():
                logger.info("🔄 Rilevato processo padre uvicorn")
                return True
                
        except Exception:
            pass  # psutil potrebbe non essere disponibile
        
        return False

def create_2fa_wrapper():
    """Crea un wrapper per la funzione verify_startup_code"""
    
    wrapper_code = '''
def verify_startup_code_with_reload_support():
    """
    Wrapper per verify_startup_code che supporta auto-reload
    """
    import sys
    import os
    from pathlib import Path
    
    # Importa il manager
    sys.path.insert(0, str(Path(__file__).parent))
    from fix_2fa_reload_issue import TwoFactorReloadManager
    
    manager = TwoFactorReloadManager()
    
    # Se siamo in un contesto di reload e abbiamo una sessione valida, salta 2FA
    if manager.is_reload_context() and manager.is_session_valid():
        print("🔄 Riavvio automatico rilevato - Sessione 2FA valida")
        print("✅ Bypass verifica 2FA per auto-reload")
        return True
    
    # Altrimenti, esegui la verifica 2FA normale
    print("🔐 Avvio iniziale - Verifica 2FA richiesta")
    
    # Importa la funzione originale
    from main import verify_startup_code as original_verify
    
    # Esegui verifica 2FA
    if original_verify():
        # Se la verifica ha successo, crea token sessione
        manager.create_session_token()
        return True
    else:
        return False

# Sostituisci la funzione originale
verify_startup_code = verify_startup_code_with_reload_support
'''
    
    return wrapper_code

def patch_main_py():
    """Applica patch al main.py per supportare auto-reload con 2FA"""
    logger.info("🔧 Applicazione patch per supporto 2FA con auto-reload...")

    try:
        # Leggi main.py
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()

        # Cerca la chiamata a verify_startup_code
        if 'if not verify_startup_code():' in content:
            logger.info("✅ Trovata chiamata verify_startup_code in main.py")

            # Backup del file originale
            backup_file = 'main.py.backup_2fa'
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"📁 Backup creato: {backup_file}")

            # Sostituisci la chiamata verify_startup_code con una versione inline
            modified_content = content.replace(
                'if not verify_startup_code():',
                '''# Sistema 2FA con supporto auto-reload
    try:
        import tempfile
        import json
        from datetime import datetime, timedelta
        from pathlib import Path

        # Gestione sessione 2FA
        session_file = Path(tempfile.gettempdir()) / "snip_2fa_session.json"
        session_duration = timedelta(hours=8)

        # Verifica se siamo in contesto reload
        is_reload = bool(os.getenv("UVICORN_RELOADER") or os.getenv("RUN_MAIN"))

        # Verifica sessione esistente
        session_valid = False
        if session_file.exists():
            try:
                with open(session_file, 'r') as f:
                    session_data = json.load(f)
                expires = datetime.fromisoformat(session_data['expires'])
                if datetime.now() < expires and session_data.get('authenticated'):
                    session_valid = True
            except:
                pass

        # Se reload e sessione valida, bypassa 2FA
        if is_reload and session_valid:
            print("🔄 Riavvio automatico rilevato - Sessione 2FA valida")
            print("✅ Bypass verifica 2FA per auto-reload")
            logger.info("🔄 Auto-reload: Bypass 2FA con sessione valida")
            startup_2fa_passed = True
        else:
            # Esegui verifica 2FA normale
            print("🔐 Avvio iniziale - Verifica 2FA richiesta")
            logger.info("🔐 Avvio iniziale: Richiesta verifica 2FA")
            startup_2fa_passed = verify_startup_code()

            # Se successo, crea sessione
            if startup_2fa_passed:
                session_data = {
                    "authenticated": True,
                    "timestamp": datetime.now().isoformat(),
                    "expires": (datetime.now() + session_duration).isoformat()
                }
                try:
                    with open(session_file, 'w') as f:
                        json.dump(session_data, f)
                    logger.info("✅ Sessione 2FA creata")
                except:
                    pass
    except Exception as e:
        logger.error(f"❌ Errore gestione 2FA: {e}")
        startup_2fa_passed = verify_startup_code()

    if not startup_2fa_passed:'''
            )

            # Scrivi il file modificato
            with open('main.py', 'w', encoding='utf-8') as f:
                f.write(modified_content)

            logger.info("✅ Patch applicata con successo a main.py")
            return True

        else:
            logger.warning("⚠️ Chiamata verify_startup_code non trovata in main.py")
            return False

    except Exception as e:
        logger.error(f"❌ Errore durante patch: {e}")
        return False

def create_startup_script():
    """Crea script di avvio che gestisce 2FA e auto-reload"""
    
    script_content = '''#!/usr/bin/env python3
"""
Script di avvio SNIP con supporto 2FA e auto-reload
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    print("🚀 AVVIO SNIP CON SUPPORTO 2FA E AUTO-RELOAD")
    print("=" * 60)
    
    # Verifica che main.py esista
    if not Path("main.py").exists():
        print("❌ File main.py non trovato!")
        return False
    
    # Pulisci sessioni precedenti
    from fix_2fa_reload_issue import TwoFactorReloadManager
    manager = TwoFactorReloadManager()
    manager.cleanup_session()
    
    print("🔐 Avvio con verifica 2FA iniziale...")
    print("🔄 Auto-reload abilitato per modifiche successive")
    print("-" * 60)
    
    try:
        # Avvia con uvicorn e reload
        cmd = [
            sys.executable, "-m", "uvicorn",
            "main:app",
            "--host", "0.0.0.0",
            "--port", "8002",
            "--reload",
            "--reload-delay", "1"
        ]
        
        print(f"🚀 Comando: {' '.join(cmd)}")
        print()
        
        # Esegui il comando
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\\n🛑 Arresto richiesto dall'utente")
        manager.cleanup_session()
    except Exception as e:
        print(f"\\n❌ Errore: {e}")
        manager.cleanup_session()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
'''
    
    script_file = "start_snip_2fa.py"
    try:
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        logger.info(f"✅ Script di avvio creato: {script_file}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore creazione script: {e}")
        return False

def main():
    """Funzione principale"""
    print("🔧 RISOLUZIONE PROBLEMA 2FA CON AUTO-RELOAD")
    print("=" * 60)
    print("Configura il sistema per mantenere 2FA all'avvio")
    print("ma bypassarlo durante i riavvii automatici")
    print("=" * 60)
    
    # Applica patch a main.py
    if not patch_main_py():
        logger.error("❌ Impossibile applicare patch a main.py")
        return False
    
    # Crea script di avvio
    if not create_startup_script():
        logger.error("❌ Impossibile creare script di avvio")
        return False
    
    # Istruzioni finali
    print("\n" + "=" * 60)
    print("✅ CONFIGURAZIONE COMPLETATA")
    print("=" * 60)
    print()
    print("🚀 COME USARE:")
    print("1. Avvio iniziale (con 2FA):")
    print("   python start_snip_2fa.py")
    print()
    print("2. Il sistema ora:")
    print("   ✅ Richiede 2FA al primo avvio")
    print("   ✅ Bypassa 2FA per auto-reload")
    print("   ✅ Mantiene sessione per 8 ore")
    print("   ✅ Pulisce automaticamente sessioni scadute")
    print()
    print("📁 FILE CREATI:")
    print("   - start_snip_2fa.py (script di avvio)")
    print("   - main.py.backup_2fa (backup originale)")
    print("   - main.py (versione modificata)")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\\n🛑 Configurazione interrotta dall'utente")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Errore durante configurazione: {e}")
        sys.exit(1)
