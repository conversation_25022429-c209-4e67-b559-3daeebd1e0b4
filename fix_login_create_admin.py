#!/usr/bin/env python3
"""
Script per risolvere il problema di login creando un utente amministratore
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import bcrypt
from datetime import datetime

# Configurazione database (stessa di config.py)
DATABASE_URL = "postgresql://re77:271077@localhost:5432/AGENTE"

def create_admin_user():
    """Crea un utente amministratore direttamente nel database"""
    
    print("🔧 RISOLUZIONE PROBLEMA LOGIN - Creazione Admin")
    print("=" * 50)
    
    try:
        # Connessione al database
        engine = create_engine(DATABASE_URL)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        print("✅ Connessione database stabilita")
        
        # Verifica se la tabella AGENTE esiste
        result = db.execute(text("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'AGENTE'"))
        table_exists = result.scalar() > 0
        
        if not table_exists:
            print("❌ Tabella AGENTE non esiste!")
            return False
        
        print("✅ Tabella AGENTE trovata")
        
        # Verifica utenti esistenti
        result = db.execute(text('SELECT COUNT(*) FROM "AGENTE"'))
        user_count = result.scalar()
        print(f"📊 Utenti esistenti: {user_count}")
        
        # Verifica se esistono admin (anche non attivi)
        result = db.execute(text('''
            SELECT email, visibile FROM "AGENTE"
            WHERE ruolo = 'SUPER_ADMIN'
        '''))
        existing_admins = result.fetchall()

        if existing_admins:
            print(f"📊 Admin trovati: {len(existing_admins)}")
            for admin in existing_admins:
                email, visibile = admin
                print(f"   👤 {email} - Visibile: {visibile}")

                # Attiva admin se non è visibile
                if visibile != 'si':
                    print(f"   🔧 Attivazione admin {email}...")
                    db.execute(text('''
                        UPDATE "AGENTE"
                        SET visibile = 'si'
                        WHERE email = :email
                    '''), {"email": email})
                    print(f"   ✅ Admin {email} attivato")

            db.commit()

            # Verifica admin attivi dopo aggiornamento
            result = db.execute(text('''
                SELECT email FROM "AGENTE"
                WHERE ruolo = 'SUPER_ADMIN' AND visibile = 'si'
                LIMIT 1
            '''))
            active_admin = result.fetchone()

            if active_admin:
                print(f"✅ Admin attivo disponibile: {active_admin[0]}")
                return True
        
        # Crea password hash
        password = "admin123"
        password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        print("🔐 Password hashata generata")
        
        # Inserisci nuovo utente amministratore
        admin_data = {
            "email": "<EMAIL>",
            "password": password_hash,
            "nome": "Admin",
            "cognome": "Sistema",
            "ruolo": "SUPER_ADMIN",
            "reparto": "AMMINISTRAZIONE",
            "visibile": "si",
            "created_at": datetime.now(),
            "last_password_change": datetime.now()
        }
        
        # Prima verifica se l'email esiste già
        result = db.execute(text('SELECT id_user, visibile FROM "AGENTE" WHERE email = :email'), 
                           {"email": admin_data["email"]})
        existing_user = result.fetchone()
        
        if existing_user:
            # Aggiorna utente esistente per renderlo visibile
            print(f"👤 Utente {admin_data['email']} già esiste, aggiorno...")
            
            db.execute(text('''
                UPDATE "AGENTE" 
                SET visibile = 'si', 
                    ruolo = 'SUPER_ADMIN',
                    reparto = 'AMMINISTRAZIONE',
                    password = :password,
                    last_password_change = :last_change
                WHERE email = :email
            '''), {
                "password": password_hash,
                "last_change": datetime.now(),
                "email": admin_data["email"]
            })
            
            print("✅ Utente esistente aggiornato")
        else:
            # Crea nuovo utente
            print(f"👤 Creazione nuovo utente {admin_data['email']}...")
            
            db.execute(text('''
                INSERT INTO "AGENTE" 
                (email, password, "Nome", "Cognome", ruolo, reparto, visibile, created_at, last_password_change)
                VALUES (:email, :password, :nome, :cognome, :ruolo, :reparto, :visibile, :created_at, :last_password_change)
            '''), admin_data)
            
            print("✅ Nuovo utente amministratore creato")
        
        db.commit()
        
        # Verifica creazione
        result = db.execute(text('''
            SELECT email, ruolo, reparto, visibile 
            FROM "AGENTE" 
            WHERE email = :email
        '''), {"email": admin_data["email"]})
        
        user_check = result.fetchone()
        
        if user_check:
            print(f"✅ Verifica utente:")
            print(f"   📧 Email: {user_check[0]}")
            print(f"   👑 Ruolo: {user_check[1]}")
            print(f"   🏢 Reparto: {user_check[2]}")
            print(f"   👁️ Visibile: {user_check[3]}")
            
            if user_check[3] == 'si':
                print("\n🎉 UTENTE AMMINISTRATORE CREATO CON SUCCESSO!")
                print(f"🔐 Credenziali di login:")
                print(f"   Email: {admin_data['email']}")
                print(f"   Password: {password}")
                return True
            else:
                print("❌ Utente creato ma non visibile")
                return False
        else:
            print("❌ Errore nella verifica dell'utente creato")
            return False
            
    except Exception as e:
        print(f"❌ Errore durante creazione admin: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            db.close()
        except:
            pass

def test_login_after_creation():
    """Testa il login dopo la creazione dell'admin"""
    
    print("\n🧪 TEST LOGIN DOPO CREAZIONE ADMIN")
    print("-" * 40)
    
    try:
        import requests
        
        # Prova con diversi admin possibili
        admin_credentials = [
            {"username": "<EMAIL>", "password": "admin123"},
            {"username": "<EMAIL>", "password": "admin123"},
            {"username": "<EMAIL>", "password": "271077"},
        ]

        for login_data in admin_credentials:
            print(f"🔐 Test login: {login_data['username']}")

            response = requests.post(
                "http://127.0.0.1:8003/login",
                data=login_data,
                timeout=15,
                allow_redirects=False
            )

            print(f"   📊 Status: {response.status_code}")

            if response.status_code == 303:
                redirect_url = response.headers.get('location', '')
                print(f"   ✅ LOGIN RIUSCITO!")
                print(f"   🔄 Redirect a: {redirect_url}")
                print(f"   🔑 Credenziali funzionanti: {login_data['username']} / {login_data['password']}")
                return True
            elif response.status_code == 200:
                if "credenziali non valide" in response.text.lower():
                    print("   ❌ Credenziali non valide")
                elif "account non attivo" in response.text.lower():
                    print("   ❌ Account non attivo")
                else:
                    print("   ⚠️ Login processato ma stato incerto")

        return False
            
    except Exception as e:
        print(f"❌ Errore test login: {e}")
        return False

def main():
    """Funzione principale"""
    
    print("🚀 FIX SISTEMA LOGIN SNIP")
    print("=" * 50)
    print(f"🕐 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Step 1: Crea utente amministratore
    if not create_admin_user():
        print("\n❌ IMPOSSIBILE CREARE UTENTE AMMINISTRATORE")
        return False
    
    # Step 2: Testa login
    if test_login_after_creation():
        print("\n🎉 SISTEMA LOGIN RIPARATO CON SUCCESSO!")
        print("\n📋 PROSSIMI PASSI:")
        print("1. Usa <NAME_EMAIL> / admin123 per accedere")
        print("2. Attiva altri utenti dalla dashboard amministratore")
        print("3. Cambia la password di default per sicurezza")
        return True
    else:
        print("\n❌ UTENTE CREATO MA LOGIN ANCORA NON FUNZIONA")
        print("\n🔧 VERIFICA:")
        print("1. Che il server sia in esecuzione")
        print("2. Che non ci siano altri errori nei log")
        print("3. Che la funzione verify_password funzioni correttamente")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
