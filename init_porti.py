from sqlalchemy.orm import Session
from database import SessionLocal, engine
from models import Base, PortiGestione

def init_porti():
    # Crea le tabelle
    Base.metadata.create_all(bind=engine)
    
    # Crea una sessione
    db = SessionLocal()
    
    try:
        # Verifica se i porti esistono già
        porti_esistenti = db.query(PortiGestione).all()
        if porti_esistenti:
            print("I porti sono già stati inseriti nel database.")
            return
        
        # Dati dei porti
        porti = [
            PortiGestione(
                nome_porto="Porto di Salerno",
                codice_porto="ITSAL"
            ),
            PortiGestione(
                nome_porto="Porto di Gioia Tauro",
                codice_porto="ITGIT"
            )
        ]
        
        # Inserisci i porti nel database
        for porto in porti:
            db.add(porto)
        
        # Commit delle modifiche
        db.commit()
        print("Porti inseriti con successo nel database.")
        
    except Exception as e:
        print(f"Errore durante l'inserimento dei porti: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    init_porti() 