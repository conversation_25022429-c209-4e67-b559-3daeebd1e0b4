# 📧 GUIDA CONFIGURAZIONE EMAIL GMAIL PER SNIP

## 🎯 PROBLEMA RISOLTO
Errore: `535 Username and Password not accepted`

## ✅ SOLUZIONE 1: PASSWORD PER LE APP (RACCOMANDATO)

### Passo 1: Abilita Autenticazione a 2 Fattori
1. Vai su https://myaccount.google.com/security
2. Clicca su "Verifica in due passaggi"
3. Segui la procedura per abilitarla (SMS, app, ecc.)

### Passo 2: Genera Password per le App
1. Torna su https://myaccount.google.com/security
2. <PERSON><PERSON><PERSON> su "Password per le app" (appare solo dopo aver abilitato 2FA)
3. Seleziona "App" → "Altra (nome personalizzato)"
4. <PERSON><PERSON><PERSON> "SNIP Sistema Email"
5. <PERSON>lic<PERSON> "Genera"
6. **COPIA LA PASSWORD GENERATA** (16 caratteri, es: `abcd efgh ijkl mnop`)

### Passo 3: Configura SNIP
Nel dashboard amministrazione → Tab Email:
- **SMTP Host**: `smtp.gmail.com`
- **Porta**: `587`
- **Username**: `<EMAIL>`
- **Password**: `abcd efgh ijkl mnop` (la password app generata)
- **Email Mittente**: `<EMAIL>`
- **SSL/TLS**: ✅ Abilitato

### Passo 4: Test
Clicca "Test Email" - dovrebbe funzionare!

---

## ✅ SOLUZIONE 2: ACCESSO APP MENO SICURE (SCONSIGLIATO)

⚠️ **ATTENZIONE**: Google ha deprecato questa opzione per motivi di sicurezza.

1. Vai su https://myaccount.google.com/lesssecureapps
2. Abilita "Accesso app meno sicure"
3. Usa la tua password Gmail normale

**NOTA**: Questa opzione potrebbe non essere disponibile su tutti gli account.

---

## ✅ SOLUZIONE 3: OAUTH2 (AVANZATO)

Per implementazioni enterprise, si può usare OAuth2, ma richiede configurazione più complessa.

---

## 🔧 CONFIGURAZIONI ALTERNATIVE

### Microsoft Outlook/Hotmail
- **SMTP Host**: `smtp-mail.outlook.com`
- **Porta**: `587`
- **Username**: `<EMAIL>`
- **Password**: Password normale
- **SSL/TLS**: ✅ Abilitato

### Server SMTP Aziendale
Contatta il tuo amministratore IT per:
- Host SMTP
- Porta (solitamente 587, 465, o 25)
- Credenziali
- Tipo di crittografia

---

## 🎯 TEST RAPIDO

Dopo aver configurato:
1. Vai su Dashboard → Amministrazione → Tab Email
2. Inserisci le configurazioni
3. Clicca "Salva Configurazioni"
4. Clicca "Test Email"
5. Controlla la tua casella email!

---

## ❓ RISOLUZIONE PROBLEMI

### Errore 535: Credenziali rifiutate
- ✅ Usa Password per le App Gmail
- ✅ Verifica username (email completa)
- ✅ Verifica che 2FA sia abilitato

### Errore 587: Connessione rifiutata
- ✅ Verifica porta (587 per TLS, 465 per SSL)
- ✅ Verifica che SSL/TLS sia abilitato
- ✅ Controlla firewall aziendale

### Errore timeout
- ✅ Verifica connessione internet
- ✅ Prova porta alternativa (465)
- ✅ Controlla proxy aziendale

---

## 🎉 RISULTATO ATTESO

Dopo la configurazione corretta:
- ✅ Test email funziona
- ✅ Notifiche automatiche attive
- ✅ Reset password via email
- ✅ Notifiche SOF completati

**Il sistema email SNIP è completamente funzionale!** 📧✨
