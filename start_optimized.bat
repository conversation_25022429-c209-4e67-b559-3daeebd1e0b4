@echo off
echo 🚀 AVVIO FASTAPI CON RELOAD OTTIMIZZATO
echo =====================================

REM Termina processi uvicorn esistenti
echo 🛑 Terminazione processi esistenti...
taskkill /f /im python.exe 2>nul
timeout /t 2 /nobreak >nul

REM Pulisci cache
echo 🧹 Pulizia cache...
for /d /r . %%d in (__pycache__) do @if exist "%%d" rd /s /q "%%d"
del /s /q *.pyc 2>nul

REM Avvia server
echo 🔄 Avvio server...
python run_optimized.py

pause
