#!/usr/bin/env python3
"""
Debug COPY statements nel backup
"""

import gzip
from pathlib import Path

def debug_copy_statements():
    print("DEBUG COPY STATEMENTS")
    print("=" * 40)
    
    # Trova ultimo backup
    backup_dir = Path("backups")
    backup_files = list(backup_dir.glob("snip_backup_*.sql.gz"))
    latest_backup = max(backup_files, key=lambda f: f.stat().st_mtime)
    
    with gzip.open(latest_backup, 'rt', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    
    # Trova tutte le righe COPY e terminatori
    copy_lines = []
    terminator_lines = []
    
    for i, line in enumerate(lines, 1):
        if line.startswith('COPY public.'):
            copy_lines.append((i, line.strip()))
        elif line.strip() == '\\.':
            terminator_lines.append(i)
    
    print(f"COPY statements trovati: {len(copy_lines)}")
    print(f"Terminatori \\. trovati: {len(terminator_lines)}")
    
    print(f"\nCOPY STATEMENTS:")
    print("-" * 30)
    for line_num, copy_stmt in copy_lines:
        table_name = copy_stmt.split('"')[1]
        print(f"Riga {line_num:4d}: {table_name}")
    
    print(f"\nTERMINATORI:")
    print("-" * 15)
    for line_num in terminator_lines:
        print(f"Riga {line_num:4d}: \\.")
    
    # Verifica bilanciamento
    if len(copy_lines) != len(terminator_lines):
        print(f"\n❌ PROBLEMA: {len(copy_lines)} COPY vs {len(terminator_lines)} terminatori")
        
        # Trova COPY senza terminatore
        print(f"\nANALISI DETTAGLIATA:")
        print("-" * 20)
        
        copy_positions = [pos for pos, _ in copy_lines]
        
        for i, (copy_pos, copy_stmt) in enumerate(copy_lines):
            table_name = copy_stmt.split('"')[1]
            
            # Trova il prossimo COPY o fine file
            if i + 1 < len(copy_lines):
                next_copy_pos = copy_lines[i + 1][0]
            else:
                next_copy_pos = len(lines)
            
            # Cerca terminatore tra questo COPY e il prossimo
            terminators_in_range = [t for t in terminator_lines if copy_pos < t < next_copy_pos]
            
            if terminators_in_range:
                print(f"✓ {table_name}: COPY riga {copy_pos}, terminatore riga {terminators_in_range[0]}")
            else:
                print(f"❌ {table_name}: COPY riga {copy_pos}, NESSUN TERMINATORE")
                
                # Mostra contenuto tra COPY e prossimo COPY
                print(f"   Contenuto righe {copy_pos}-{min(copy_pos+10, next_copy_pos)}:")
                for j in range(copy_pos, min(copy_pos+10, next_copy_pos)):
                    if j < len(lines):
                        line_content = lines[j-1].strip()
                        if line_content:
                            print(f"   {j:4d}: {line_content[:60]}")
    else:
        print(f"\n✓ BILANCIATO: {len(copy_lines)} COPY = {len(terminator_lines)} terminatori")

if __name__ == "__main__":
    debug_copy_statements()
