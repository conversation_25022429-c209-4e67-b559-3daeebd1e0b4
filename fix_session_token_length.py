#!/usr/bin/env python3

import psycopg2
import sys

def fix_session_token_length():
    print("🔧 CORREZIONE LUNGHEZZA SESSION_TOKEN")
    print("=" * 50)
    
    try:
        # Connessione al database
        conn = psycopg2.connect(
            host='localhost',
            database='AGENTE',
            user='re77',
            password='271077',
            connect_timeout=5
        )
        conn.autocommit = False
        cursor = conn.cursor()
        
        print("✅ Connesso al database")
        
        # 1. Controlla la struttura attuale della tabella USER_SESSIONS
        print("\n1️⃣ Controllo struttura attuale...")
        cursor.execute("""
            SELECT column_name, data_type, character_maximum_length
            FROM information_schema.columns 
            WHERE table_name = 'USER_SESSIONS' 
                AND table_schema = 'public'
                AND column_name IN ('session_token', 'user_agent')
            ORDER BY column_name;
        """)
        
        columns = cursor.fetchall()
        for col in columns:
            name, data_type, max_length = col
            print(f"   {name}: {data_type}({max_length})")
        
        # 2. Controlla se ci sono sessioni esistenti
        print("\n2️⃣ Controllo sessioni esistenti...")
        cursor.execute('SELECT COUNT(*) FROM "USER_SESSIONS"')
        session_count = cursor.fetchone()[0]
        print(f"   Sessioni esistenti: {session_count}")
        
        if session_count > 0:
            # Mostra lunghezze dei token esistenti
            cursor.execute("""
                SELECT LENGTH(session_token) as token_length, 
                       LENGTH(user_agent) as agent_length,
                       user_id
                FROM "USER_SESSIONS" 
                ORDER BY token_length DESC 
                LIMIT 5
            """)
            lengths = cursor.fetchall()
            print("   Lunghezze token esistenti:")
            for length_info in lengths:
                token_len, agent_len, user_id = length_info
                print(f"     User {user_id}: token={token_len}, user_agent={agent_len}")
        
        # 3. Aumenta la lunghezza del campo session_token
        print("\n3️⃣ Aumento lunghezza session_token...")
        try:
            cursor.execute("""
                ALTER TABLE "USER_SESSIONS" 
                ALTER COLUMN session_token TYPE VARCHAR(1000)
            """)
            print("   ✅ session_token esteso a VARCHAR(1000)")
        except Exception as e:
            print(f"   ⚠️ Errore modifica session_token: {e}")
        
        # 4. Aumenta la lunghezza del campo user_agent (spesso anche questo è lungo)
        print("\n4️⃣ Aumento lunghezza user_agent...")
        try:
            cursor.execute("""
                ALTER TABLE "USER_SESSIONS" 
                ALTER COLUMN user_agent TYPE VARCHAR(500)
            """)
            print("   ✅ user_agent esteso a VARCHAR(500)")
        except Exception as e:
            print(f"   ⚠️ Errore modifica user_agent: {e}")
        
        # 5. Verifica le modifiche
        print("\n5️⃣ Verifica modifiche...")
        cursor.execute("""
            SELECT column_name, data_type, character_maximum_length
            FROM information_schema.columns 
            WHERE table_name = 'USER_SESSIONS' 
                AND table_schema = 'public'
                AND column_name IN ('session_token', 'user_agent')
            ORDER BY column_name;
        """)
        
        updated_columns = cursor.fetchall()
        for col in updated_columns:
            name, data_type, max_length = col
            print(f"   ✅ {name}: {data_type}({max_length})")
        
        # 6. Test inserimento con token lungo
        print("\n6️⃣ Test inserimento token lungo...")
        test_token = "eyJhbGciOiAiSFMyNTYiLCAidHlwIjogIkpXVCJ9." + "x" * 400  # Token di test lungo
        test_user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"
        
        try:
            cursor.execute("""
                INSERT INTO "USER_SESSIONS" (user_id, session_token, ip_address, user_agent, expires_at, is_active)
                VALUES (999, %s, '127.0.0.1', %s, NOW() + INTERVAL '1 hour', TRUE)
                ON CONFLICT (session_token) DO NOTHING
            """, (test_token, test_user_agent))
            
            print(f"   ✅ Test inserimento riuscito (token length: {len(test_token)})")
            
            # Rimuovi il record di test
            cursor.execute("DELETE FROM \"USER_SESSIONS\" WHERE user_id = 999")
            print("   ✅ Record di test rimosso")
            
        except Exception as e:
            print(f"   ❌ Test inserimento fallito: {e}")
        
        # Commit delle modifiche
        conn.commit()
        print("\n✅ CORREZIONE COMPLETATA!")
        print("💡 I campi session_token e user_agent sono stati estesi")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Errore durante la correzione: {e}")
        try:
            conn.rollback()
            conn.close()
        except:
            pass
        return False

if __name__ == "__main__":
    success = fix_session_token_length()
    if success:
        print("\n🎉 Correzione completata con successo!")
        print("💡 Riavvia l'app per utilizzare i campi estesi")
    else:
        print("\n❌ Correzione fallita!")
    
    sys.exit(0 if success else 1)
