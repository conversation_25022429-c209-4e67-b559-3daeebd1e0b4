#!/usr/bin/env python3
"""
Script per correggere tutti i vincoli della tabella DEPARTMENT_NOTIFICATIONS
"""

import psycopg2
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_all_constraints():
    """Corregge tutti i vincoli della tabella DEPARTMENT_NOTIFICATIONS"""
    try:
        # Connessione al database
        conn = psycopg2.connect(
            host='localhost',
            database='AGENTE',
            user='re77',
            password='271077',
            connect_timeout=5
        )
        conn.autocommit = False
        cursor = conn.cursor()
        
        logger.info("✅ Connesso al database")
        
        # Rimuovi tutti i vincoli problematici
        logger.info("🔧 Rimozione vincoli esistenti...")
        
        constraints_to_drop = [
            'chk_notification_type',
            'chk_priority',
            'chk_target_reparto'
        ]
        
        for constraint in constraints_to_drop:
            try:
                cursor.execute(f"""
                    ALTER TABLE "DEPARTMENT_NOTIFICATIONS" 
                    DROP CONSTRAINT IF EXISTS {constraint};
                """)
                logger.info(f"   ✅ Rimosso vincolo: {constraint}")
            except Exception as e:
                logger.warning(f"   ⚠️ Errore rimozione {constraint}: {e}")
        
        # Aggiungi vincoli corretti
        logger.info("🔧 Aggiunta vincoli corretti...")
        
        # Vincolo per notification_type (accetta sia maiuscolo che minuscolo)
        cursor.execute("""
            ALTER TABLE "DEPARTMENT_NOTIFICATIONS" 
            ADD CONSTRAINT chk_notification_type 
            CHECK (notification_type IN ('info', 'warning', 'error', 'success', 'INFO', 'WARNING', 'ERROR', 'SUCCESS'));
        """)
        logger.info("   ✅ Vincolo notification_type aggiunto")
        
        # Vincolo per priority (accetta valori numerici e stringhe)
        cursor.execute("""
            ALTER TABLE "DEPARTMENT_NOTIFICATIONS" 
            ADD CONSTRAINT chk_priority 
            CHECK (priority IN ('low', 'normal', 'high', 'urgent', '1', '2', '3', '4') OR 
                   (priority::text ~ '^[1-4]$'));
        """)
        logger.info("   ✅ Vincolo priority aggiunto")
        
        # Vincolo per target_reparto
        cursor.execute("""
            ALTER TABLE "DEPARTMENT_NOTIFICATIONS" 
            ADD CONSTRAINT chk_target_reparto 
            CHECK (target_reparto IN ('OPERATIVO', 'AMMINISTRATIVO', 'TECNICO', 'ALL'));
        """)
        logger.info("   ✅ Vincolo target_reparto aggiunto")
        
        # Commit delle modifiche
        conn.commit()
        logger.info("✅ Tutti i vincoli sono stati corretti!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore durante la correzione: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
        
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def test_notification_creation():
    """Testa la creazione di notifiche con i nuovi vincoli"""
    try:
        # Connessione al database
        conn = psycopg2.connect(
            host='localhost',
            database='AGENTE',
            user='re77',
            password='271077',
            connect_timeout=5
        )
        conn.autocommit = False
        cursor = conn.cursor()
        
        logger.info("🧪 Test creazione notifiche...")
        
        # Test con diversi tipi e priorità
        test_cases = [
            ('WARNING', '3', 'Test WARNING con priorità 3'),
            ('info', 'high', 'Test info con priorità high'),
            ('SUCCESS', '4', 'Test SUCCESS con priorità 4'),
            ('error', 'urgent', 'Test error con priorità urgent')
        ]
        
        created_ids = []
        
        for notification_type, priority, message in test_cases:
            try:
                cursor.execute("""
                    INSERT INTO "DEPARTMENT_NOTIFICATIONS"
                    (title, message, notification_type, target_reparto, created_by, priority, send_email, is_active)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING id;
                """, (
                    f"Test {notification_type}",
                    message,
                    notification_type,
                    "OPERATIVO",
                    1,
                    priority,
                    False,
                    True
                ))
                
                notification_id = cursor.fetchone()[0]
                created_ids.append(notification_id)
                logger.info(f"   ✅ Test {notification_type}/{priority}: OK (ID: {notification_id})")
                
            except Exception as e:
                logger.error(f"   ❌ Test {notification_type}/{priority}: FALLITO - {e}")
        
        # Rimuovi le notifiche di test
        for notification_id in created_ids:
            cursor.execute("DELETE FROM \"DEPARTMENT_NOTIFICATIONS\" WHERE id = %s", (notification_id,))
        
        conn.commit()
        logger.info(f"✅ Test completato! Rimosse {len(created_ids)} notifiche di test")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore durante il test: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
        
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    logger.info("🔧 CORREZIONE COMPLETA VINCOLI NOTIFICHE")
    logger.info("=" * 50)
    
    # Correggi tutti i vincoli
    success = fix_all_constraints()
    
    if success:
        # Testa la creazione di notifiche
        test_success = test_notification_creation()
        
        if test_success:
            logger.info("\n🎉 OPERAZIONE COMPLETATA CON SUCCESSO!")
            logger.info("Tutti i vincoli sono stati corretti e testati.")
            logger.info("Le notifiche di ripristino SOF ora funzioneranno correttamente.")
        else:
            logger.error("\n⚠️ VINCOLI CORRETTI MA TEST FALLITO!")
    else:
        logger.error("\n❌ OPERAZIONE FALLITA!")
    
    exit(0 if success else 1)
