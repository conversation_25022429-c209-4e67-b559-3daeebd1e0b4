#!/usr/bin/env python3
"""
Script per verificare le dipendenze del sistema 2FA SMS
"""

import sys

def check_dependency(module_name, import_name=None, description=""):
    """Verifica se un modulo è installato"""
    if import_name is None:
        import_name = module_name
    
    try:
        module = __import__(import_name)
        version = getattr(module, '__version__', 'N/A')
        print(f"✅ {module_name} ({version}) - {description}")
        return True
    except ImportError:
        print(f"❌ {module_name} - {description} (MANCANTE)")
        return False

def test_requests_functionality():
    """Testa che requests funzioni correttamente"""
    try:
        import requests
        print("\n🧪 Test funzionalità requests...")
        
        # Test connessione
        response = requests.get('https://httpbin.org/status/200', timeout=5)
        if response.status_code == 200:
            print("✅ Connessione HTTP funzionante")
            return True
        else:
            print(f"⚠️  Risposta HTTP inaspettata: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Errore test requests: {e}")
        return False

def main():
    """Verifica tutte le dipendenze del 2FA"""
    print("🔍 VERIFICA DIPENDENZE SISTEMA 2FA SMS")
    print("=" * 50)
    
    # Dipendenze essenziali per il 2FA
    essential_deps = [
        ("requests", "requests", "Libreria per invio SMS"),
        ("random", "random", "Generazione codici casuali (built-in)"),
        ("logging", "logging", "Sistema di logging (built-in)"),
        ("os", "os", "Variabili d'ambiente (built-in)"),
        ("typing", "typing", "Type hints (built-in)"),
    ]
    
    print("\n📋 DIPENDENZE ESSENZIALI:")
    essential_ok = all(check_dependency(*dep) for dep in essential_deps)
    
    # Test funzionalità requests
    requests_ok = test_requests_functionality()
    
    # Verifica file di configurazione
    print("\n📁 FILE DI CONFIGURAZIONE:")
    import os
    config_files = [
        ("sms_config.py", "Configurazione provider SMS"),
        (".env.example", "Template variabili d'ambiente"),
        ("requirements_2fa.txt", "Lista dipendenze 2FA"),
    ]
    
    config_ok = True
    for filename, description in config_files:
        if os.path.exists(filename):
            print(f"✅ {filename} - {description}")
        else:
            print(f"⚠️  {filename} - {description} (mancante)")
            config_ok = False
    
    # Test script di supporto
    print("\n🧪 SCRIPT DI TEST:")
    test_scripts = [
        ("test_2fa.py", "Test configurazione sistema"),
        ("test_2fa_complete.py", "Test flusso completo"),
        ("test_sms_real.py", "Test invio SMS reale"),
    ]
    
    for filename, description in test_scripts:
        if os.path.exists(filename):
            print(f"✅ {filename} - {description}")
        else:
            print(f"⚠️  {filename} - {description} (mancante)")
    
    print("\n" + "=" * 50)
    print("📊 RIEPILOGO")
    print("=" * 50)
    
    if essential_ok and requests_ok:
        print("✅ Tutte le dipendenze essenziali per il 2FA sono OK")
        print("🔐 Il sistema 2FA SMS è pronto per l'uso")
        
        if config_ok:
            print("✅ Tutti i file di configurazione sono presenti")
        else:
            print("⚠️  Alcuni file di configurazione sono mancanti (non critici)")
        
        print("\n🚀 PROSSIMI PASSI:")
        print("1. Testa il sistema: python test_2fa.py")
        print("2. Avvia l'applicazione: python main.py")
        print("3. Riceverai un SMS con il codice al numero 3805127005")
        
    else:
        print("❌ Alcune dipendenze essenziali sono mancanti")
        print("\n🔧 RISOLUZIONE:")
        print("1. Installa requests: pip install requests")
        print("2. Verifica connessione internet")
        print("3. Riprova questo test")
    
    return essential_ok and requests_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
