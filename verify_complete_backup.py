#!/usr/bin/env python3
"""
Verifica backup completo con tutte le 18 tabelle
"""

import gzip
from pathlib import Path

def verify_complete_backup():
    print("VERIFICA BACKUP COMPLETO - 18 TABELLE")
    print("=" * 50)
    
    # Trova ultimo backup
    backup_dir = Path("backups")
    backup_files = list(backup_dir.glob("snip_backup_*.sql.gz"))
    
    if not backup_files:
        print("Nessun backup trovato")
        return
    
    latest_backup = max(backup_files, key=lambda f: f.stat().st_mtime)
    print(f"File: {latest_backup.name}")
    print(f"Dimensione: {latest_backup.stat().st_size} bytes")
    
    try:
        with gzip.open(latest_backup, 'rt', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        print(f"Righe totali: {len(lines)}")
        
        # Verifica header PGDMP
        print(f"\nHeader PGDMP: {'PGDMP' in content}")
        print(f"CREATE TYPE: {content.count('CREATE TYPE')}")
        print(f"COPY statements: {content.count('COPY public.')}")
        
        # Verifica tutte le 18 tabelle
        print(f"\nVERIFICA 18 TABELLE:")
        print("-" * 40)
        
        expected_tables = [
            "AGENTE", "ARMATORE", "ATLAS", "AUDIT_LOG", 
            "CODICI_ATLAS", "DEPARTMENT_NOTIFICATIONS", 
            "EXPORT", "IMPORT", "LOGIN_ATTEMPTS", "NAVI",
            "ORARI", "PORTI_GESTIONE", "SOF_DOCUMENTS", 
            "SYSTEM_CONFIG", "SYSTEM_STATS", 
            "USER_NOTIFICATION_READ", "USER_SESSIONS", "VIAGGIO"
        ]
        
        tables_found = 0
        for table in expected_tables:
            if f'COPY public."{table}"' in content:
                print(f"✓ {table}")
                tables_found += 1
            else:
                print(f"✗ {table}")
        
        print(f"\nTabelle trovate: {tables_found}/18")
        
        # Verifica tabelle vuote incluse
        print(f"\nTABELLE VUOTE INCLUSE:")
        empty_tables = ["CODICI_ATLAS", "SYSTEM_STATS", "USER_NOTIFICATION_READ"]
        for table in empty_tables:
            if f'COPY public."{table}"' in content:
                print(f"✓ {table} - Inclusa (vuota)")
            else:
                print(f"✗ {table} - Mancante")
        
        # Prime 50 righe per vedere formato
        print(f"\nPRIME 50 RIGHE (formato):")
        print("-" * 40)
        for i, line in enumerate(lines[:50], 1):
            clean_line = line.encode('ascii', 'ignore').decode('ascii')
            print(f"{i:2d}: {clean_line}")
        
        if tables_found == 18:
            print(f"\n✅ BACKUP COMPLETO: TUTTE LE 18 TABELLE INCLUSE")
            print(f"✅ FORMATO: Identico a AGENTE.sql")
            print(f"✅ TABELLE VUOTE: Incluse correttamente")
        else:
            print(f"\n❌ BACKUP INCOMPLETO: {tables_found}/18 tabelle")
            
    except Exception as e:
        print(f"Errore: {e}")

if __name__ == "__main__":
    verify_complete_backup()
