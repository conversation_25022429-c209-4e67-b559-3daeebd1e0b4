#!/usr/bin/env python3
"""
Script finale per correggere tutte le funzioni malformate in main.py
"""

import re
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_all_function_definitions():
    """Corregge tutte le definizioni delle funzioni malformate"""
    logger.info("🔧 Correzione finale di tutte le funzioni in main.py")
    
    try:
        # Leggi il file
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Lista di tutte le correzioni da fare
        corrections = [
            # Funzioni con sintassi malformata
            (r'def notifications\(request: Request: Agente = Depends\(require_auth\)\):',
             r'def notifications(request: Request, current_user: Agente = Depends(require_auth)):'),
            
            (r'def contabilita_pagamenti\(request: Request: Agente = Depends\(require_reparto_access\(RepartoEnum\.CONTABILITA\)\)\):',
             r'def contabilita_pagamenti(request: Request, current_user: Agente = Depends(require_reparto_access(RepartoEnum.CONTABILITA))):'),
            
            (r'def contabilita_report\(request: Request: Agente = Depends\(require_reparto_access\(RepartoEnum\.CONTABILITA\)\)\):',
             r'def contabilita_report(request: Request, current_user: Agente = Depends(require_reparto_access(RepartoEnum.CONTABILITA))):'),
            
            (r'def contabilita_bilanci\(request: Request: Agente = Depends\(require_reparto_access\(RepartoEnum\.CONTABILITA\)\)\):',
             r'def contabilita_bilanci(request: Request, current_user: Agente = Depends(require_reparto_access(RepartoEnum.CONTABILITA))):'),
            
            (r'def contabilita_costi\(request: Request: Agente = Depends\(require_reparto_access\(RepartoEnum\.CONTABILITA\)\)\):',
             r'def contabilita_costi(request: Request, current_user: Agente = Depends(require_reparto_access(RepartoEnum.CONTABILITA))):'),
            
            (r'def shortsea_rotte\(request: Request: Agente = Depends\(require_reparto_access\(RepartoEnum\.SHORTSEA\)\)\):',
             r'def shortsea_rotte(request: Request, current_user: Agente = Depends(require_reparto_access(RepartoEnum.SHORTSEA))):'),
            
            (r'def shortsea_spedizioni\(request: Request: Agente = Depends\(require_reparto_access\(RepartoEnum\.SHORTSEA\)\)\):',
             r'def shortsea_spedizioni(request: Request, current_user: Agente = Depends(require_reparto_access(RepartoEnum.SHORTSEA))):'),
            
            (r'def shortsea_report\(request: Request: Agente = Depends\(require_reparto_access\(RepartoEnum\.SHORTSEA\)\)\):',
             r'def shortsea_report(request: Request, current_user: Agente = Depends(require_reparto_access(RepartoEnum.SHORTSEA))):'),
            
            (r'def admin_backup\(request: Request: Agente = Depends\(require_min_role\(RuoloEnum\.SUPER_ADMIN\)\)\):',
             r'def admin_backup(request: Request, current_user: Agente = Depends(require_min_role(RuoloEnum.SUPER_ADMIN))):'),
            
            (r'def change_password_required_page\(request: Request: Agente = Depends\(require_auth\), db: Session = Depends\(get_db\)\):',
             r'def change_password_required_page(request: Request, current_user: Agente = Depends(require_auth), db: Session = Depends(get_db)):'),
            
            (r'def dashboard_amministrazione\(request: Request: Agente = Depends\(require_reparto_access\(RepartoEnum\.AMMINISTRAZIONE\)\)\):',
             r'def dashboard_amministrazione(request: Request, current_user: Agente = Depends(require_reparto_access(RepartoEnum.AMMINISTRAZIONE))):'),
            
            (r'def test_messages\(request: Request: Agente = Depends\(get_current_user\)\):',
             r'def test_messages(request: Request, current_user: Agente = Depends(get_current_user)):'),
            
            (r'def admin_notifiche\(request: Request: Agente = Depends\(require_min_role\(RuoloEnum\.ADMIN\)\)\):',
             r'def admin_notifiche(request: Request, current_user: Agente = Depends(require_min_role(RuoloEnum.ADMIN))):'),
            
            (r'def user_notifiche\(request: Request: Agente = Depends\(require_auth\)\):',
             r'def user_notifiche(request: Request, current_user: Agente = Depends(require_auth)):'),
            
            (r'def dashboard_shortsea\(request: Request: Agente = Depends\(require_reparto_access\(RepartoEnum\.SHORTSEA\)\)\):',
             r'def dashboard_shortsea(request: Request, current_user: Agente = Depends(require_reparto_access(RepartoEnum.SHORTSEA))):'),
            
            (r'def dashboard_contabilita\(request: Request: Agente = Depends\(require_reparto_access\(RepartoEnum\.CONTABILITA\)\)\):',
             r'def dashboard_contabilita(request: Request, current_user: Agente = Depends(require_reparto_access(RepartoEnum.CONTABILITA))):'),
            
            (r'def operativo_porti\(request: Request: Agente = Depends\(require_reparto_access\(RepartoEnum\.OPERATIVO\)\), db: Session = Depends\(get_db\)\):',
             r'def operativo_porti(request: Request, current_user: Agente = Depends(require_reparto_access(RepartoEnum.OPERATIVO)), db: Session = Depends(get_db)):'),
            
            # Caso speciale con parametro porto_gestione
            (r'def operativo_sof_da_realizzare\(request: Request, porto_gestione: str = None  # type: ignore: Agente = Depends\(require_reparto_access\(RepartoEnum\.OPERATIVO\)\), db: Session = Depends\(get_db\)\):',
             r'def operativo_sof_da_realizzare(request: Request, porto_gestione: Optional[str] = None, current_user: Agente = Depends(require_reparto_access(RepartoEnum.OPERATIVO)), db: Session = Depends(get_db)):'),
        ]
        
        # Applica tutte le correzioni
        for pattern, replacement in corrections:
            old_content = content
            content = re.sub(pattern, replacement, content)
            if content != old_content:
                logger.info(f"   ✅ Corretto: {pattern[:50]}...")
        
        # Rimuovi caratteri di escape malformati
        content = content.replace("\\'", "'")
        
        # Scrivi il file modificato
        if content != original_content:
            with open('main.py', 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info("✅ File main.py corretto con successo!")
            
            # Conta le modifiche
            original_lines = len(original_content.split('\n'))
            new_lines = len(content.split('\n'))
            logger.info(f"📊 Righe originali: {original_lines}, Nuove righe: {new_lines}")
        else:
            logger.info("ℹ️ Nessuna modifica necessaria")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore durante la correzione: {e}")
        return False

def validate_syntax():
    """Valida la sintassi del file Python"""
    logger.info("🔍 Validazione sintassi Python...")
    
    try:
        import ast
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Prova a parsare il file
        ast.parse(content)
        logger.info("✅ Sintassi Python valida!")
        return True
        
    except SyntaxError as e:
        logger.error(f"❌ Errore di sintassi: {e}")
        logger.error(f"   Riga {e.lineno}: {e.text}")
        return False
    except Exception as e:
        logger.error(f"❌ Errore durante la validazione: {e}")
        return False

def main():
    """Esegue tutte le correzioni"""
    logger.info("🚀 CORREZIONE FINALE FUNZIONI")
    logger.info("=" * 50)
    
    # Correggi le definizioni delle funzioni
    fix_ok = fix_all_function_definitions()
    
    # Valida la sintassi
    syntax_ok = validate_syntax()
    
    # Riepilogo
    logger.info("=" * 50)
    logger.info("📋 RIEPILOGO:")
    logger.info(f"   Correzioni applicate: {'✅' if fix_ok else '❌'}")
    logger.info(f"   Sintassi valida: {'✅' if syntax_ok else '❌'}")
    
    if fix_ok and syntax_ok:
        logger.info("🎉 CORREZIONI COMPLETATE CON SUCCESSO!")
        logger.info("💡 Il file main.py ora dovrebbe essere sintatticamente corretto")
        logger.info("🔧 Prossimi passi:")
        logger.info("   - Esegui diagnostica IDE per verificare i tipi")
        logger.info("   - Testa il server per verificare il funzionamento")
    else:
        logger.info("⚠️ PROBLEMI RILEVATI")
        if not fix_ok:
            logger.info("   - Errore durante le correzioni")
        if not syntax_ok:
            logger.info("   - Errori di sintassi persistenti")
        logger.info("   - Controlla manualmente il file main.py")
    
    logger.info("=" * 50)
    logger.info("🏁 CORREZIONE COMPLETATA")

if __name__ == "__main__":
    main()
