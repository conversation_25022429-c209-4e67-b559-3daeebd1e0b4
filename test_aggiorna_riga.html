<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Test Aggiorna Riga Viaggio</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .console-output {
            background-color: #1e1e1e;
            color: #d4d4d4;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            padding: 15px;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .test-section {
            margin-bottom: 30px;
        }
        
        .before-after {
            display: flex;
            gap: 20px;
        }
        
        .before-after > div {
            flex: 1;
        }
        
        .highlight-success {
            background-color: #d4edda !important;
            transition: background-color 0.3s;
        }
        
        .highlight-error {
            background-color: #f8d7da !important;
            transition: background-color 0.3s;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    🧪 Test Sistema Aggiornamento Riga Viaggio
                </h1>
                <p class="text-center text-muted">
                    Questo test verifica che la funzione <code>aggiornaRigaViaggio</code> funzioni correttamente
                </p>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="test-section">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-play me-2"></i>Controlli Test
                            </h5>
                        </div>
                        <div class="card-body">
                            <button id="runTestBtn" class="btn btn-success btn-lg w-100 mb-3">
                                <i class="fas fa-rocket me-2"></i>Esegui Test
                            </button>
                            <button id="resetBtn" class="btn btn-secondary w-100 mb-3">
                                <i class="fas fa-undo me-2"></i>Reset Tabella
                            </button>
                            <button id="clearConsoleBtn" class="btn btn-outline-dark w-100">
                                <i class="fas fa-trash me-2"></i>Pulisci Console
                            </button>
                        </div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>Stato Test
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="testStatus" class="alert alert-secondary">
                                <i class="fas fa-clock me-2"></i>In attesa di esecuzione...
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="test-section">
                    <div class="card">
                        <div class="card-header bg-dark text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-terminal me-2"></i>Console Output
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <div id="consoleOutput" class="console-output">
🧪 Test System Ready
📋 Waiting for test execution...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>Tabella Test (Viaggio ID: 61)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="tableContainer">
                            <!-- La tabella verrà inserita qui dal JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-database me-2"></i>Dati Mock API
                        </h5>
                    </div>
                    <div class="card-body">
                        <pre id="mockData" class="bg-light p-3 rounded"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Console personalizzata
        const consoleOutput = document.getElementById('consoleOutput');
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;

        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : '📝';
            const color = type === 'error' ? '#ff6b6b' : '#d4d4d4';
            
            consoleOutput.innerHTML += `<span style="color: ${color}">[${timestamp}] ${prefix} ${message}</span>\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            addToConsole(args.join(' '), 'log');
            originalConsoleLog.apply(console, args);
        };

        console.error = function(...args) {
            addToConsole(args.join(' '), 'error');
            originalConsoleError.apply(console, args);
        };

        // Funzioni di utilità
        function updateTestStatus(message, type = 'info') {
            const statusDiv = document.getElementById('testStatus');
            const icons = {
                'info': 'fas fa-info-circle',
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle'
            };
            const classes = {
                'info': 'alert-info',
                'success': 'alert-success',
                'error': 'alert-danger',
                'warning': 'alert-warning'
            };
            
            statusDiv.className = `alert ${classes[type]}`;
            statusDiv.innerHTML = `<i class="${icons[type]} me-2"></i>${message}`;
        }

        function createMockTable() {
            const tableHTML = `
                <table class="table table-hover table-bordered">
                    <thead class="table-dark">
                        <tr>
                            <th>Codice</th>
                            <th>Nave</th>
                            <th>Porto Gestione</th>
                            <th>Porto Arrivo</th>
                            <th>Porto Destinazione</th>
                            <th>Data Arrivo</th>
                            <th>Data Partenza</th>
                            <th>Azioni</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr data-viaggio-id="61" id="testRow">
                            <td>
                                <span class="badge bg-secondary">TEST001</span>
                            </td>
                            <td class="cell-nave">
                                <strong>NAVE TEST</strong>
                            </td>
                            <td>
                                <span class="badge bg-primary porto-gestione-badge" title="Porto Test">
                                    <i class="fas fa-anchor me-1"></i>Porto Test
                                </span>
                            </td>
                            <td class="cell-porto-arrivo" title="Codice: N/A">
                                <span class="badge bg-info text-dark">Porto Arrivo Test</span>
                            </td>
                            <td class="cell-porto-destinazione" title="Codice: N/A">
                                <span class="badge bg-warning text-dark">Porto Destinazione Test</span>
                            </td>
                            <td>
                                <span class="text-success fw-bold">01/01/2024</span>
                            </td>
                            <td>
                                <span class="text-warning fw-bold">02/01/2024</span>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-warning" onclick="runSingleTest()">
                                    <i class="fas fa-edit"></i> Test
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            `;
            
            document.getElementById('tableContainer').innerHTML = tableHTML;
            console.log('📋 Tabella mock creata con successo');
        }

        // Dati mock per l'API
        const mockApiResponse = {
            success: true,
            viaggi: [
                {
                    id: 61,
                    viaggio: "CTA5525A",
                    nome_nave: "CATANIA",
                    nome_porto: "Catania",
                    porto_arrivo_nome: "Palermo",
                    porto_destinazione_nome: "Napoli",
                    porto_arrivo_code: "05",
                    porto_destinazione_code: "09",
                    data_arrivo: "2024-01-15",
                    data_partenza: "2024-01-16"
                }
            ]
        };

        // Mostra i dati mock
        document.getElementById('mockData').textContent = JSON.stringify(mockApiResponse, null, 2);
    </script>
    <script src="test_aggiorna_riga.js"></script>
    <script>
        // Event listeners
        document.getElementById('runTestBtn').addEventListener('click', function() {
            updateTestStatus('Esecuzione test in corso...', 'info');
            createMockTable();
            
            setTimeout(() => {
                runSingleTest();
            }, 500);
        });

        document.getElementById('resetBtn').addEventListener('click', function() {
            createMockTable();
            updateTestStatus('Tabella resettata', 'info');
            console.log('🔄 Tabella resettata allo stato iniziale');
        });

        document.getElementById('clearConsoleBtn').addEventListener('click', function() {
            consoleOutput.innerHTML = '🧪 Console cleared\n📋 Ready for new test...\n';
        });

        function runSingleTest() {
            console.log('🚀 Avvio test singolo...');
            updateTestStatus('Test in esecuzione...', 'warning');
            
            testAggiornaRigaViaggio(61).then(result => {
                if (result) {
                    updateTestStatus('✅ Test completato con successo!', 'success');
                    console.log('🎉 TEST SUPERATO! Il sistema funziona correttamente.');
                } else {
                    updateTestStatus('❌ Test fallito!', 'error');
                    console.log('❌ TEST FALLITO! Ci sono problemi nel sistema.');
                }
            }).catch(error => {
                updateTestStatus('💥 Errore durante il test!', 'error');
                console.error('💥 Errore critico:', error);
            });
        }

        // Inizializzazione
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Sistema di test inizializzato');
            createMockTable();
            updateTestStatus('Sistema pronto per il test', 'info');
        });
    </script>
</body>
</html>
