<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Debug Archivio Old</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>🔧 Debug Tab Archivio Old</h1>
        
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Scopo:</strong> Testare il pulsante "Visualizza Dettagli" della tab archivio_old
        </div>

        <!-- Test Funzioni JavaScript -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-code me-2"></i>Test Funzioni JavaScript</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-primary mb-2" onclick="testFunzioneEsiste()">
                            <i class="fas fa-search me-1"></i>Test Funzione Esiste
                        </button>
                        <br>
                        <button class="btn btn-warning mb-2" onclick="testChiamataAPI()">
                            <i class="fas fa-api me-1"></i>Test Chiamata API
                        </button>
                        <br>
                        <button class="btn btn-success mb-2" onclick="testModalFunziona()">
                            <i class="fas fa-modal me-1"></i>Test Modal
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-info mb-2" onclick="testVisualizzaDettagliJSON('test.json')">
                            <i class="fas fa-play me-1"></i>Test Funzione Completa
                        </button>
                        <br>
                        <button class="btn btn-secondary mb-2" onclick="mostraErroriConsole()">
                            <i class="fas fa-bug me-1"></i>Mostra Errori Console
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Risultati Test -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clipboard-list me-2"></i>Risultati Test</h5>
            </div>
            <div class="card-body">
                <div id="testResults">
                    <p class="text-muted">I risultati dei test appariranno qui...</p>
                </div>
            </div>
        </div>

        <!-- Test con File Reale -->
        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="fas fa-file-archive me-2"></i>Test con File Reale</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">Nome File JSON:</label>
                    <input type="text" class="form-control" id="testFilename" 
                           value="Archivio_Anno_2025_Trimestre_2_20250618_112649.json" 
                           placeholder="Nome file JSON">
                </div>
                <button class="btn btn-primary" onclick="testConFileReale()">
                    <i class="fas fa-play me-1"></i>Test con File Reale
                </button>
            </div>
        </div>
    </div>

    <!-- Modal Test -->
    <div class="modal fade" id="modalDettagliSOF" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalDettagliTitle">
                        <i class="fas fa-info-circle me-2"></i>
                        Test Modal
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="modalDettagliContent">
                    <!-- Contenuto dinamico -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Chiudi</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Include il file JavaScript del sistema -->
    <script src="/static/js/sof-archiviati.js"></script>
    
    <script>
        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const alertClass = type === 'error' ? 'alert-danger' : type === 'success' ? 'alert-success' : 'alert-info';
            
            resultsDiv.innerHTML += `
                <div class="alert ${alertClass} alert-sm">
                    <small class="text-muted">[${timestamp}]</small> ${message}
                </div>
            `;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function testFunzioneEsiste() {
            if (typeof window.visualizzaDettagliJSON === 'function') {
                logResult('✅ Funzione visualizzaDettagliJSON ESISTE', 'success');
            } else {
                logResult('❌ Funzione visualizzaDettagliJSON NON ESISTE', 'error');
            }
            
            if (typeof window.visualizzaDettagliViaggioCompleto === 'function') {
                logResult('✅ Funzione visualizzaDettagliViaggioCompleto ESISTE', 'success');
            } else {
                logResult('❌ Funzione visualizzaDettagliViaggioCompleto NON ESISTE', 'error');
            }
        }

        async function testChiamataAPI() {
            try {
                logResult('🔄 Testando chiamata API...');
                const response = await fetch('/api/operativo/sof/archivio_old/Archivio_Anno_2025_Trimestre_2_20250618_112649.json/viaggi');
                
                if (response.ok) {
                    const data = await response.json();
                    logResult(`✅ API funziona! Trovati ${data.viaggi ? data.viaggi.length : 0} viaggi`, 'success');
                } else {
                    logResult(`❌ API errore: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                logResult(`❌ Errore chiamata API: ${error.message}`, 'error');
            }
        }

        function testModalFunziona() {
            try {
                const modal = new bootstrap.Modal(document.getElementById('modalDettagliSOF'));
                modal.show();
                logResult('✅ Modal si apre correttamente', 'success');
                
                setTimeout(() => {
                    modal.hide();
                    logResult('✅ Modal si chiude correttamente', 'success');
                }, 2000);
            } catch (error) {
                logResult(`❌ Errore modal: ${error.message}`, 'error');
            }
        }

        async function testVisualizzaDettagliJSON(filename) {
            try {
                logResult(`🔄 Testando visualizzaDettagliJSON('${filename}')...`);
                
                if (typeof window.visualizzaDettagliJSON === 'function') {
                    await window.visualizzaDettagliJSON(filename);
                    logResult('✅ Funzione eseguita senza errori', 'success');
                } else {
                    logResult('❌ Funzione non disponibile', 'error');
                }
            } catch (error) {
                logResult(`❌ Errore esecuzione: ${error.message}`, 'error');
            }
        }

        async function testConFileReale() {
            const filename = document.getElementById('testFilename').value;
            if (!filename) {
                logResult('❌ Inserisci un nome file', 'error');
                return;
            }
            
            await testVisualizzaDettagliJSON(filename);
        }

        function mostraErroriConsole() {
            logResult('🔍 Controlla la console del browser (F12) per eventuali errori JavaScript');
            console.log('🔧 Debug: Test funzioni archivio old');
            console.log('window.visualizzaDettagliJSON:', typeof window.visualizzaDettagliJSON);
            console.log('window.visualizzaDettagliViaggioCompleto:', typeof window.visualizzaDettagliViaggioCompleto);
        }

        // Test automatico all'avvio
        document.addEventListener('DOMContentLoaded', function() {
            logResult('🚀 Pagina caricata, avvio test automatici...');
            setTimeout(testFunzioneEsiste, 1000);
        });
    </script>
</body>
</html>
