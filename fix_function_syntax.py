#!/usr/bin/env python3
"""
Script per correggere i problemi di sintassi delle funzioni in main.py
"""

import re
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_function_definitions():
    """Corregge le definizioni delle funzioni malformate"""
    logger.info("🔧 Correzione definizioni funzioni in main.py")
    
    try:
        # Leggi il file
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Pattern per trovare definizioni di funzione malformate
        # Cerca pattern come: def func(request: Request: Agente = Depends(...)):
        pattern = r'def (\w+)\(request: Request: Agente = Depends\(([^)]+)\)\):'
        
        def fix_function_def(match):
            func_name = match.group(1)
            depends_content = match.group(2)
            return f'def {func_name}(request: Request, current_user: Agente = Depends({depends_content})):'
        
        # Applica la correzione
        content = re.sub(pattern, fix_function_def, content)
        
        # Pattern più specifici per casi particolari
        specific_fixes = [
            # Caso con db parameter
            (r'def (\w+)\(request: Request: Agente = Depends\(([^)]+)\), db: Session = Depends\(get_db\)\):',
             r'def \1(request: Request, current_user: Agente = Depends(\2), db: Session = Depends(get_db)):'),
            
            # Caso con parametri aggiuntivi
            (r'def (\w+)\(request: Request, (\w+): ([^,]+) = None  # type: ignore: Agente = Depends\(([^)]+)\), db: Session = Depends\(get_db\)\):',
             r'def \1(request: Request, \2: \3 = None, current_user: Agente = Depends(\4), db: Session = Depends(get_db)):'),
            
            # Caso change_password_required_page
            (r'def change_password_required_page\(request: Request: Agente = Depends\(require_auth\), db: Session = Depends\(get_db\)\):',
             r'def change_password_required_page(request: Request, current_user: Agente = Depends(require_auth), db: Session = Depends(get_db)):'),
        ]
        
        for pattern, replacement in specific_fixes:
            content = re.sub(pattern, replacement, content)
        
        # Rimuovi la definizione duplicata di templates
        content = re.sub(r'\ntemplates = Jinja2Templates\(directory="templates"\)\n\n# Configurazione per l\'hashing delle password', 
                        r'\n\n# Configurazione per l\'hashing delle password', content)
        
        # Correggi problemi con Optional[str] = None  # type: ignore
        content = re.sub(r'porto: Optional\[str\] = None  # type: ignore,', 
                        r'porto: Optional[str] = None,', content)
        
        # Correggi import non utilizzati
        content = re.sub(r'from reportlab\.platypus import SimpleDocTemplateStyle', 
                        r'from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer', content)
        content = re.sub(r'from reportlab\.lib\.styles import getSampleStyleSheetStyle', 
                        r'from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle', content)
        
        # Correggi problemi con colors
        content = re.sub(r'\(-1, 0\)\.grey', r'(-1, 0), colors.grey', content)
        content = re.sub(r'\(-1, 0\)\.whitesmoke', r'(-1, 0), colors.whitesmoke', content)
        content = re.sub(r'\(-1, -1\)\.beige', r'(-1, -1), colors.beige', content)
        content = re.sub(r'1\.black', r'1, colors.black', content)
        
        # Scrivi il file modificato
        if content != original_content:
            with open('main.py', 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info("✅ File main.py corretto con successo!")
            
            # Conta le modifiche
            original_lines = len(original_content.split('\n'))
            new_lines = len(content.split('\n'))
            logger.info(f"📊 Righe originali: {original_lines}, Nuove righe: {new_lines}")
        else:
            logger.info("ℹ️ Nessuna modifica necessaria")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore durante la correzione: {e}")
        return False

def validate_syntax():
    """Valida la sintassi del file Python"""
    logger.info("🔍 Validazione sintassi Python...")
    
    try:
        import ast
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Prova a parsare il file
        ast.parse(content)
        logger.info("✅ Sintassi Python valida!")
        return True
        
    except SyntaxError as e:
        logger.error(f"❌ Errore di sintassi: {e}")
        logger.error(f"   Riga {e.lineno}: {e.text}")
        return False
    except Exception as e:
        logger.error(f"❌ Errore durante la validazione: {e}")
        return False

def main():
    """Esegue tutte le correzioni"""
    logger.info("🚀 CORREZIONE SINTASSI FUNZIONI")
    logger.info("=" * 50)
    
    # Correggi le definizioni delle funzioni
    fix_ok = fix_function_definitions()
    
    # Valida la sintassi
    syntax_ok = validate_syntax()
    
    # Riepilogo
    logger.info("=" * 50)
    logger.info("📋 RIEPILOGO:")
    logger.info(f"   Correzioni applicate: {'✅' if fix_ok else '❌'}")
    logger.info(f"   Sintassi valida: {'✅' if syntax_ok else '❌'}")
    
    if fix_ok and syntax_ok:
        logger.info("🎉 CORREZIONI COMPLETATE CON SUCCESSO!")
        logger.info("💡 Prossimi passi:")
        logger.info("   - Esegui nuovamente la diagnostica IDE")
        logger.info("   - Testa il server per verificare il funzionamento")
        logger.info("   - Controlla i log per eventuali warning")
    else:
        logger.info("⚠️ PROBLEMI RILEVATI")
        if not fix_ok:
            logger.info("   - Errore durante le correzioni")
        if not syntax_ok:
            logger.info("   - Errori di sintassi persistenti")
        logger.info("   - Controlla manualmente il file main.py")
    
    logger.info("=" * 50)
    logger.info("🏁 CORREZIONE COMPLETATA")

if __name__ == "__main__":
    main()
