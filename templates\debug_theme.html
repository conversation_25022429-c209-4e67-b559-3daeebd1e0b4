<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Tema - SNIP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .debug-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .log-output {
            background-color: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="theme-{{ user_theme|default('maritime') }}" data-theme="{{ user_theme|default('maritime') }}">
    <div class="container mt-4">
        <h1>🔧 Debug Tema SNIP</h1>
        
        <div class="debug-section">
            <h3>📊 Informazioni Utente</h3>
            <p><strong>Nome:</strong> {{ current_user.Nome }} {{ current_user.Cognome }}</p>
            <p><strong>Email:</strong> {{ current_user.email }}</p>
            <p><strong>ID:</strong> {{ current_user.id_user }}</p>
            <p><strong>Tema dal Server:</strong> <span id="server-theme">{{ user_theme }}</span></p>
            <p><strong>Classe Body:</strong> <span id="body-classes"></span></p>
        </div>

        <div class="debug-section">
            <h3>🎨 Test API Tema</h3>
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-primary" onclick="testGetTheme()">GET Tema Corrente</button>
                    <button class="btn btn-success" onclick="testSetTheme('dark')">SET Tema Scuro</button>
                    <button class="btn btn-warning" onclick="testSetTheme('light')">SET Tema Chiaro</button>
                    <button class="btn btn-info" onclick="testSetTheme('maritime')">SET Tema Marittimo</button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-secondary" onclick="clearLog()">Pulisci Log</button>
                    <button class="btn btn-danger" onclick="location.reload()">Ricarica Pagina</button>
                </div>
            </div>
        </div>

        <div class="debug-section">
            <h3>📝 Log Output</h3>
            <div class="log-output" id="log-output">
                Pronto per il debug...<br>
            </div>
        </div>

        <div class="debug-section">
            <h3>🔍 Test Dropdown Profilo</h3>
            <label class="form-label">Tema (come nel profilo):</label>
            <select class="form-select" id="theme-dropdown">
                <option value="maritime">Marittimo (Default)</option>
                <option value="dark">Scuro</option>
                <option value="light">Chiaro</option>
            </select>
            <button class="btn btn-primary mt-2" onclick="testLoadThemeInDropdown()">Carica Tema in Dropdown</button>
        </div>
    </div>

    <script>
        function log(message) {
            const logOutput = document.getElementById('log-output');
            const timestamp = new Date().toLocaleTimeString();
            logOutput.innerHTML += `[${timestamp}] ${message}<br>`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log-output').innerHTML = 'Log pulito...<br>';
        }

        // Mostra informazioni iniziali
        document.addEventListener('DOMContentLoaded', function() {
            const bodyClasses = document.body.className;
            document.getElementById('body-classes').textContent = bodyClasses;
            log(`🚀 Pagina caricata. Classi body: ${bodyClasses}`);
            log(`📊 Tema dal server: {{ user_theme }}`);
        });

        async function testGetTheme() {
            log('🔍 Test GET /api/user/theme...');
            try {
                const response = await fetch('/api/user/theme', {
                    method: 'GET',
                    credentials: 'include'
                });

                log(`📡 Status: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Risposta: ${JSON.stringify(data)}`);
                    log(`🎨 Tema ricevuto: ${data.theme}`);
                } else {
                    const errorText = await response.text();
                    log(`❌ Errore: ${errorText}`);
                }
            } catch (error) {
                log(`💥 Eccezione: ${error.message}`);
            }
        }

        async function testSetTheme(theme) {
            log(`🔄 Test POST /api/user/theme con tema: ${theme}`);
            try {
                const response = await fetch('/api/user/theme', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({ theme: theme })
                });

                log(`📡 Status: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Risposta: ${JSON.stringify(data)}`);
                    log(`🎨 Tema salvato: ${data.theme}`);
                } else {
                    const errorText = await response.text();
                    log(`❌ Errore: ${errorText}`);
                }
            } catch (error) {
                log(`💥 Eccezione: ${error.message}`);
            }
        }

        async function testLoadThemeInDropdown() {
            log('🎯 Test caricamento tema in dropdown...');
            try {
                const response = await fetch('/api/user/theme', {
                    method: 'GET',
                    credentials: 'include'
                });

                if (response.ok) {
                    const data = await response.json();
                    const userTheme = data.theme || 'maritime';
                    document.getElementById('theme-dropdown').value = userTheme;
                    log(`✅ Dropdown aggiornato con tema: ${userTheme}`);
                } else {
                    log(`❌ Errore caricamento tema per dropdown`);
                }
            } catch (error) {
                log(`💥 Errore: ${error.message}`);
            }
        }
    </script>
</body>
</html>
