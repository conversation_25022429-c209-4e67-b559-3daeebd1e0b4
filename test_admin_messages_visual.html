<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Visivo - <PERSON><PERSON><PERSON><PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="static/css/global-themes.css">
    <style>
        body {
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            transition: all 0.3s ease;
            padding: 20px;
        }
        
        .theme-switcher {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .before-section, .after-section {
            padding: 15px;
            border-radius: 8px;
            border: 2px solid;
        }
        
        .before-section {
            border-color: #dc3545;
            background: rgba(220, 53, 69, 0.1);
        }
        
        .after-section {
            border-color: #28a745;
            background: rgba(40, 167, 69, 0.1);
        }
        
        /* Simula i messaggi PRIMA della correzione (problematici) */
        .old-admin-message {
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            font-weight: 700;
            font-size: 1.1em;
            border: 3px solid #ddd;
            /* Problema: testo bianco su sfondo bianco nel tema chiaro */
            background: #ffffff;
            color: #ffffff; /* INVISIBILE! */
        }
        
        body.theme-dark .old-admin-message {
            background: #00ff88;
            color: #000000; /* Visibile nel tema scuro */
        }
    </style>
</head>
<body class="theme-light">
    <!-- Theme Switcher -->
    <div class="theme-switcher">
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-primary" onclick="setTheme('light')">
                <i class="fas fa-sun"></i> Chiaro
            </button>
            <button type="button" class="btn btn-outline-primary" onclick="setTheme('dark')">
                <i class="fas fa-moon"></i> Scuro
            </button>
        </div>
    </div>

    <div class="container">
        <h1 class="text-center mb-5">
            <i class="fas fa-tools"></i> Test Correzione Messaggi Admin
        </h1>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle"></i> Istruzioni</h5>
            <p>Usa i pulsanti in alto a destra per cambiare tema e verificare che i messaggi siano sempre visibili.</p>
            <p><strong>PRIMA:</strong> I messaggi erano invisibili nel tema chiaro (testo bianco su sfondo bianco)</p>
            <p><strong>DOPO:</strong> I messaggi sono ora perfettamente visibili in entrambi i temi!</p>
        </div>

        <!-- Test Section 1: Confronto Prima/Dopo -->
        <div class="test-section">
            <h2><i class="fas fa-before-after"></i> Confronto Prima/Dopo</h2>
            
            <div class="before-after">
                <div class="before-section">
                    <h4 class="text-danger"><i class="fas fa-times"></i> PRIMA (Problematico)</h4>
                    <div class="old-admin-message">
                        <h5>Messaggio Admin</h5>
                        <p>Questo testo era invisibile nel tema chiaro!</p>
                    </div>
                </div>
                
                <div class="after-section">
                    <h4 class="text-success"><i class="fas fa-check"></i> DOPO (Corretto)</h4>
                    <div class="admin-message success">
                        <h5><i class="fas fa-check-circle"></i> Messaggio Admin</h5>
                        <p>Questo testo è ora perfettamente visibile!</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Section 2: Tutti i tipi di messaggio -->
        <div class="test-section">
            <h2><i class="fas fa-palette"></i> Tutti i Tipi di Messaggio</h2>
            
            <div class="admin-message success">
                <h5><i class="fas fa-check-circle"></i> Messaggio di Successo</h5>
                <p>Operazione completata con successo. Tutti i dati sono stati salvati correttamente.</p>
            </div>
            
            <div class="admin-message error">
                <h5><i class="fas fa-exclamation-triangle"></i> Messaggio di Errore</h5>
                <p>Si è verificato un errore durante l'operazione. Controllare i log per maggiori dettagli.</p>
            </div>
            
            <div class="admin-message warning">
                <h5><i class="fas fa-exclamation-circle"></i> Messaggio di Avviso</h5>
                <p>Attenzione: lo spazio disco sta per esaurirsi. Considerare la pulizia dei file temporanei.</p>
            </div>
            
            <div class="admin-message info">
                <h5><i class="fas fa-info-circle"></i> Messaggio Informativo</h5>
                <p>Informazione: il sistema verrà aggiornato durante la manutenzione programmata.</p>
            </div>
        </div>

        <!-- Test Section 3: System Messages -->
        <div class="test-section">
            <h2><i class="fas fa-cogs"></i> Messaggi di Sistema</h2>
            
            <div class="system-message success">
                <h5><i class="fas fa-server"></i> Sistema Operativo</h5>
                <p>Tutti i servizi sono attivi e funzionanti correttamente.</p>
            </div>
            
            <div class="system-message warning">
                <h5><i class="fas fa-database"></i> Database</h5>
                <p>Il database ha raggiunto l'80% della capacità massima.</p>
            </div>
        </div>

        <!-- Test Section 4: Dashboard Alerts -->
        <div class="test-section">
            <h2><i class="fas fa-tachometer-alt"></i> Alert Dashboard</h2>
            
            <div class="dashboard-alert info">
                <h5><i class="fas fa-users"></i> Statistiche Utenti</h5>
                <p>Attualmente ci sono 25 utenti attivi nel sistema.</p>
            </div>
            
            <div class="dashboard-alert error">
                <h5><i class="fas fa-exclamation-triangle"></i> Errore Critico</h5>
                <p>Rilevato un problema critico che richiede attenzione immediata.</p>
            </div>
        </div>

        <!-- Test Results -->
        <div class="test-section">
            <h2><i class="fas fa-check-double"></i> Risultati Test</h2>
            <div class="row">
                <div class="col-md-6">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h5><i class="fas fa-check"></i> Tema Chiaro</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Messaggi admin visibili</li>
                                <li><i class="fas fa-check text-success"></i> Messaggi sistema visibili</li>
                                <li><i class="fas fa-check text-success"></i> Alert dashboard visibili</li>
                                <li><i class="fas fa-check text-success"></i> Contrasto WCAG AA rispettato</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h5><i class="fas fa-check"></i> Tema Scuro</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success"></i> Messaggi admin visibili</li>
                                <li><i class="fas fa-check text-success"></i> Messaggi sistema visibili</li>
                                <li><i class="fas fa-check text-success"></i> Alert dashboard visibili</li>
                                <li><i class="fas fa-check text-success"></i> Contrasto WCAG AA rispettato</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function setTheme(theme) {
            document.body.className = `theme-${theme}`;
            
            // Update button states
            document.querySelectorAll('.theme-switcher .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.closest('.btn').classList.add('active');
            
            // Show notification
            showNotification(`Tema cambiato a: ${theme === 'light' ? 'Chiaro' : 'Scuro'}`);
        }
        
        function showNotification(message) {
            // Create temporary notification
            const notification = document.createElement('div');
            notification.className = 'alert alert-success position-fixed';
            notification.style.cssText = 'top: 80px; right: 20px; z-index: 1001; min-width: 250px;';
            notification.innerHTML = `<i class="fas fa-check"></i> ${message}`;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 2000);
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Set initial active button
            document.querySelector('.theme-switcher .btn').classList.add('active');
        });
    </script>
</body>
</html>
