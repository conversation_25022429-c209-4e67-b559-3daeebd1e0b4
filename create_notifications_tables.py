#!/usr/bin/env python3
"""
Script per creare le tabelle del sistema di notifiche
"""

import psycopg2
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_notifications_tables():
    """Crea le tabelle per il sistema di notifiche"""
    try:
        # Connessione al database
        conn = psycopg2.connect(
            host='localhost',
            database='AGENTE',
            user='re77',
            password='271077',
            connect_timeout=5
        )
        conn.autocommit = False
        cursor = conn.cursor()
        
        logger.info("✅ Connesso al database")
        
        # 1. Crea tabella DEPARTMENT_NOTIFICATIONS
        logger.info("🔧 Creazione tabella DEPARTMENT_NOTIFICATIONS...")
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS "DEPARTMENT_NOTIFICATIONS" (
                id SERIAL PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                target_reparto VARCHAR(50) NOT NULL,
                created_by INTEGER,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP WITH TIME ZONE,
                is_active BOOLEAN DEFAULT TRUE,
                priority VARCHAR(20) DEFAULT 'normal',
                notification_type VARCHAR(50) DEFAULT 'info',
                
                -- Vincolo per reparto
                CONSTRAINT chk_target_reparto CHECK (target_reparto IN ('OPERATIVO', 'AMMINISTRATIVO', 'TECNICO', 'ALL')),
                CONSTRAINT chk_priority CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
                CONSTRAINT chk_notification_type CHECK (notification_type IN ('info', 'warning', 'error', 'success')),
                
                -- Foreign key per created_by (opzionale)
                CONSTRAINT fk_dept_notifications_created_by 
                    FOREIGN KEY (created_by) REFERENCES "AGENTE"(id_user) ON DELETE SET NULL
            );
        """)
        
        # 2. Crea tabella USER_NOTIFICATION_READ
        logger.info("🔧 Creazione tabella USER_NOTIFICATION_READ...")
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS "USER_NOTIFICATION_READ" (
                id SERIAL PRIMARY KEY,
                notification_id INTEGER NOT NULL,
                user_id INTEGER NOT NULL,
                read_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                
                -- Vincoli di integrità
                CONSTRAINT fk_user_notif_read_notification_id 
                    FOREIGN KEY (notification_id) REFERENCES "DEPARTMENT_NOTIFICATIONS"(id) ON DELETE CASCADE,
                CONSTRAINT fk_user_notif_read_user_id 
                    FOREIGN KEY (user_id) REFERENCES "AGENTE"(id_user) ON DELETE CASCADE,
                
                -- Vincolo di unicità: un utente può leggere una notifica solo una volta
                CONSTRAINT uk_user_notification_read UNIQUE (notification_id, user_id)
            );
        """)
        
        # 3. Crea indici per performance
        logger.info("🔧 Creazione indici per performance...")
        
        # Indici per DEPARTMENT_NOTIFICATIONS
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_dept_notifications_target_reparto 
            ON "DEPARTMENT_NOTIFICATIONS"(target_reparto);
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_dept_notifications_is_active 
            ON "DEPARTMENT_NOTIFICATIONS"(is_active);
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_dept_notifications_expires_at 
            ON "DEPARTMENT_NOTIFICATIONS"(expires_at);
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_dept_notifications_created_at 
            ON "DEPARTMENT_NOTIFICATIONS"(created_at);
        """)
        
        # Indici per USER_NOTIFICATION_READ
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_user_notif_read_user_id 
            ON "USER_NOTIFICATION_READ"(user_id);
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_user_notif_read_notification_id 
            ON "USER_NOTIFICATION_READ"(notification_id);
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_user_notif_read_read_at 
            ON "USER_NOTIFICATION_READ"(read_at);
        """)
        
        # 4. Inserisci alcune notifiche di esempio
        logger.info("🔧 Inserimento notifiche di esempio...")
        
        cursor.execute("""
            INSERT INTO "DEPARTMENT_NOTIFICATIONS" 
            (title, message, target_reparto, priority, notification_type, expires_at)
            VALUES 
            ('Benvenuto nel Sistema', 'Sistema di notifiche attivato con successo!', 'ALL', 'normal', 'success', NOW() + INTERVAL '30 days'),
            ('Manutenzione Programmata', 'Manutenzione del sistema programmata per il prossimo weekend.', 'OPERATIVO', 'high', 'warning', NOW() + INTERVAL '7 days'),
            ('Aggiornamento Completato', 'Il sistema è stato aggiornato con nuove funzionalità.', 'ALL', 'normal', 'info', NOW() + INTERVAL '15 days')
            ON CONFLICT DO NOTHING;
        """)
        
        # Commit delle modifiche
        conn.commit()
        
        logger.info("✅ Tabelle notifiche create con successo!")
        
        # Verifica finale
        cursor.execute("""
            SELECT table_name, column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name IN ('DEPARTMENT_NOTIFICATIONS', 'USER_NOTIFICATION_READ')
            ORDER BY table_name, ordinal_position;
        """)
        
        columns = cursor.fetchall()
        current_table = None
        
        for col in columns:
            table_name, column_name, data_type, nullable = col
            if table_name != current_table:
                logger.info(f"\n📋 Struttura tabella {table_name}:")
                current_table = table_name
            logger.info(f"   • {column_name}: {data_type} ({'NULL' if nullable == 'YES' else 'NOT NULL'})")
        
        # Conta le notifiche inserite
        cursor.execute('SELECT COUNT(*) FROM "DEPARTMENT_NOTIFICATIONS"')
        count = cursor.fetchone()[0]
        logger.info(f"\n📊 Notifiche di esempio inserite: {count}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore durante la creazione delle tabelle: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
        
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    logger.info("🚀 CREAZIONE TABELLE SISTEMA NOTIFICHE")
    logger.info("=" * 50)
    
    success = create_notifications_tables()
    
    if success:
        logger.info("\n🎉 OPERAZIONE COMPLETATA CON SUCCESSO!")
        logger.info("Il sistema di notifiche è ora completamente configurato.")
    else:
        logger.error("\n❌ OPERAZIONE FALLITA!")
        logger.error("Controlla i log per i dettagli dell'errore.")
    
    exit(0 if success else 1)
