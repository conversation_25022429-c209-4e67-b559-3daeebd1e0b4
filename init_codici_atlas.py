from sqlalchemy.orm import Session
from database import SessionLocal, engine
from models import Base, CodiciAtlas
import csv

def init_codici_atlas():
    # Crea le tabelle
    Base.metadata.create_all(bind=engine)
    
    # Crea una sessione
    db = SessionLocal()
    
    try:
        # Verifica se i codici esistono già
        codici_esistenti = db.query(CodiciAtlas).all()
        if codici_esistenti:
            print("I codici sono già stati inseriti nel database.")
            return
        
        # Leggi il file CSV
        codici = []
        with open('CODICI PORTI G-ATLAS.csv', 'r', encoding='utf-8') as file:
            csv_reader = csv.reader(file, delimiter=';')
            for row in csv_reader:
                if len(row) == 3:  # Verifica che la riga abbia 3 colonne
                    codice_porto, nome_porto, nazione = row
                    # Crea un nuovo record CodiciAtlas
                    codici.append(CodiciAtlas(
                        codice_porto=codice_porto,
                        nazione=nazione
                    ))
        
        # Inserisci i codici nel database
        for codice in codici:
            db.add(codice)
        
        # Commit delle modifiche
        db.commit()
        print(f"Importati {len(codici)} codici porto nel database.")
        
    except Exception as e:
        print(f"Errore durante l'inserimento dei codici: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    init_codici_atlas() 