
<script>
// SCRIPT DEBUG COMPLETO PER PORTI
console.log('🚀 INIZIO DEBUG COMPLETO PORTI');

// Test 1: Verifica elementi DOM
function debugElementiDOM() {
    console.log('\n🔍 TEST 1: ELEMENTI DOM');
    
    const targetIds = ['porto_arrivo', 'porto_destinazione', 'modifica_porto_arrivo', 'modifica_porto_destinazione'];
    const results = {};
    
    targetIds.forEach(id => {
        const elem = document.getElementById(id);
        results[id] = {
            exists: !!elem,
            tag: elem ? elem.tagName : null,
            visible: elem ? getComputedStyle(elem).display !== 'none' : false,
            parent: elem ? elem.parentElement.className : null
        };
        console.log(`   ${id}: ${results[id].exists ? '✅' : '❌'} ${JSON.stringify(results[id])}`);
    });
    
    return results;
}

// Test 2: Verifica API
async function debugAPI() {
    console.log('\n🌐 TEST 2: API ATLAS');
    
    try {
        const response = await fetch('/api/atlas?limit=3');
        console.log(`   Status: ${response.status}`);
        
        const data = await response.json();
        console.log(`   Success: ${data.success}`);
        console.log(`   Porti: ${data.data ? data.data.length : 0}`);
        
        if (data.data && data.data.length > 0) {
            console.log('   Primo porto:', data.data[0]);
        }
        
        return data;
    } catch (error) {
        console.error('   ❌ Errore API:', error);
        return null;
    }
}

// Test 3: Popolamento manuale
async function debugPopolamentoManuale() {
    console.log('\n🔧 TEST 3: POPOLAMENTO MANUALE');
    
    const apiData = await debugAPI();
    if (!apiData || !apiData.success) {
        console.log('   ❌ API non funziona, skip test');
        return;
    }
    
    const portoArrivo = document.getElementById('porto_arrivo');
    if (portoArrivo) {
        console.log('   🔧 Popolamento manuale porto_arrivo...');
        
        // Salva HTML originale
        const originalHTML = portoArrivo.innerHTML;
        
        // Popola manualmente
        portoArrivo.innerHTML = '<option value="">MANUALE - Seleziona porto...</option>';
        apiData.data.forEach(porto => {
            const option = document.createElement('option');
            option.value = porto.id_cod;
            option.textContent = `MANUALE - ${porto.porto}`;
            portoArrivo.appendChild(option);
        });
        
        console.log(`   ✅ Popolato con ${portoArrivo.options.length} opzioni`);
        
        // Ripristina dopo 5 secondi
        setTimeout(() => {
            portoArrivo.innerHTML = originalHTML;
            console.log('   🔄 HTML originale ripristinato');
        }, 5000);
    } else {
        console.log('   ❌ Elemento porto_arrivo non trovato');
    }
}

// Test 4: Verifica funzioni globali
function debugFunzioniGlobali() {
    console.log('\n🔍 TEST 4: FUNZIONI GLOBALI');
    
    const functions = ['popolaSelectPorti', 'caricaPortiAtlas'];
    functions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`   ✅ ${funcName} è disponibile globalmente`);
        } else {
            console.log(`   ❌ ${funcName} NON è disponibile globalmente`);
        }
    });
}

// Esegui tutti i test
async function eseguiTuttiITest() {
    console.log('🚀 ESECUZIONE COMPLETA DEBUG PORTI');
    
    debugElementiDOM();
    await debugAPI();
    debugFunzioniGlobali();
    
    // Test popolamento manuale dopo 2 secondi
    setTimeout(debugPopolamentoManuale, 2000);
}

// Funzione globale per test manuale
window.debugPorti = eseguiTuttiITest;

// Esegui automaticamente dopo 1 secondo
setTimeout(eseguiTuttiITest, 1000);

console.log('✅ Script debug caricato. Usa debugPorti() per test manuale.');
</script>
