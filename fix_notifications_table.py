#!/usr/bin/env python3
"""
Script per aggiungere la colonna send_email mancante alla tabella DEPARTMENT_NOTIFICATIONS
"""

import psycopg2
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_send_email_column():
    """Aggiunge la colonna send_email alla tabella DEPARTMENT_NOTIFICATIONS"""
    try:
        # Connessione al database
        conn = psycopg2.connect(
            host='localhost',
            database='AGENTE',
            user='re77',
            password='271077',
            connect_timeout=5
        )
        conn.autocommit = False
        cursor = conn.cursor()
        
        logger.info("✅ Connesso al database")
        
        # Verifica se la colonna esiste già
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'DEPARTMENT_NOTIFICATIONS' 
            AND column_name = 'send_email';
        """)
        
        column_exists = cursor.fetchone()
        
        if column_exists:
            logger.info("ℹ️ La colonna send_email esiste già")
            return True
        
        logger.info("🔧 Aggiunta colonna send_email alla tabella DEPARTMENT_NOTIFICATIONS...")
        
        # Aggiungi la colonna send_email
        cursor.execute("""
            ALTER TABLE "DEPARTMENT_NOTIFICATIONS" 
            ADD COLUMN send_email BOOLEAN DEFAULT FALSE;
        """)
        
        # Crea indice per performance
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_dept_notifications_send_email 
            ON "DEPARTMENT_NOTIFICATIONS"(send_email);
        """)
        
        # Commit delle modifiche
        conn.commit()
        
        logger.info("✅ Colonna send_email aggiunta con successo!")
        
        # Verifica finale
        cursor.execute("""
            SELECT column_name, data_type, column_default, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'DEPARTMENT_NOTIFICATIONS' 
            AND column_name = 'send_email';
        """)
        
        column_info = cursor.fetchone()
        if column_info:
            name, data_type, default, nullable = column_info
            logger.info(f"📋 Colonna creata: {name} ({data_type}, default: {default}, nullable: {nullable})")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore durante l'aggiunta della colonna: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
        
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def test_notification_creation():
    """Testa la creazione di una notifica con la nuova colonna"""
    try:
        # Connessione al database
        conn = psycopg2.connect(
            host='localhost',
            database='AGENTE',
            user='re77',
            password='271077',
            connect_timeout=5
        )
        conn.autocommit = False
        cursor = conn.cursor()
        
        logger.info("🧪 Test creazione notifica...")
        
        # Prova a inserire una notifica di test
        cursor.execute("""
            INSERT INTO "DEPARTMENT_NOTIFICATIONS"
            (title, message, notification_type, target_reparto, created_by, priority, send_email, is_active)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            RETURNING id;
        """, (
            "Test Notifica",
            "Questa è una notifica di test per verificare la colonna send_email",
            "INFO",
            "OPERATIVO",
            1,
            1,
            False,
            True
        ))
        
        notification_id = cursor.fetchone()[0]
        logger.info(f"✅ Notifica di test creata con ID: {notification_id}")
        
        # Rimuovi la notifica di test
        cursor.execute("DELETE FROM \"DEPARTMENT_NOTIFICATIONS\" WHERE id = %s", (notification_id,))
        
        conn.commit()
        logger.info("✅ Test completato con successo!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore durante il test: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
        
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    logger.info("🚀 CORREZIONE TABELLA DEPARTMENT_NOTIFICATIONS")
    logger.info("=" * 50)
    
    success = add_send_email_column()
    
    if success:
        # Testa la creazione di notifiche
        test_success = test_notification_creation()
        
        if test_success:
            logger.info("\n🎉 OPERAZIONE COMPLETATA CON SUCCESSO!")
            logger.info("La colonna send_email è stata aggiunta e testata.")
            logger.info("Le notifiche di ripristino SOF ora funzioneranno correttamente.")
        else:
            logger.error("\n⚠️ COLONNA AGGIUNTA MA TEST FALLITO!")
    else:
        logger.error("\n❌ OPERAZIONE FALLITA!")
        logger.error("Controlla i log per i dettagli dell'errore.")
    
    exit(0 if success else 1)
