#!/usr/bin/env python3
"""
Script per aggiornare i valori Agemar_GioiaTauro dal file Excel gioia12.xlsx
"""

import pandas as pd
from sqlalchemy import text
from database import SessionLocal
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def update_agemar_from_excel():
    """Aggiorna i valori Agemar_GioiaTauro dal file Excel"""
    
    file_path = r"c:\Users\<USER>\Desktop\cline_thebest\gioia12.xlsx"
    db = SessionLocal()
    
    try:
        print("🚢 AGGIORNAMENTO AGEMAR_GIOIATAURO DA EXCEL")
        print("=" * 60)
        
        # Leggi il foglio "gioia" dal file Excel
        print(f"📖 Lettura file Excel: {file_path}")
        df = pd.read_excel(file_path, sheet_name='gioia')
        
        print(f"📊 Trovate {len(df)} righe nel foglio 'gioia'")
        
        # Rinomina le colonne per facilità d'uso
        df.columns = ['nome_nave', 'agemar_value']
        
        # Pulisci i nomi delle navi (rimuovi spazi extra)
        df['nome_nave'] = df['nome_nave'].str.strip()
        
        print(f"\n📋 Prime 5 righe del file:")
        for i in range(min(5, len(df))):
            row = df.iloc[i]
            print(f"   {i+1}. '{row['nome_nave']}' -> {row['agemar_value']}")
        
        # Contatori per statistiche
        updated_count = 0
        not_found_count = 0
        error_count = 0
        
        print(f"\n🔄 Inizio aggiornamento database...")
        
        # Per ogni riga nel file Excel
        for index, row in df.iterrows():
            nome_nave = row['nome_nave']
            agemar_value = row['agemar_value']
            
            try:
                # Cerca la nave nel database
                result = db.execute(text('''
                    SELECT "Nave" FROM "NAVI" 
                    WHERE "Nave" = :nome_nave
                '''), {'nome_nave': nome_nave})
                
                nave_trovata = result.fetchone()
                
                if nave_trovata:
                    # Aggiorna il valore Agemar_GioiaTauro
                    db.execute(text('''
                        UPDATE "NAVI" 
                        SET "Agemar_GioiaTauro" = :agemar_value
                        WHERE "Nave" = :nome_nave
                    '''), {
                        'agemar_value': agemar_value,
                        'nome_nave': nome_nave
                    })
                    
                    updated_count += 1
                    print(f"   ✅ {nome_nave}: {agemar_value}")
                    
                else:
                    not_found_count += 1
                    print(f"   ⚠️ NAVE NON TROVATA: '{nome_nave}'")
                    
            except Exception as e:
                error_count += 1
                print(f"   ❌ ERRORE per '{nome_nave}': {e}")
        
        # Commit delle modifiche
        db.commit()
        
        print(f"\n📈 RISULTATI AGGIORNAMENTO:")
        print(f"   ✅ Navi aggiornate: {updated_count}")
        print(f"   ⚠️ Navi non trovate: {not_found_count}")
        print(f"   ❌ Errori: {error_count}")
        print(f"   📊 Totale processate: {len(df)}")
        
        if updated_count > 0:
            print(f"\n🎉 Aggiornamento completato con successo!")
            print(f"   {updated_count} navi hanno ora i nuovi valori Agemar_GioiaTauro")
        
        # Mostra alcune navi non trovate per debug
        if not_found_count > 0:
            print(f"\n🔍 NAVI NON TROVATE (prime 10):")
            not_found_ships = []
            for index, row in df.iterrows():
                nome_nave = row['nome_nave']
                result = db.execute(text('''
                    SELECT "Nave" FROM "NAVI" 
                    WHERE "Nave" = :nome_nave
                '''), {'nome_nave': nome_nave})
                
                if not result.fetchone():
                    not_found_ships.append(nome_nave)
                    if len(not_found_ships) >= 10:
                        break
            
            for ship in not_found_ships:
                print(f"   • '{ship}'")
        
        return updated_count > 0
        
    except Exception as e:
        logger.error(f"Errore aggiornamento: {e}")
        db.rollback()
        return False
        
    finally:
        db.close()

if __name__ == "__main__":
    update_agemar_from_excel()
