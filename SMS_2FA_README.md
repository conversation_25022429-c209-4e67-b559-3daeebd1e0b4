# Sistema 2FA SMS per Avvio Applicazione

## Descrizione
Il sistema implementa un'autenticazione a due fattori (2FA) tramite SMS per l'avvio dell'applicazione SNIP. 

## Funzionalità
- ✅ Generazione automatica di codice a 6 cifre
- ✅ Invio SMS al numero amministratore configurato
- ✅ Verifica del codice con massimo 3 tentativi
- ✅ Modalità di emergenza se l'SMS non viene inviato
- ✅ Supporto per diversi provider SMS
- ✅ Logging completo delle operazioni

## Provider SMS Supportati

### 1. TextBelt (Gratuito - Limitato)
- **Pro**: Gratuito, facile da configurare
- **Contro**: Limitato a pochi SMS al giorno
- **Configurazione**: Nessuna (funziona out-of-the-box)

### 2. <PERSON><PERSON><PERSON> (Professionale)
- **Pro**: Affidabile, scalabile, supporto globale
- **Contro**: A pagamento
- **Configurazione**: Richiede account Twilio e credenziali

### 3. SMS.it (Provider Italiano)
- **Pro**: Provider italiano, buoni prezzi
- **Contro**: A pagamento
- **Configurazione**: Richiede account SMS.it e API key

## Configurazione

### 1. Configurazione Base
Il sistema funziona immediatamente con TextBelt (gratuito ma limitato).

### 2. Configurazione Avanzata
1. Copia `.env.example` in `.env`
2. Configura le variabili d'ambiente per il tuo provider preferito

### 3. Esempio configurazione Twilio
```bash
SMS_PROVIDER=twilio
ADMIN_PHONE=+************
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_FROM_NUMBER=+**********
```

## Come Funziona

1. **Avvio**: Quando avvii l'applicazione con `python main.py`
2. **Generazione**: Il sistema genera un codice a 6 cifre
3. **Invio SMS**: Il codice viene inviato al numero configurato
4. **Verifica**: Ti viene richiesto di inserire il codice
5. **Accesso**: Se il codice è corretto, l'applicazione si avvia

## Modalità di Emergenza

Se l'SMS non può essere inviato:
- Il sistema mostra il codice nei log
- Puoi usare questo codice per accedere
- Viene registrato un warning nei log

## Sicurezza

- ✅ Codice valido solo per una sessione
- ✅ Massimo 3 tentativi di inserimento
- ✅ Logging completo di tutti i tentativi
- ✅ Timeout automatico per l'invio SMS
- ✅ Gestione errori robusta

## Personalizzazione

### Cambiare il numero di telefono
Modifica `ADMIN_PHONE` nel file `.env` o in `sms_config.py`

### Cambiare il numero di tentativi
Modifica `MAX_ATTEMPTS` nella funzione `verify_startup_code()`

### Personalizzare i messaggi
Modifica il dizionario `MESSAGES` in `sms_config.py`

## Troubleshooting

### SMS non ricevuto
1. Verifica la connessione internet
2. Controlla il numero di telefono (formato internazionale)
3. Usa la modalità di emergenza con il codice dai log

### Errori di configurazione
1. Verifica le credenziali del provider SMS
2. Controlla il file `.env`
3. Consulta i log per dettagli specifici

### Provider non funziona
1. Prova con TextBelt (gratuito)
2. Verifica le credenziali del provider
3. Controlla la documentazione del provider

## Log e Monitoraggio

Tutti gli eventi sono registrati in `snip.log`:
- Generazione codici
- Invio SMS (successo/errore)
- Tentativi di verifica
- Accessi autorizzati/negati

## Disabilitare il Sistema 2FA

Per disabilitare temporaneamente il sistema, commenta la chiamata a `verify_startup_code()` in `main.py`:

```python
# if not verify_startup_code():
#     print("\n🛑 Avvio applicazione interrotto per motivi di sicurezza.")
#     exit(1)
```

**⚠️ ATTENZIONE**: Disabilitare il 2FA riduce la sicurezza dell'applicazione.
