#!/usr/bin/env python3

import psycopg2

def check_users():
    print("👥 CONTROLLO UTENTI NEL DATABASE")
    print("=" * 40)
    
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='AGENTE',
            user='re77',
            password='271077',
            connect_timeout=3
        )
        cursor = conn.cursor()
        
        # Lista tutti gli utenti
        cursor.execute("""
            SELECT id_user, "Nome", "Cognome", email, ruolo, reparto, visibile
            FROM "AGENTE"
            ORDER BY id_user
        """)
        
        users = cursor.fetchall()
        
        print(f"✅ Trovati {len(users)} utenti:")
        print("-" * 40)
        
        for user in users:
            id_user, nome, cognome, email, ruolo, reparto, visibile = user
            print(f"ID: {id_user}")
            print(f"   Nome: {nome} {cognome}")
            print(f"   Email: {email}")
            print(f"   Ruolo: {ruolo}")
            print(f"   Reparto: {reparto}")
            print(f"   Visibile: {visibile}")
            print("-" * 40)
        
        conn.close()
        
        # Suggerisci credenziali per test
        if users:
            print("\n💡 CREDENZIALI SUGGERITE PER TEST:")
            for user in users[:3]:  # Prime 3
                email = user[3]
                print(f"   Username: {email}")
                print(f"   Password: [prova password comuni: admin123, test123, password]")
        
        return users
        
    except Exception as e:
        print(f"❌ Errore: {e}")
        return []

if __name__ == "__main__":
    check_users()
