#!/usr/bin/env python3
"""
Diagnostica i problemi di auto-reload di uvicorn
Identifica perché il riavvio dell'app non funziona correttamente
"""

import os
import sys
import time
import psutil
import logging
import subprocess
from pathlib import Path
from datetime import datetime

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ReloadDiagnostic:
    """Classe per diagnosticare problemi di reload"""
    
    def __init__(self):
        self.issues_found = []
        self.recommendations = []
        
    def add_issue(self, issue, recommendation=None):
        """Aggiunge un problema trovato"""
        self.issues_found.append(issue)
        if recommendation:
            self.recommendations.append(recommendation)
    
    def check_uvicorn_processes(self):
        """Verifica processi uvicorn attivi"""
        logger.info("🔍 Controllo processi uvicorn...")
        
        uvicorn_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                proc_info = proc.info
                if proc_info['name'] and 'uvicorn' in proc_info['name'].lower():
                    uvicorn_processes.append(proc_info)
                elif proc_info['cmdline'] and any('uvicorn' in str(arg) for arg in proc_info['cmdline']):
                    uvicorn_processes.append(proc_info)
            except (psutil.NoSuchProcess, psutil.AccessDenied, TypeError):
                continue
        
        if len(uvicorn_processes) == 0:
            self.add_issue(
                "❌ Nessun processo uvicorn attivo",
                "Avvia il server con: uvicorn main:app --reload"
            )
        elif len(uvicorn_processes) > 1:
            self.add_issue(
                f"⚠️ Multipli processi uvicorn attivi ({len(uvicorn_processes)})",
                "Termina i processi duplicati per evitare conflitti"
            )
        else:
            logger.info(f"✅ Processo uvicorn attivo: PID {uvicorn_processes[0]['pid']}")
            
        return uvicorn_processes
    
    def check_file_permissions(self):
        """Verifica permessi sui file"""
        logger.info("🔍 Controllo permessi file...")
        
        critical_files = ['main.py', 'models.py', 'database.py']
        
        for filename in critical_files:
            if os.path.exists(filename):
                file_path = Path(filename)
                try:
                    # Test lettura
                    with open(file_path, 'r') as f:
                        f.read(1)
                    
                    # Test scrittura (crea file temporaneo)
                    test_file = file_path.with_suffix('.tmp')
                    with open(test_file, 'w') as f:
                        f.write('test')
                    test_file.unlink()
                    
                    logger.info(f"✅ Permessi OK per: {filename}")
                    
                except PermissionError:
                    self.add_issue(
                        f"❌ Permessi insufficienti per: {filename}",
                        f"Verifica i permessi di lettura/scrittura per {filename}"
                    )
                except Exception as e:
                    self.add_issue(
                        f"⚠️ Problema accesso file {filename}: {e}",
                        f"Verifica l'integrità del file {filename}"
                    )
    
    def check_file_locks(self):
        """Verifica se ci sono file bloccati"""
        logger.info("🔍 Controllo file bloccati...")
        
        python_files = list(Path('.').glob('*.py'))
        locked_files = []
        
        for file_path in python_files:
            try:
                # Prova ad aprire in modalità esclusiva
                with open(file_path, 'r+') as f:
                    pass
            except PermissionError:
                locked_files.append(str(file_path))
            except Exception:
                pass  # Altri errori non sono necessariamente lock
        
        if locked_files:
            self.add_issue(
                f"⚠️ File potenzialmente bloccati: {', '.join(locked_files)}",
                "Chiudi editor/IDE che potrebbero bloccare i file"
            )
        else:
            logger.info("✅ Nessun file bloccato rilevato")
    
    def check_import_errors(self):
        """Verifica errori di import in main.py"""
        logger.info("🔍 Controllo errori import...")
        
        try:
            # Prova a compilare main.py
            with open('main.py', 'r', encoding='utf-8') as f:
                code = f.read()
            
            compile(code, 'main.py', 'exec')
            logger.info("✅ Sintassi main.py corretta")
            
            # Prova import (senza eseguire)
            import ast
            tree = ast.parse(code)
            
            # Estrai import
            imports = []
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        imports.append(node.module)
            
            logger.info(f"✅ Import trovati: {len(imports)}")
            
        except SyntaxError as e:
            self.add_issue(
                f"❌ Errore sintassi in main.py: {e}",
                f"Correggi l'errore alla riga {e.lineno}"
            )
        except Exception as e:
            self.add_issue(
                f"⚠️ Problema analisi main.py: {e}",
                "Verifica l'integrità del file main.py"
            )
    
    def check_watchdog_functionality(self):
        """Verifica funzionalità watchdog"""
        logger.info("🔍 Controllo watchdog...")
        
        try:
            from watchdog.observers import Observer
            from watchdog.events import FileSystemEventHandler
            
            # Test rapido watchdog
            observer = Observer()
            handler = FileSystemEventHandler()
            observer.schedule(handler, '.', recursive=False)
            observer.start()
            time.sleep(0.1)
            observer.stop()
            observer.join()
            
            logger.info("✅ Watchdog funzionante")
            
        except ImportError:
            self.add_issue(
                "⚠️ Watchdog non installato",
                "Installa con: pip install watchdog (opzionale ma consigliato)"
            )
        except Exception as e:
            self.add_issue(
                f"⚠️ Problema watchdog: {e}",
                "Verifica installazione watchdog"
            )
    
    def check_system_resources(self):
        """Verifica risorse sistema"""
        logger.info("🔍 Controllo risorse sistema...")
        
        # CPU
        cpu_percent = psutil.cpu_percent(interval=1)
        if cpu_percent > 80:
            self.add_issue(
                f"⚠️ CPU alta: {cpu_percent}%",
                "Sistema sotto stress, potrebbe rallentare il reload"
            )
        
        # Memoria
        memory = psutil.virtual_memory()
        if memory.percent > 85:
            self.add_issue(
                f"⚠️ Memoria alta: {memory.percent}%",
                "Memoria insufficiente, potrebbe causare problemi"
            )
        
        # Disco
        disk = psutil.disk_usage('.')
        if disk.percent > 90:
            self.add_issue(
                f"⚠️ Disco pieno: {disk.percent}%",
                "Spazio disco insufficiente"
            )
        
        logger.info(f"✅ Risorse: CPU {cpu_percent}%, RAM {memory.percent}%, Disco {disk.percent}%")
    
    def test_reload_mechanism(self):
        """Test del meccanismo di reload"""
        logger.info("🔍 Test meccanismo reload...")
        
        # Crea file di test
        test_file = Path('test_reload.py')
        try:
            with open(test_file, 'w') as f:
                f.write('# Test file for reload\nprint("Test reload")\n')
            
            logger.info("✅ Creazione file test OK")
            
            # Modifica file
            time.sleep(0.1)
            with open(test_file, 'a') as f:
                f.write(f'# Modified at {datetime.now()}\n')
            
            logger.info("✅ Modifica file test OK")
            
        except Exception as e:
            self.add_issue(
                f"❌ Errore test reload: {e}",
                "Problema con il file system o permessi"
            )
        finally:
            # Cleanup
            if test_file.exists():
                test_file.unlink()
    
    def run_diagnosis(self):
        """Esegue diagnosi completa"""
        logger.info("🔬 AVVIO DIAGNOSI RELOAD")
        logger.info("=" * 50)
        
        # Esegui tutti i controlli
        self.check_uvicorn_processes()
        self.check_file_permissions()
        self.check_file_locks()
        self.check_import_errors()
        self.check_watchdog_functionality()
        self.check_system_resources()
        self.test_reload_mechanism()
        
        # Report finale
        self.print_report()
    
    def print_report(self):
        """Stampa report finale"""
        logger.info("=" * 50)
        logger.info("📋 REPORT DIAGNOSI")
        logger.info("=" * 50)
        
        if not self.issues_found:
            logger.info("🎉 NESSUN PROBLEMA RILEVATO!")
            logger.info("Il sistema dovrebbe funzionare correttamente.")
        else:
            logger.info(f"⚠️ PROBLEMI TROVATI: {len(self.issues_found)}")
            logger.info("")
            
            for i, issue in enumerate(self.issues_found, 1):
                logger.info(f"{i}. {issue}")
            
            if self.recommendations:
                logger.info("")
                logger.info("💡 RACCOMANDAZIONI:")
                for i, rec in enumerate(self.recommendations, 1):
                    logger.info(f"{i}. {rec}")
        
        logger.info("=" * 50)

def main():
    """Funzione principale"""
    print("🔬 DIAGNOSI PROBLEMI RELOAD UVICORN")
    print("=" * 50)
    print("Identifica problemi con l'auto-reload di FastAPI")
    print("=" * 50)
    
    diagnostic = ReloadDiagnostic()
    diagnostic.run_diagnosis()
    
    return len(diagnostic.issues_found) == 0

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Diagnosi interrotta dall'utente")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Errore durante diagnosi: {e}")
        sys.exit(1)
