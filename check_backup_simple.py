#!/usr/bin/env python3
"""
Check backup content semplice
"""

import gzip
from pathlib import Path

def main():
    print("CHECK BACKUP CONTENT")
    print("=" * 40)
    
    # Trova ultimo backup
    backup_dir = Path("backups")
    backup_files = list(backup_dir.glob("snip_backup_*.sql.gz"))
    
    if not backup_files:
        print("Nessun backup trovato")
        return
    
    latest_backup = max(backup_files, key=lambda f: f.stat().st_mtime)
    print(f"File: {latest_backup.name}")
    print(f"Dimensione: {latest_backup.stat().st_size} bytes")
    
    try:
        with gzip.open(latest_backup, 'rt', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        print(f"Righe totali: {len(lines)}")
        
        # Prime 30 righe
        print("\nPrime 30 righe:")
        print("-" * 40)
        for i, line in enumerate(lines[:30], 1):
            # Rimuovi caratteri problematici
            clean_line = line.encode('ascii', 'ignore').decode('ascii')
            print(f"{i:2d}: {clean_line}")
        
        # Statistiche
        print(f"\nStatistiche:")
        print(f"- PGDMP header: {'PGDMP' in content}")
        print(f"- COPY statements: {content.count('COPY public.')}")
        print(f"- Tabelle: {content.count('Data for Name:')}")
        print(f"- AGENTE data: {'AGENTE' in content}")
        print(f"- NAVI data: {'NAVI' in content}")
        
        # Cerca sezioni specifiche
        if 'COPY public."AGENTE"' in content:
            print("- Backup AGENTE: OK")
        if 'COPY public."NAVI"' in content:
            print("- Backup NAVI: OK")
        if 'COPY public."SYSTEM_CONFIG"' in content:
            print("- Backup SYSTEM_CONFIG: OK")
            
        print("\nBACKUP FORMATO PG_DUMP: CREATO CON SUCCESSO")
        
    except Exception as e:
        print(f"Errore: {e}")

if __name__ == "__main__":
    main()
