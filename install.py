#!/usr/bin/env python3
"""
Script di installazione automatica per SNIP
Sistema Navale Integrato Portuale
"""

import os
import sys
import subprocess
import platform

def print_header():
    """Stampa l'header dell'installazione"""
    print("=" * 60)
    print("🚢 SNIP - Sistema Navale Integrato Portuale")
    print("📦 Script di Installazione Automatica")
    print("=" * 60)
    print()

def check_python_version():
    """Verifica la versione di Python"""
    print("🔍 Controllo versione Python...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python {version.major}.{version.minor} non supportato")
        print("✅ Richiesto Python 3.8 o superiore")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - OK")
    return True

def check_pip():
    """Verifica che pip sia installato"""
    print("🔍 Controllo pip...")
    
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        print("✅ pip - OK")
        return True
    except subprocess.CalledProcessError:
        print("❌ pip non trovato")
        return False

def create_virtual_environment():
    """Crea virtual environment"""
    print("🔧 Creazione virtual environment...")
    
    venv_path = "venv"
    if os.path.exists(venv_path):
        print(f"⚠️ Virtual environment '{venv_path}' già esistente")
        response = input("Vuoi ricrearlo? (y/N): ").lower()
        if response == 'y':
            import shutil
            shutil.rmtree(venv_path)
        else:
            print("✅ Uso virtual environment esistente")
            return True
    
    try:
        subprocess.run([sys.executable, "-m", "venv", venv_path], check=True)
        print(f"✅ Virtual environment creato: {venv_path}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Errore creazione virtual environment: {e}")
        return False

def get_pip_executable():
    """Ottiene il percorso dell'eseguibile pip nel virtual environment"""
    system = platform.system()
    if system == "Windows":
        return os.path.join("venv", "Scripts", "pip.exe")
    else:
        return os.path.join("venv", "bin", "pip")

def install_requirements():
    """Installa le dipendenze dal requirements.txt"""
    print("📦 Installazione dipendenze...")
    
    if not os.path.exists("requirements.txt"):
        print("❌ File requirements.txt non trovato")
        return False
    
    pip_executable = get_pip_executable()
    
    try:
        # Aggiorna pip
        print("🔄 Aggiornamento pip...")
        subprocess.run([pip_executable, "install", "--upgrade", "pip"], check=True)
        
        # Installa dipendenze
        print("📥 Installazione dipendenze da requirements.txt...")
        subprocess.run([pip_executable, "install", "-r", "requirements.txt"], check=True)
        
        print("✅ Dipendenze installate con successo")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Errore installazione dipendenze: {e}")
        return False

def check_database_config():
    """Controlla la configurazione del database"""
    print("🗄️ Controllo configurazione database...")
    
    if not os.path.exists("config.py"):
        print("⚠️ File config.py non trovato")
        print("💡 Assicurati di configurare il database PostgreSQL")
        return True
    
    try:
        with open("config.py", "r") as f:
            content = f.read()
            if "postgresql://" in content:
                print("✅ Configurazione database trovata")
                return True
            else:
                print("⚠️ Configurazione database non trovata in config.py")
                return True
    except Exception as e:
        print(f"⚠️ Errore lettura config.py: {e}")
        return True

def create_directories():
    """Crea le directory necessarie"""
    print("📁 Creazione directory necessarie...")
    
    directories = [
        "sof_documents",
        "static/uploads",
        "logs"
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory, exist_ok=True)
                print(f"✅ Directory creata: {directory}")
            except Exception as e:
                print(f"⚠️ Errore creazione directory {directory}: {e}")
        else:
            print(f"✅ Directory già esistente: {directory}")

def print_next_steps():
    """Stampa i prossimi passi"""
    print()
    print("🎉 INSTALLAZIONE COMPLETATA!")
    print("=" * 60)
    print()
    print("📋 PROSSIMI PASSI:")
    print()
    print("1️⃣ Attiva il virtual environment:")
    
    system = platform.system()
    if system == "Windows":
        print("   venv\\Scripts\\activate")
    else:
        print("   source venv/bin/activate")
    
    print()
    print("2️⃣ Configura il database PostgreSQL:")
    print("   - Crea database 'AGENTE'")
    print("   - Verifica credenziali in config.py")
    print()
    print("3️⃣ Avvia l'applicazione:")
    print("   uvicorn main:app --reload --host 0.0.0.0 --port 8002")
    print()
    print("4️⃣ Apri il browser:")
    print("   http://localhost:8002")
    print()
    print("📚 Per maggiori informazioni, consulta README.md")
    print("=" * 60)

def main():
    """Funzione principale"""
    print_header()
    
    # Controlli preliminari
    if not check_python_version():
        sys.exit(1)
    
    if not check_pip():
        sys.exit(1)
    
    # Installazione
    if not create_virtual_environment():
        sys.exit(1)
    
    if not install_requirements():
        sys.exit(1)
    
    # Configurazione
    check_database_config()
    create_directories()
    
    # Completamento
    print_next_steps()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ Installazione interrotta dall'utente")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Errore durante l'installazione: {e}")
        sys.exit(1)
