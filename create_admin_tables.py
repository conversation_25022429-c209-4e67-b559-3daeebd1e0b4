#!/usr/bin/env python3
"""
Script per creare le tabelle del sistema di amministrazione SNIP
"""

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from models import Base, SystemConfig, AuditLog, UserSession, SystemStats
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_admin_tables():
    """Crea le tabelle per il sistema di amministrazione"""
    
    # Connessione al database
    DATABASE_URL = "postgresql://re77:271077@localhost:5432/AGENTE"
    engine = create_engine(DATABASE_URL)
    
    try:
        logger.info("🚀 Creazione tabelle sistema amministrazione...")
        
        # Crea le tabelle
        Base.metadata.create_all(bind=engine, tables=[
            SystemConfig.__table__,
            AuditLog.__table__,
            UserSession.__table__,
            SystemStats.__table__
        ])
        
        logger.info("✅ Tabelle create con successo!")
        
        # Inserisci configurazioni di default
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        try:
            logger.info("📋 Inserimento configurazioni di default...")
            
            default_configs = [
                {
                    "config_key": "system_name",
                    "config_value": "SNIP - Sistema Navale Integrato Portuale",
                    "description": "Nome del sistema",
                    "config_type": "string"
                },
                {
                    "config_key": "system_version",
                    "config_value": "2.0.0",
                    "description": "Versione del sistema",
                    "config_type": "string"
                },
                {
                    "config_key": "max_sessions_per_user",
                    "config_value": "5",
                    "description": "Numero massimo di sessioni simultanee per utente",
                    "config_type": "integer"
                },
                {
                    "config_key": "session_timeout_hours",
                    "config_value": "8",
                    "description": "Timeout sessioni in ore",
                    "config_type": "integer"
                },
                {
                    "config_key": "audit_retention_days",
                    "config_value": "90",
                    "description": "Giorni di conservazione log audit",
                    "config_type": "integer"
                },
                {
                    "config_key": "auto_cleanup_enabled",
                    "config_value": "true",
                    "description": "Abilita pulizia automatica dati vecchi",
                    "config_type": "boolean"
                },
                {
                    "config_key": "maintenance_mode",
                    "config_value": "false",
                    "description": "Modalità manutenzione sistema",
                    "config_type": "boolean"
                },
                {
                    "config_key": "sof_template_path",
                    "config_value": "/templates/sof/",
                    "description": "Percorso template SOF",
                    "config_type": "string"
                },
                {
                    "config_key": "max_file_upload_mb",
                    "config_value": "50",
                    "description": "Dimensione massima upload file in MB",
                    "config_type": "integer"
                },
                {
                    "config_key": "email_notifications",
                    "config_value": "false",
                    "description": "Abilita notifiche email",
                    "config_type": "boolean"
                }
            ]
            
            for config_data in default_configs:
                # Verifica se la configurazione esiste già
                existing = db.query(SystemConfig).filter(
                    SystemConfig.config_key == config_data["config_key"]
                ).first()
                
                if not existing:
                    config = SystemConfig(**config_data)
                    db.add(config)
                    logger.info(f"  ➕ Aggiunta configurazione: {config_data['config_key']}")
                else:
                    logger.info(f"  ⏭️ Configurazione già esistente: {config_data['config_key']}")
            
            db.commit()
            logger.info("✅ Configurazioni di default inserite!")
            
        except Exception as e:
            db.rollback()
            logger.error(f"❌ Errore inserimento configurazioni: {str(e)}")
            raise
        finally:
            db.close()
        
        # Verifica tabelle create
        logger.info("🔍 Verifica tabelle create...")
        
        with engine.connect() as conn:
            # Verifica SYSTEM_CONFIG
            result = conn.execute(text("SELECT COUNT(*) FROM \"SYSTEM_CONFIG\""))
            config_count = result.scalar()
            logger.info(f"  📊 SYSTEM_CONFIG: {config_count} record")
            
            # Verifica AUDIT_LOG
            result = conn.execute(text("SELECT COUNT(*) FROM \"AUDIT_LOG\""))
            audit_count = result.scalar()
            logger.info(f"  📋 AUDIT_LOG: {audit_count} record")
            
            # Verifica USER_SESSIONS
            result = conn.execute(text("SELECT COUNT(*) FROM \"USER_SESSIONS\""))
            sessions_count = result.scalar()
            logger.info(f"  🔐 USER_SESSIONS: {sessions_count} record")
            
            # Verifica SYSTEM_STATS
            result = conn.execute(text("SELECT COUNT(*) FROM \"SYSTEM_STATS\""))
            stats_count = result.scalar()
            logger.info(f"  📈 SYSTEM_STATS: {stats_count} record")
        
        logger.info("🎉 Sistema amministrazione configurato con successo!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore creazione tabelle: {str(e)}")
        return False

def create_admin_user():
    """Crea un utente amministratore di default"""
    
    DATABASE_URL = "postgresql://re77:271077@localhost:5432/AGENTE"
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        from models import Agente, RuoloEnum, RepartoEnum
        from passlib.context import CryptContext
        
        pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        logger.info("👤 Creazione utente amministratore...")
        
        # Verifica se esiste già un admin
        existing_admin = db.query(Agente).filter(
            Agente.ruolo == RuoloEnum.SUPER_ADMIN
        ).first()
        
        if existing_admin:
            logger.info(f"  ⏭️ Utente SUPER_ADMIN già esistente: {existing_admin.email}")
            return True
        
        # Crea utente admin
        admin_password = "admin123"  # Password di default - CAMBIARE IN PRODUZIONE!
        hashed_password = pwd_context.hash(admin_password)
        
        admin_user = Agente(
            Nome="Admin",
            Cognome="Sistema",
            email="<EMAIL>",
            password=hashed_password,
            reparto=RepartoEnum.AMMINISTRAZIONE,
            ruolo=RuoloEnum.SUPER_ADMIN,
            visibile="si"
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        logger.info("✅ Utente amministratore creato!")
        logger.info(f"  📧 Email: <EMAIL>")
        logger.info(f"  🔑 Password: {admin_password}")
        logger.warning("⚠️ CAMBIARE LA PASSWORD DI DEFAULT IN PRODUZIONE!")
        
        return True
        
    except Exception as e:
        db.rollback()
        logger.error(f"❌ Errore creazione utente admin: {str(e)}")
        return False
    finally:
        db.close()

def verify_admin_system():
    """Verifica che il sistema di amministrazione sia configurato correttamente"""
    
    DATABASE_URL = "postgresql://re77:271077@localhost:5432/AGENTE"
    engine = create_engine(DATABASE_URL)
    
    try:
        logger.info("🔍 Verifica sistema amministrazione...")
        
        with engine.connect() as conn:
            # Verifica tabelle esistenti
            tables_query = text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name IN ('SYSTEM_CONFIG', 'AUDIT_LOG', 'USER_SESSIONS', 'SYSTEM_STATS')
                ORDER BY table_name
            """)
            
            tables = conn.execute(tables_query).fetchall()
            table_names = [table[0] for table in tables]
            
            expected_tables = ['AUDIT_LOG', 'SYSTEM_CONFIG', 'SYSTEM_STATS', 'USER_SESSIONS']
            missing_tables = [t for t in expected_tables if t not in table_names]
            
            if missing_tables:
                logger.error(f"❌ Tabelle mancanti: {missing_tables}")
                return False
            
            logger.info("✅ Tutte le tabelle amministrazione presenti")
            
            # Verifica configurazioni
            config_count = conn.execute(text("SELECT COUNT(*) FROM \"SYSTEM_CONFIG\"")).scalar()
            if config_count == 0:
                logger.warning("⚠️ Nessuna configurazione di sistema trovata")
            else:
                logger.info(f"✅ {config_count} configurazioni di sistema presenti")
            
            # Verifica utenti admin
            admin_count = conn.execute(text("""
                SELECT COUNT(*) FROM "AGENTE" 
                WHERE ruolo IN ('ADMIN', 'SUPER_ADMIN') AND visibile = 'si'
            """)).scalar()
            
            if admin_count == 0:
                logger.warning("⚠️ Nessun utente amministratore trovato")
            else:
                logger.info(f"✅ {admin_count} utenti amministratori presenti")
        
        logger.info("🎉 Sistema amministrazione verificato!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore verifica sistema: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 SETUP SISTEMA AMMINISTRAZIONE SNIP")
    print("=" * 50)
    
    # Crea tabelle
    if create_admin_tables():
        print("✅ Tabelle amministrazione create")
    else:
        print("❌ Errore creazione tabelle")
        exit(1)
    
    # Crea utente admin
    if create_admin_user():
        print("✅ Utente amministratore configurato")
    else:
        print("❌ Errore creazione utente admin")
        exit(1)
    
    # Verifica sistema
    if verify_admin_system():
        print("✅ Sistema amministrazione operativo")
    else:
        print("❌ Problemi nel sistema amministrazione")
        exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 SISTEMA AMMINISTRAZIONE CONFIGURATO!")
    print("🔗 Accedi a: http://localhost:8000/dashboard/amministrazione")
    print("👤 Email: <EMAIL>")
    print("🔑 Password: admin123")
    print("⚠️ CAMBIARE LA PASSWORD DI DEFAULT!")
