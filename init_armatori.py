from sqlalchemy import text
from database import engine, SessionLocal

def init_armatori():
    # Crea una sessione
    db = SessionLocal()

    try:
        # Verifica se la tabella esiste
        result = db.execute(text("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'ARMATORE')"))
        table_exists = result.scalar()

        if not table_exists:
            print("Creazione tabella ARMATORE...")
            # Crea la tabella
            db.execute(text("""
                CREATE TABLE "ARMATORE" (
                    id SERIAL PRIMARY KEY,
                    "Nome_Armatore" VARCHAR
                )
            """))
            db.commit()
            print("Tabella ARMATORE creata con successo!")

            # Inserisci alcuni dati di esempio
            print("Inserimento dati di esempio...")
            db.execute(text("""
                INSERT INTO "ARMATORE" ("Nome_Armatore") VALUES
                ('COSTA CROCIERE'),
                ('MSC CROCIERE'),
                ('GRIMALDI LINES'),
                ('TIRRENIA'),
                ('MOBY LINES'),
                ('GRANDI NAVI VELOCI'),
                ('SNAV'),
                ('CORSICA FERRIES'),
                ('SARDINIA FERRIES'),
                ('CARONTE & TOURIST')
            """))
            db.commit()
            print("Dati di esempio inseriti con successo!")
        else:
            print("Tabella ARMATORE già esistente")

        # Verifica il contenuto della tabella
        result = db.execute(text('SELECT COUNT(*) FROM "ARMATORE"'))
        count = result.scalar()
        print(f"Numero di armatori nella tabella: {count}")

        # Se la tabella è vuota, aggiungi i dati di esempio
        if count == 0:
            print("Inserimento dati di esempio...")
            db.execute(text("""
                INSERT INTO "ARMATORE" ("Nome_Armatore") VALUES
                ('COSTA CROCIERE'),
                ('MSC CROCIERE'),
                ('GRIMALDI LINES'),
                ('TIRRENIA'),
                ('MOBY LINES'),
                ('GRANDI NAVI VELOCI'),
                ('SNAV'),
                ('CORSICA FERRIES'),
                ('SARDINIA FERRIES'),
                ('CARONTE & TOURIST')
            """))
            db.commit()
            print("Dati di esempio inseriti con successo!")

            # Ricontrolla il numero
            result = db.execute(text('SELECT COUNT(*) FROM "ARMATORE"'))
            count = result.scalar()
            print(f"Numero di armatori dopo l'inserimento: {count}")

        # Mostra alcuni esempi
        result = db.execute(text('SELECT * FROM "ARMATORE" LIMIT 5'))
        armatori = result.fetchall()
        print("\nPrimi 5 armatori:")
        for armatore in armatori:
            print(f"- ID: {armatore[0]}, Nome: {armatore[1]}")

        # Mostra la struttura della tabella
        result = db.execute(text("""
            SELECT column_name, data_type
            FROM information_schema.columns
            WHERE table_name = 'ARMATORE'
        """))
        columns = result.fetchall()
        print("\nStruttura tabella ARMATORE:")
        for col in columns:
            print(f"- {col[0]}: {col[1]}")

        print("\nInizializzazione completata!")

    except Exception as e:
        print(f"Errore durante l'inizializzazione: {str(e)}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    init_armatori()
