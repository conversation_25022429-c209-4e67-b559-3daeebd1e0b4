# Fix per Rigenerazione SOF con Dati Aggiornati

## Problema Identificato

Quando si generava un file SOF .docx e successivamente si modificavano orari o dati, se il file .docx era già stato generato, il sistema restituiva il file vecchio con i dati obsoleti invece di rigenerarlo con i dati aggiornati.

## Causa del Problema

Il codice originale controllava solo se esisteva un file SOF salvato per il viaggio e, se presente, lo restituiva senza verificare se i dati sottostanti erano stati modificati dopo la sua creazione.

```python
# Codice problematico originale
if existing_sof and os.path.exists(existing_sof[0]):
    # Restituiva sempre il file esistente, anche se i dati erano cambiati
    return FileResponse(path=existing_sof[0], ...)
```

## Soluzione Implementata

### 1. Tracking delle Modifiche

- **Aggiunta colonne timestamp**: Aggiunte colonne `updated_at` alle tabelle `ORARI`, `IMPORT`, `EXPORT` per tracciare le modifiche
- **Colonne timestamp SOF**: Aggiunte colonne `created_at` e `updated_at` alla tabella `SOF_DOCUMENTS`

### 2. Controllo Intelligente delle Modifiche

Il sistema ora verifica se ci sono state modifiche ai dati dopo la creazione del SOF:

```python
# Verifica modifiche negli orari
orari_modified = db.execute(text("""
    SELECT COUNT(*) FROM "ORARI" 
    WHERE viaggio_id = :viaggio_id 
    AND (updated_at > :sof_created OR updated_at IS NULL)
"""), {"viaggio_id": viaggio_id, "sof_created": sof_created_at}).fetchone()

# Verifica modifiche nei dati import/export (simile)
```

### 3. Rigenerazione Automatica

Se vengono rilevate modifiche:
- Il file SOF esistente viene eliminato
- Viene generato un nuovo SOF con i dati aggiornati
- Il nuovo file viene salvato con timestamp aggiornato

### 4. Endpoint per Rigenerazione Forzata

Aggiunto endpoint `POST /operativo/sof/viaggio/{viaggio_id}/regenerate` per forzare la rigenerazione manuale:

```python
@app.post("/operativo/sof/viaggio/{viaggio_id}/regenerate")
async def force_regenerate_sof(viaggio_id: int, current_user: Agente = Depends(require_auth), db: Session = Depends(get_db)):
```

## Funzionalità Aggiunte

### 1. Funzione `ensure_timestamp_columns()`

Assicura che tutte le tabelle necessarie abbiano le colonne timestamp per il tracking:

```python
def ensure_timestamp_columns(db: Session):
    tables_to_update = ["ORARI", "IMPORT", "EXPORT"]
    for table in tables_to_update:
        db.execute(text(f"""
            ALTER TABLE "{table}" 
            ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        """))
```

### 2. Logica di Controllo Modifiche

Il sistema ora:
1. Controlla se esiste un SOF salvato
2. Verifica se ci sono state modifiche ai dati dopo la creazione del SOF
3. Se ci sono modifiche, elimina il file esistente e rigenera
4. Se non ci sono modifiche, restituisce il file esistente

### 3. Logging Migliorato

Aggiunto logging dettagliato per tracciare:
- Quando viene rilevata una modifica
- Quando viene forzata una rigenerazione
- Statistiche delle modifiche per tipo di dato

## Come Usare

### Rigenerazione Automatica
Il sistema ora rigenera automaticamente il SOF quando rileva modifiche ai dati. Non è necessaria alcuna azione da parte dell'utente.

### Rigenerazione Forzata
Per forzare la rigenerazione di un SOF:

1. **Via API**: 
   ```bash
   POST /operativo/sof/viaggio/{viaggio_id}/regenerate
   ```

2. **Risultato**: Il SOF esistente viene eliminato e il viaggio torna visibile per permettere la rigenerazione

## Benefici

1. **Dati sempre aggiornati**: I SOF generati contengono sempre i dati più recenti
2. **Trasparenza**: Logging dettagliato di tutte le operazioni
3. **Controllo manuale**: Possibilità di forzare la rigenerazione quando necessario
4. **Backward compatibility**: Il sistema funziona anche con SOF esistenti senza timestamp

## Note Tecniche

- Le colonne timestamp vengono aggiunte automaticamente se non esistono
- Il sistema è retrocompatibile con SOF esistenti
- La verifica delle modifiche è efficiente e non impatta le performance
- I file SOF obsoleti vengono eliminati automaticamente per evitare accumulo di spazio

## Test Consigliati

1. Generare un SOF per un viaggio
2. Modificare orari o dati import/export
3. Rigenerare il SOF e verificare che contenga i dati aggiornati
4. Testare l'endpoint di rigenerazione forzata
5. Verificare che i SOF non modificati vengano restituiti dalla cache
