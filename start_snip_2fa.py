#!/usr/bin/env python3
"""
Script di avvio SNIP con supporto 2FA e auto-reload
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    print("🚀 AVVIO SNIP CON SUPPORTO 2FA E AUTO-RELOAD")
    print("=" * 60)
    
    # Verifica che main.py esista
    if not Path("main.py").exists():
        print("❌ File main.py non trovato!")
        return False
    
    # Pulisci sessioni precedenti
    from fix_2fa_reload_issue import TwoFactorReloadManager
    manager = TwoFactorReloadManager()
    manager.cleanup_session()
    
    print("🔐 Avvio con verifica 2FA iniziale...")
    print("🔄 Auto-reload abilitato per modifiche successive")
    print("-" * 60)
    
    try:
        # Avvia con uvicorn e reload
        cmd = [
            sys.executable, "-m", "uvicorn",
            "main:app",
            "--host", "0.0.0.0",
            "--port", "8002",
            "--reload",
            "--reload-delay", "1"
        ]
        
        print(f"🚀 Comando: {' '.join(cmd)}")
        print()
        
        # Esegui il comando
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n🛑 Arresto richiesto dall'utente")
        manager.cleanup_session()
    except Exception as e:
        print(f"\n❌ Errore: {e}")
        manager.cleanup_session()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
