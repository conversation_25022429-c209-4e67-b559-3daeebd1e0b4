#!/usr/bin/env python3
"""
Script per verificare lo stato della configurazione email_admin_email nel database
"""

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

def check_email_config():
    """Verifica lo stato della configurazione email nel database"""
    
    # Connessione al database
    DATABASE_URL = "postgresql://re77:271077@localhost:5432/AGENTE"
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    try:
        with SessionLocal() as db:
            print("🔍 Verifica configurazioni email nel database...")
            
            # Cerca tutte le configurazioni email
            result = db.execute(text("""
                SELECT config_key, config_value, is_active, id, created_at, updated_at 
                FROM "SYSTEM_CONFIG" 
                WHERE config_key LIKE '%email%'
                ORDER BY config_key
            """))
            
            rows = result.fetchall()
            
            if rows:
                print(f"📋 Trovate {len(rows)} configurazioni email:")
                for row in rows:
                    print(f"  ID: {row[3]}")
                    print(f"  Key: {row[0]}")
                    print(f"  Value: {row[1]}")
                    print(f"  Active: {row[2]}")
                    print(f"  Created: {row[4]}")
                    print(f"  Updated: {row[5]}")
                    print("  " + "-" * 40)
            else:
                print("❌ Nessuna configurazione email trovata")
            
            # Verifica specifica per email_admin_email
            print("\n🎯 Verifica specifica per 'email_admin_email':")
            result2 = db.execute(text("""
                SELECT config_key, config_value, is_active, id 
                FROM "SYSTEM_CONFIG" 
                WHERE config_key = 'email_admin_email'
            """))
            
            email_rows = result2.fetchall()
            
            if email_rows:
                print(f"✅ Trovate {len(email_rows)} righe per 'email_admin_email':")
                for row in email_rows:
                    print(f"  ID: {row[3]}, Value: {row[1]}, Active: {row[2]}")
                    
                if len(email_rows) > 1:
                    print("⚠️  PROBLEMA: Trovate multiple righe per la stessa chiave!")
            else:
                print("❌ Nessuna configurazione 'email_admin_email' trovata")
                
    except Exception as e:
        print(f"❌ Errore durante la verifica: {str(e)}")

if __name__ == "__main__":
    check_email_config()
