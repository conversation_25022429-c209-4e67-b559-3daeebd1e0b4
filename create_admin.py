#!/usr/bin/env python3
"""
Crea un utente admin per i test
"""

from database import SessionLocal
from sqlalchemy import text

def create_admin():
    db = SessionLocal()
    try:
        # Verifica utenti admin esistenti
        result = db.execute(text('SELECT email, ruolo FROM "AGENTE" WHERE ruolo IN (\'ADMIN\', \'SUPER_ADMIN\')'))
        admins = result.fetchall()
        print('Admin users found:')
        for admin in admins:
            print(f'  - {admin[0]}: {admin[1]}')
        
        if not admins:
            print('No admin users found. Creating test admin...')
        
        # Crea/aggiorna admin di test
        db.execute(text('''
            INSERT INTO "AGENTE" ("Nome", "Cognome", email, password, reparto, ruolo, visibile)
            VALUES ('Admin', 'Test', '<EMAIL>', 'AdminPassword123!', 'AMMINISTRAZIONE', 'SUPER_ADMIN', 'si')
            ON CONFLICT (email) DO UPDATE SET ruolo = 'SUPER_ADMIN'
        '''))
        db.commit()
        print('Admin user created/updated: <EMAIL> (SUPER_ADMIN)')
        
        # Verifica creazione
        result = db.execute(text('SELECT email, ruolo FROM "AGENTE" WHERE email = \'<EMAIL>\''))
        admin = result.fetchone()
        if admin:
            print(f'Verification: {admin[0]} has role {admin[1]}')
        else:
            print('ERROR: Admin user not found after creation')
            
    except Exception as e:
        print(f'Error: {e}')
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_admin()
