#!/usr/bin/env python3
"""
Fix per il problema di riavvio automatico di uvicorn
Quando il servizio FastAPI riavvia l'app, l'invio del codice e il confronto per riavviare l'app non funziona
"""

import os
import sys
import time
import logging
import subprocess
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CodeChangeHandler(FileSystemEventHandler):
    """Handler per rilevare cambiamenti nei file Python"""
    
    def __init__(self, restart_callback):
        self.restart_callback = restart_callback
        self.last_restart = 0
        self.restart_delay = 2  # Secondi di delay per evitare riavvii multipli
        
    def on_modified(self, event):
        if event.is_directory:
            return
            
        # Monitora solo file Python
        if not event.src_path.endswith('.py'):
            return
            
        # Evita riavvii troppo frequenti
        current_time = time.time()
        if current_time - self.last_restart < self.restart_delay:
            return
            
        logger.info(f"🔄 Rilevato cambiamento in: {event.src_path}")
        self.last_restart = current_time
        self.restart_callback()

class FastAPIReloadManager:
    """Manager per gestire il riavvio automatico di FastAPI"""
    
    def __init__(self, app_module="main:app", host="0.0.0.0", port=8002):
        self.app_module = app_module
        self.host = host
        self.port = port
        self.process = None
        self.observer = None
        self.restart_count = 0
        
    def start_server(self):
        """Avvia il server uvicorn"""
        try:
            if self.process:
                logger.info("🛑 Terminando processo esistente...")
                self.process.terminate()
                self.process.wait(timeout=5)
                
        except subprocess.TimeoutExpired:
            logger.warning("⚠️ Processo non terminato, forzando kill...")
            self.process.kill()
        except Exception as e:
            logger.error(f"❌ Errore terminazione processo: {e}")
            
        # Avvia nuovo processo
        cmd = [
            sys.executable, "-m", "uvicorn",
            self.app_module,
            "--host", self.host,
            "--port", str(self.port),
            "--reload-delay", "1",  # Delay più breve per reload
            "--reload-dir", ".",    # Directory da monitorare
            "--log-level", "info"
        ]
        
        logger.info(f"🚀 Avviando server: {' '.join(cmd)}")
        
        try:
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.restart_count += 1
            logger.info(f"✅ Server avviato (riavvio #{self.restart_count})")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Errore avvio server: {e}")
            return False
    
    def setup_file_watcher(self):
        """Configura il file watcher personalizzato"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            
        self.observer = Observer()
        handler = CodeChangeHandler(self.restart_server)
        
        # Monitora directory corrente e sottodirectory
        watch_paths = [
            ".",
            "templates",
            "static"
        ]
        
        for path in watch_paths:
            if os.path.exists(path):
                self.observer.schedule(handler, path, recursive=True)
                logger.info(f"👁️ Monitoraggio attivato per: {path}")
        
        self.observer.start()
        logger.info("🔍 File watcher avviato")
    
    def restart_server(self):
        """Riavvia il server"""
        logger.info("🔄 Riavvio server richiesto...")
        self.start_server()
    
    def run(self):
        """Avvia il manager"""
        logger.info("🚀 Avvio FastAPI Reload Manager...")
        
        # Avvia server iniziale
        if not self.start_server():
            logger.error("❌ Impossibile avviare il server")
            return False
            
        # Configura file watcher
        self.setup_file_watcher()
        
        try:
            logger.info("✅ Manager attivo. Premi Ctrl+C per fermare.")
            
            # Loop principale
            while True:
                if self.process and self.process.poll() is not None:
                    logger.warning("⚠️ Processo server terminato inaspettatamente")
                    if not self.start_server():
                        logger.error("❌ Impossibile riavviare il server")
                        break
                        
                time.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("🛑 Arresto richiesto dall'utente")
            
        finally:
            self.cleanup()
            
        return True
    
    def cleanup(self):
        """Pulizia risorse"""
        logger.info("🧹 Pulizia risorse...")
        
        if self.observer:
            self.observer.stop()
            self.observer.join()
            
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.process.kill()
            except Exception as e:
                logger.error(f"❌ Errore cleanup processo: {e}")
                
        logger.info("✅ Cleanup completato")

def check_dependencies():
    """Verifica dipendenze necessarie"""
    try:
        import watchdog
        logger.info("✅ Watchdog disponibile")
        return True
    except ImportError:
        logger.error("❌ Watchdog non installato")
        logger.info("💡 Installa con: pip install watchdog")
        return False

def main():
    """Funzione principale"""
    print("🔧 FASTAPI RELOAD MANAGER")
    print("=" * 50)
    print("Risolve i problemi di auto-reload di uvicorn")
    print("Fornisce un meccanismo di riavvio più affidabile")
    print("=" * 50)
    
    # Verifica dipendenze
    if not check_dependencies():
        return False
        
    # Verifica che main.py esista
    if not os.path.exists("main.py"):
        logger.error("❌ File main.py non trovato nella directory corrente")
        return False
        
    # Avvia manager
    manager = FastAPIReloadManager()
    return manager.run()

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Arresto richiesto dall'utente")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Errore critico: {e}")
        sys.exit(1)
