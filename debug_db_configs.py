#!/usr/bin/env python3
"""
Debug delle configurazioni nel database
"""

from database import SessionLocal
from sqlalchemy import text

def debug_db_configs():
    db = SessionLocal()
    try:
        # Leggi tutte le configurazioni dal database
        result = db.execute(text("""
            SELECT config_key, config_value, config_type
            FROM "SYSTEM_CONFIG"
            ORDER BY config_key
        """))
        
        configs = result.fetchall()
        
        print(f"Configurazioni trovate nel database: {len(configs)}")
        print("=" * 60)
        
        # Raggruppa per sezione
        sections = {}
        for row in configs:
            key_parts = row[0].split('_', 1)
            if len(key_parts) >= 2:
                section = key_parts[0]
                key = key_parts[1]
                value = row[1]
                config_type = row[2]
                
                if section not in sections:
                    sections[section] = []
                sections[section].append((key, value, config_type))
            else:
                print(f"Chiave malformata: {row[0]}")
        
        # Mostra per sezione
        for section, items in sections.items():
            print(f"\n[{section.upper()}] - {len(items)} configurazioni:")
            for key, value, config_type in items[:5]:  # Mostra solo le prime 5
                print(f"  {key}: {value} ({config_type})")
            if len(items) > 5:
                print(f"  ... e altre {len(items) - 5} configurazioni")
        
        # Verifica sezioni specifiche
        print("\n" + "=" * 60)
        print("VERIFICA SEZIONI SPECIFICHE:")
        
        target_sections = ['database', 'reporting', 'system']
        for section in target_sections:
            count = len([c for c in configs if c[0].startswith(f"{section}_")])
            print(f"- {section}: {count} configurazioni")
            
            if count > 0:
                section_configs = [c for c in configs if c[0].startswith(f"{section}_")]
                print(f"  Esempi: {[c[0] for c in section_configs[:3]]}")
        
    except Exception as e:
        print(f"Errore: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    debug_db_configs()
