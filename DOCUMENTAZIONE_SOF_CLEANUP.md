# 🗑️ Sistema di Pulizia Automatica File SOF

## 📋 Panoramica

Implementazione della funzionalità richiesta: **eliminazione automatica dei file SOF esistenti prima di generarne uno nuovo**.

Quando si clicca su "Genera SOF" per un viaggio, il sistema ora:

1. **Controlla** se esistono file SOF per quel viaggio nella cartella `sof_documents`
2. **Elimina automaticamente** tutti i file SOF esistenti per quel viaggio
3. **Genera sempre** un nuovo file SOF con timestamp aggiornato

## 🔧 Modifiche Implementate

### 1. **Endpoint `/operativo/sof/viaggio/{viaggio_id}/sof/download`**

**PRIMA** (comportamento precedente):
- Controllava se esisteva un SOF salvato
- Se esisteva e non c'erano modifiche → restituiva il file esistente
- Solo se c'erano modifiche → rigenerava il SOF

**DOPO** (nuovo comportamento):
- **Elimina SEMPRE** qualsiasi SOF esistente per quel viaggio
- **Genera SEMPRE** un nuovo SOF
- **Pulisce** eventuali file orfani nella cartella

### 2. **Nuove Funzioni Utility**

#### `cleanup_orphaned_sof_files(viaggio_id, viaggio_nome)`
```python
def cleanup_orphaned_sof_files(viaggio_id: int, viaggio_nome: str):
    """
    Pulisce eventuali file SOF orfani nella cartella sof_documents per un viaggio specifico.
    Cerca file con pattern: SOF_Viaggio_{viaggio_id}_*
    """
```

**Funzionalità:**
- Cerca file con pattern `SOF_Viaggio_{viaggio_id}_*.docx`
- Cerca file con pattern `SOF_Viaggio_{viaggio_id}_{viaggio_nome}_*.docx`
- Elimina tutti i file trovati
- Gestisce errori senza bloccare il processo

#### `get_sof_file_pattern(viaggio_id, viaggio_nome)`
```python
def get_sof_file_pattern(viaggio_id: int, viaggio_nome: str = None) -> str:
    """
    Restituisce il pattern per i file SOF di un viaggio specifico
    """
```

## 📁 Pattern File SOF

### Formato Nome File
```
SOF_Viaggio_{viaggio_id}_{viaggio_nome}_{timestamp}.docx
```

**Esempio per viaggio 62 con nome "ECA1222":**
```
SOF_Viaggio_62_ECA1222_20250613_221406.docx
```

### Pattern di Ricerca
1. **Pattern Principale:** `SOF_Viaggio_{viaggio_id}_*.docx`
2. **Pattern Specifico:** `SOF_Viaggio_{viaggio_id}_{viaggio_nome}_*.docx`

## 🔄 Flusso di Esecuzione

### Quando si clicca "Genera SOF":

```mermaid
graph TD
    A[Click Genera SOF] --> B[Controlla SOF esistenti nel DB]
    B --> C{SOF esistente?}
    C -->|Sì| D[Elimina file fisico]
    C -->|No| F[Continua]
    D --> E[Elimina record DB]
    E --> F
    F --> G[Pulizia file orfani]
    G --> H[Genera nuovo SOF]
    H --> I[Salva in sof_documents/]
    I --> J[Registra nel DB]
    J --> K[Download file]
```

### Dettaglio Pulizia File Orfani:

```mermaid
graph TD
    A[cleanup_orphaned_sof_files] --> B[Verifica cartella sof_documents]
    B --> C[Cerca pattern SOF_Viaggio_62_*]
    C --> D[Cerca pattern SOF_Viaggio_62_ECA1222_*]
    D --> E[Unisce risultati]
    E --> F[Rimuove duplicati]
    F --> G{File trovati?}
    G -->|Sì| H[Elimina ogni file]
    G -->|No| I[Nessuna azione]
    H --> J[Log eliminazione]
    J --> I[Fine]
```

## 🧪 Test Implementati

### File: `test_sof_cleanup.py`

**Test 1: Pattern Matching**
- Verifica che i pattern glob trovino i file corretti
- Testa diversi formati di nome file

**Test 2: Funzione Cleanup**
- Crea file SOF di test
- Esegue la pulizia
- Verifica che solo i file del viaggio specifico vengano eliminati

**Esecuzione:**
```bash
python test_sof_cleanup.py
```

## 📊 Vantaggi della Nuova Implementazione

### ✅ **Vantaggi**
1. **Sempre aggiornato:** Ogni SOF generato contiene i dati più recenti
2. **Nessun file obsoleto:** Elimina automaticamente versioni precedenti
3. **Gestione spazio:** Evita accumulo di file SOF duplicati
4. **Consistenza:** Comportamento prevedibile e uniforme
5. **Pulizia robusta:** Gestisce anche file orfani o corrotti

### ⚠️ **Considerazioni**
1. **Tempo di generazione:** Ogni richiesta genera un nuovo file (più lento)
2. **Perdita cronologia:** Non mantiene versioni precedenti del SOF
3. **Carico server:** Maggiore utilizzo CPU per generazione DOCX

## 🔧 Configurazione

### Cartella SOF
```
sof_documents/
├── SOF_Viaggio_61_ABC123_20250614_101530.docx
├── SOF_Viaggio_62_ECA1222_20250614_102045.docx
└── SOF_Viaggio_63_XYZ789_20250614_103012.docx
```

### Database
Tabella `SOF_DOCUMENTS`:
- `viaggio_id`: ID del viaggio
- `filename`: Nome del file
- `file_path`: Percorso completo
- `file_size`: Dimensione file
- `statistics`: Statistiche JSON
- `created_at`: Timestamp creazione
- `updated_at`: Timestamp aggiornamento

## 🚀 Utilizzo

### Per l'Utente
1. Vai su `/operativo/sof/viaggio/{viaggio_id}`
2. Clicca su **"Genera SOF"**
3. Il sistema elimina automaticamente eventuali SOF esistenti
4. Genera e scarica il nuovo SOF aggiornato

### Per lo Sviluppatore
```python
# Pulizia manuale file orfani
from main import cleanup_orphaned_sof_files
cleanup_orphaned_sof_files(viaggio_id=62, viaggio_nome="ECA1222")

# Pattern per trovare file SOF
from main import get_sof_file_pattern
pattern = get_sof_file_pattern(62, "ECA1222")
# Risultato: "SOF_Viaggio_62_ECA1222_*.docx"
```

## 🔍 Troubleshooting

### Problema: File non eliminati
**Causa:** Permessi file o file in uso
**Soluzione:** Verificare permessi cartella `sof_documents/`

### Problema: Pattern non trova file
**Causa:** Nome viaggio con caratteri speciali
**Soluzione:** La funzione pulisce automaticamente caratteri speciali

### Problema: Errori durante eliminazione
**Causa:** File corrotti o bloccati
**Soluzione:** Il sistema continua comunque con la generazione

## 📝 Log di Sistema

Il sistema registra tutte le operazioni:

```
🔄 Generazione SOF per viaggio 62 - Controllo file esistenti...
🗑️ SOF esistente trovato per viaggio 62: SOF_Viaggio_62_ECA1222_20250613_221406.docx
✅ File SOF eliminato: sof_documents/SOF_Viaggio_62_ECA1222_20250613_221406.docx
✅ Record SOF eliminato dal database per viaggio 62
🧹 Trovati 0 file SOF orfani per viaggio 62
🔄 Inizio generazione SOF per viaggio 62
```

## 🎯 Risultato Finale

**Comportamento garantito:**
- ✅ Ogni click su "Genera SOF" elimina il file esistente
- ✅ Ogni SOF generato è sempre aggiornato
- ✅ Nessun accumulo di file duplicati
- ✅ Pulizia automatica di file orfani
- ✅ Gestione robusta degli errori
