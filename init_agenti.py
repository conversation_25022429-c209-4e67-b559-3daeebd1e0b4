from sqlalchemy import text
from database import engine, SessionLocal
from models import Base, Agente
from passlib.context import CryptContext

# Configurazione per l'hashing delle password
pwd_context = CryptContext(
    schemes=["bcrypt"],
    deprecated="auto",
    bcrypt__rounds=12
)

def init_agenti():
    # Crea la tabella se non esiste
    Base.metadata.create_all(bind=engine)

    # Crea una sessione
    db = SessionLocal()

    try:
        # Verifica se la tabella esiste
        result = db.execute(text("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'AGENTE')"))
        table_exists = result.scalar()

        if not table_exists:
            print("Creazione tabella AGENTE...")
            # Crea la tabella
            db.execute(text("""
                CREATE TABLE "AGENTE" (
                    id_user SERIAL PRIMARY KEY,
                    "Nome" VARCHAR,
                    "Cognome" VARCHAR,
                    email VARCHAR UNIQUE,
                    password VARCHAR,
                    reparto VARCHAR,
                    bloccato BOOLEAN DEFAULT true
                )
            """))
            db.commit()
            print("Tabella AGENTE creata con successo!")

        # Verifica se ci sono già utenti
        result = db.execute(text('SELECT COUNT(*) FROM "AGENTE"'))
        count = result.scalar()

        if count == 0:
            print("Inserimento utente di test...")
            # Crea un utente di test
            hashed_password = pwd_context.hash("admin123")
            db.execute(text("""
                INSERT INTO "AGENTE" ("Nome", "Cognome", email, password, reparto, bloccato)
                VALUES ('Admin', 'Admin', '<EMAIL>', :password, 'OPERATIVO', false)
            """), {"password": hashed_password})
            db.commit()
            print("Utente di test creato con successo!")

        print("Inizializzazione completata!")

    except Exception as e:
        print(f"Errore durante l'inizializzazione: {str(e)}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    init_agenti()