from sqlalchemy import text
from database import engine, SessionLocal

def init_navi():
    # Crea una sessione
    db = SessionLocal()
    
    try:
        # Verifica se la tabella esiste
        result = db.execute(text("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'NAVI')"))
        table_exists = result.scalar()
        
        if not table_exists:
            print("Creazione tabella NAVI...")
            # Crea la tabella
            db.execute(text("""
                CREATE TABLE "NAVI" (
                    id SERIAL PRIMARY KEY,
                    nave VARCHAR,
                    codice_nave VARCHAR,
                    prefisso_viaggio VARCHAR
                )
            """))
            db.commit()
            print("Tabella NAVI creata con successo!")
            
            # Inserisci alcuni dati di esempio
            print("Inserimento dati di esempio...")
            db.execute(text("""
                INSERT INTO "NAVI" (nave, codice_nave, prefisso_viaggio) VALUES
                ('ATLANTIC COMPANION', 'UN', 'SCP'),
                ('ATLANTIC STAR', 'UN', 'SCP'),
                ('ATLANTIC SUN', 'UN', 'SCP')
            """))
            db.commit()
            print("Dati di esempio inseriti con successo!")
        else:
            print("Tabella NAVI già esistente")
            
        # Verifica il contenuto della tabella
        result = db.execute(text('SELECT COUNT(*) FROM "NAVI"'))
        count = result.scalar()
        print(f"Numero di navi nella tabella: {count}")
        
        # Mostra la struttura della tabella
        result = db.execute(text("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'NAVI'
        """))
        columns = result.fetchall()
        print("\nStruttura tabella NAVI:")
        for col in columns:
            print(f"- {col[0]}: {col[1]}")
        
        print("\nInizializzazione completata!")
        
    except Exception as e:
        print(f"Errore durante l'inizializzazione: {str(e)}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    init_navi()