#!/usr/bin/env python3
"""
Script per correggere tutte le virgolette escape nelle f-string
"""

import re
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_all_quotes():
    """Corregge tutte le virgolette escape nelle f-string"""
    logger.info("🔧 Correzione virgolette escape nelle f-string")
    
    try:
        # Leggi il file
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Correggi tutte le f-string con virgolette escape
        logger.info("   📝 Correzione f-string con virgolette escape...")
        
        # Pattern per trovare f-string con getattr e virgolette escape
        pattern = r"f'([^']*){getattr\(([^,]+), \\\"([^\\\"]+)\\\", \\\"([^\\\"]*)\\\")([^']*)}([^']*)')"
        replacement = r'f"\1{getattr(\2, \"\3\", \"\4\")}\5\6"'
        
        content = re.sub(pattern, replacement, content)
        
        # Pattern più semplice per f-string con virgolette escape
        content = re.sub(
            r"f'([^']*){getattr\(current_user, \\\"email\\\", \\\"unknown\\\"\)}([^']*)'",
            r'f"\1{getattr(current_user, \'email\', \'unknown\')}\2"',
            content
        )
        
        # Correggi manualmente le righe problematiche
        specific_fixes = [
            # Riga 3031
            (r"logger\.info\(f'Password verificata correttamente per utente \{getattr\(current_user, \\\"email\\\", \\\"unknown\\\"\)\}'\)",
             r'logger.info(f"Password verificata correttamente per utente {getattr(current_user, \'email\', \'unknown\')}")'),
            
            # Riga 3037
            (r"logger\.warning\(f'Tentativo di verifica password fallito per utente \{getattr\(current_user, \\\"email\\\", \\\"unknown\\\"\)\}'\)",
             r'logger.warning(f"Tentativo di verifica password fallito per utente {getattr(current_user, \'email\', \'unknown\')}")'),
            
            # Riga 3044
            (r"logger\.error\(f'Errore verifica password per \{getattr\(current_user, \\\"email\\\", \\\"unknown\\\"\)\}: \{str\(e\)\}'\)",
             r'logger.error(f"Errore verifica password per {getattr(current_user, \'email\', \'unknown\')}: {str(e)}")'),
            
            # Altre righe simili
            (r"logger\.warning\(f'Tentativo cambio password con password attuale errata per \{getattr\(current_user, \\\"email\\\", \\\"unknown\\\"\)\}'\)",
             r'logger.warning(f"Tentativo cambio password con password attuale errata per {getattr(current_user, \'email\', \'unknown\')}")'),
            
            (r"logger\.warning\(f'Tentativo cambio password con nuova password uguale a quella attuale per \{getattr\(current_user, \\\"email\\\", \\\"unknown\\\"\)\}'\)",
             r'logger.warning(f"Tentativo cambio password con nuova password uguale a quella attuale per {getattr(current_user, \'email\', \'unknown\')}")'),
            
            (r"logger\.error\(f'Errore aggiornamento password per \{getattr\(current_user, \\\"email\\\", \\\"unknown\\\"\)\}: \{e\}'\)",
             r'logger.error(f"Errore aggiornamento password per {getattr(current_user, \'email\', \'unknown\')}: {e}")'),
            
            (r"logger\.info\(f'Password cambiata con successo per utente \{getattr\(current_user, \\\"email\\\", \\\"unknown\\\"\)\}'\)",
             r'logger.info(f"Password cambiata con successo per utente {getattr(current_user, \'email\', \'unknown\')}")'),
            
            (r"logger\.error\(f'Errore cambio password per \{getattr\(current_user, \\\"email\\\", \\\"unknown\\\"\)\}: \{str\(e\)\}'\)",
             r'logger.error(f"Errore cambio password per {getattr(current_user, \'email\', \'unknown\')}: {str(e)}")'),
            
            (r"logger\.warning\(f'Tentativo cambio password obbligatorio con password attuale errata per \{getattr\(current_user, \\\"email\\\", \\\"unknown\\\"\)\}'\)",
             r'logger.warning(f"Tentativo cambio password obbligatorio con password attuale errata per {getattr(current_user, \'email\', \'unknown\')}")'),
            
            (r"logger\.warning\(f'Tentativo cambio password obbligatorio con nuova password uguale a quella attuale per \{getattr\(current_user, \\\"email\\\", \\\"unknown\\\"\)\}'\)",
             r'logger.warning(f"Tentativo cambio password obbligatorio con nuova password uguale a quella attuale per {getattr(current_user, \'email\', \'unknown\')}")'),
            
            (r"logger\.error\(f'Errore aggiornamento password obbligatorio per \{getattr\(current_user, \\\"email\\\", \\\"unknown\\\"\)\}: \{e\}'\)",
             r'logger.error(f"Errore aggiornamento password obbligatorio per {getattr(current_user, \'email\', \'unknown\')}: {e}")'),
            
            (r"logger\.info\(f'Password scaduta cambiata con successo per utente \{getattr\(current_user, \\\"email\\\", \\\"unknown\\\"\)\}'\)",
             r'logger.info(f"Password scaduta cambiata con successo per utente {getattr(current_user, \'email\', \'unknown\')}")'),
        ]
        
        for pattern, replacement in specific_fixes:
            old_content = content
            content = re.sub(pattern, replacement, content)
            if content != old_content:
                logger.info(f"     ✅ Corretto: {pattern[:50]}...")
        
        # Scrivi il file modificato
        if content != original_content:
            with open('main.py', 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info("✅ File main.py corretto con successo!")
            
            # Conta le modifiche
            original_lines = len(original_content.split('\n'))
            new_lines = len(content.split('\n'))
            logger.info(f"📊 Righe originali: {original_lines}, Nuove righe: {new_lines}")
        else:
            logger.info("ℹ️ Nessuna modifica necessaria")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore durante la correzione: {e}")
        return False

def validate_syntax():
    """Valida la sintassi del file Python"""
    logger.info("🔍 Validazione sintassi Python...")
    
    try:
        import ast
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Prova a parsare il file
        ast.parse(content)
        logger.info("✅ Sintassi Python valida!")
        return True
        
    except SyntaxError as e:
        logger.error(f"❌ Errore di sintassi: {e}")
        logger.error(f"   Riga {e.lineno}: {e.text}")
        return False
    except Exception as e:
        logger.error(f"❌ Errore durante la validazione: {e}")
        return False

def main():
    """Esegue tutte le correzioni"""
    logger.info("🚀 CORREZIONE VIRGOLETTE F-STRING")
    logger.info("=" * 50)
    
    # Correggi le virgolette
    fix_ok = fix_all_quotes()
    
    # Valida la sintassi
    syntax_ok = validate_syntax()
    
    # Riepilogo
    logger.info("=" * 50)
    logger.info("📋 RIEPILOGO:")
    logger.info(f"   Correzioni applicate: {'✅' if fix_ok else '❌'}")
    logger.info(f"   Sintassi valida: {'✅' if syntax_ok else '❌'}")
    
    if fix_ok and syntax_ok:
        logger.info("🎉 VIRGOLETTE CORRETTE!")
        logger.info("💡 Il file main.py ora dovrebbe avere sintassi valida")
    else:
        logger.info("⚠️ PROBLEMI RILEVATI")
        if not fix_ok:
            logger.info("   - Errore durante le correzioni")
        if not syntax_ok:
            logger.info("   - Errori di sintassi persistenti")
    
    logger.info("=" * 50)
    logger.info("🏁 CORREZIONE VIRGOLETTE COMPLETATA")

if __name__ == "__main__":
    main()
