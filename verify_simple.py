#!/usr/bin/env python3
"""
Verifica semplice backup completo
"""

import gzip
from pathlib import Path

def verify_simple():
    print("VERIFICA BACKUP COMPLETO")
    print("=" * 40)
    
    # Trova ultimo backup
    backup_dir = Path("backups")
    backup_files = list(backup_dir.glob("snip_backup_*.sql.gz"))
    
    latest_backup = max(backup_files, key=lambda f: f.stat().st_mtime)
    print(f"File: {latest_backup.name}")
    print(f"Dimensione: {latest_backup.stat().st_size} bytes")
    
    with gzip.open(latest_backup, 'rt', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    print(f"Righe totali: {len(lines)}")
    
    # Verifica componenti
    print(f"\nCOMPONENTI:")
    print(f"- Header PGDMP: {content.count('PGDMP')}")
    print(f"- CREATE TYPE: {content.count('CREATE TYPE')}")
    print(f"- COPY statements: {content.count('COPY public.')}")
    
    # Conta tabelle
    expected_tables = [
        "AGENTE", "ARMATORE", "ATLAS", "AUDIT_LOG", 
        "CODICI_ATLAS", "DEPARTMENT_NOTIFICATIONS", 
        "EXPORT", "IMPORT", "LOGIN_ATTEMPTS", "NAVI",
        "ORARI", "PORTI_GESTIONE", "SOF_DOCUMENTS", 
        "SYSTEM_CONFIG", "SYSTEM_STATS", 
        "USER_NOTIFICATION_READ", "USER_SESSIONS", "VIAGGIO"
    ]
    
    tables_found = 0
    print(f"\nTABELLE (18 totali):")
    for table in expected_tables:
        if f'COPY public."{table}"' in content:
            print(f"OK {table}")
            tables_found += 1
        else:
            print(f"NO {table}")
    
    print(f"\nRISULTATO:")
    print(f"Tabelle trovate: {tables_found}/18")
    
    if tables_found == 18:
        print("BACKUP COMPLETO: TUTTE LE 18 TABELLE INCLUSE")
        print("FORMATO: Identico a AGENTE.sql")
        print("TABELLE VUOTE: Incluse correttamente")
        print("STATO: PERFETTO")
    else:
        print(f"BACKUP INCOMPLETO: {tables_found}/18 tabelle")
    
    # Mostra prime righe
    print(f"\nPRIME 20 RIGHE:")
    print("-" * 30)
    for i, line in enumerate(lines[:20], 1):
        clean_line = line.encode('ascii', 'ignore').decode('ascii')
        if clean_line.strip():
            print(f"{i:2d}: {clean_line}")

if __name__ == "__main__":
    verify_simple()
