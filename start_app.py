#!/usr/bin/env python3
"""
Script di avvio per l'applicazione SNIP con sistema 2FA SMS
Gestisce l'ambiente virtuale e avvia l'applicazione con il sistema di sicurezza
"""

import os
import sys
import subprocess
import logging

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_virtual_env():
    """Verifica se stiamo usando l'ambiente virtuale corretto"""
    expected_venv = os.path.join(os.getcwd(), 'venv_new', 'Scripts', 'python.exe')
    current_python = sys.executable
    
    if os.path.exists(expected_venv):
        if current_python.lower() != expected_venv.lower():
            logger.warning(f"Non stai usando l'ambiente virtuale corretto")
            logger.info(f"Ambiente attuale: {current_python}")
            logger.info(f"Ambiente atteso: {expected_venv}")
            return False
        else:
            logger.info("✅ Ambiente virtuale corretto in uso")
            return True
    else:
        logger.warning("⚠️  Ambiente virtuale non trovato")
        return False

def check_dependencies():
    """Verifica che tutte le dipendenze siano installate"""
    required_modules = [
        'fastapi', 'uvicorn', 'sqlalchemy', 'requests', 
        'pydantic_settings', 'psycopg2', 'jinja2'
    ]
    
    missing = []
    for module in required_modules:
        try:
            __import__(module)
            logger.info(f"✅ {module}")
        except ImportError:
            logger.error(f"❌ {module} (mancante)")
            missing.append(module)
    
    return len(missing) == 0, missing

def main():
    """Funzione principale di avvio"""
    print("🚀 AVVIO APPLICAZIONE SNIP CON SISTEMA 2FA SMS")
    print("=" * 60)
    
    # Verifica ambiente virtuale
    if not check_virtual_env():
        print("\n⚠️  ATTENZIONE: Ambiente virtuale non corretto!")
        print("🔧 Per usare l'ambiente virtuale corretto:")
        print("   1. Apri PowerShell come amministratore")
        print("   2. Esegui: Set-ExecutionPolicy RemoteSigned -Scope CurrentUser")
        print("   3. Attiva l'ambiente: .\\venv_new\\Scripts\\Activate.ps1")
        print("   4. Avvia l'app: python main.py")
        return False
    
    # Verifica dipendenze
    print("\n🔍 Verifica dipendenze...")
    deps_ok, missing = check_dependencies()
    
    if not deps_ok:
        print(f"\n❌ Dipendenze mancanti: {', '.join(missing)}")
        print("🔧 Installa le dipendenze mancanti:")
        print("   pip install -r requirements_complete.txt")
        return False
    
    print("✅ Tutte le dipendenze sono installate")
    
    # Avvia l'applicazione principale
    print("\n🔐 Avvio sistema 2FA e applicazione...")
    print("📱 Preparati a ricevere un SMS al numero 3805127005")
    print("-" * 60)
    
    try:
        # Importa e avvia il main
        import main
        return True
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Avvio interrotto dall'utente")
        return False
    except Exception as e:
        logger.error(f"❌ Errore durante l'avvio: {e}")
        print(f"\n❌ Errore: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n👋 Arrivederci!")
        sys.exit(0)
