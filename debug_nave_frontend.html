<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Nave Frontend</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-left: 4px solid #007bff; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        select { padding: 5px; margin: 5px; min-width: 200px; }
        .test-section { border: 1px solid #ddd; padding: 15px; margin: 15px 0; }
        .test-section h3 { margin-top: 0; }
    </style>
</head>
<body>
    <h1>🔍 Debug Problema Nave ATLANTIC STAR</h1>
    
    <div class="test-section">
        <h3>Test 1: Caricamento API Navi</h3>
        <button onclick="testApiNavi()">Testa API /api/navi</button>
        <div id="navi-result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 2: Simulazione Dropdown</h3>
        <button onclick="simulaDropdown()">Simula Caricamento Dropdown</button>
        <br>
        <select id="test-nave-select">
            <option value="">Seleziona nave...</option>
        </select>
        <div id="dropdown-result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 3: Test Impostazione Valore</h3>
        <input type="number" id="nave-id-input" placeholder="ID nave da impostare" value="2">
        <button onclick="testImpostazioneValore()">Testa Impostazione</button>
        <div id="impostazione-result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 4: Test Viaggio Specifico</h3>
        <input type="number" id="viaggio-id-input" placeholder="ID viaggio" value="1">
        <button onclick="testViaggioSpecifico()">Testa Caricamento Viaggio</button>
        <div id="viaggio-result"></div>
    </div>
    
    <div id="log-container">
        <h3>📋 Log Debug</h3>
    </div>

    <script>
        function log(message, type = 'log') {
            const container = document.getElementById('log-container');
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(div);
            console.log(message);
        }

        async function testApiNavi() {
            log('🚢 Test API /api/navi...');
            const resultDiv = document.getElementById('navi-result');
            
            try {
                const response = await fetch('/api/navi');
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ API navi OK: ${data.data.length} navi trovate`, 'success');
                    
                    let html = '<h4>Navi disponibili:</h4><ul>';
                    data.data.forEach((nave, index) => {
                        html += `<li><strong>ID: ${nave.id}</strong> - ${nave.nome} (Prefisso: ${nave.prefisso_viaggio || 'N/A'})`;
                        if (nave.nome.includes('ATLANTIC STAR')) {
                            html += ' <span style="color: red;">⚠️ ATLANTIC STAR</span>';
                        }
                        html += '</li>';
                    });
                    html += '</ul>';
                    
                    resultDiv.innerHTML = html;
                    
                    // Trova ATLANTIC STAR
                    const atlanticStar = data.data.find(n => n.nome.includes('ATLANTIC STAR'));
                    if (atlanticStar) {
                        log(`🎯 ATLANTIC STAR trovata: ID ${atlanticStar.id}, posizione ${data.data.indexOf(atlanticStar) + 1}`, 'warning');
                    }
                    
                    return data.data;
                } else {
                    log(`❌ Errore API navi: ${data.error}`, 'error');
                    resultDiv.innerHTML = `<div class="error">Errore: ${data.error}</div>`;
                    return null;
                }
            } catch (error) {
                log(`❌ Errore connessione: ${error}`, 'error');
                resultDiv.innerHTML = `<div class="error">Errore: ${error}</div>`;
                return null;
            }
        }

        async function simulaDropdown() {
            log('📋 Simulazione caricamento dropdown...');
            const select = document.getElementById('test-nave-select');
            const resultDiv = document.getElementById('dropdown-result');
            
            const navi = await testApiNavi();
            if (!navi) return;
            
            // Pulisci e ricarica dropdown
            select.innerHTML = '<option value="">Seleziona nave...</option>';
            
            navi.forEach(nave => {
                const option = document.createElement('option');
                option.value = nave.id;
                option.textContent = nave.nome;
                option.dataset.prefisso = nave.prefisso_viaggio || '';
                select.appendChild(option);
            });
            
            log(`✅ Dropdown popolata con ${navi.length} navi`, 'success');
            
            // Test valore di default
            log(`🔍 Valore di default select: "${select.value}"`);
            log(`🔍 Opzione selezionata: "${select.options[select.selectedIndex].textContent}"`);
            
            resultDiv.innerHTML = `
                <p><strong>Dropdown popolata:</strong> ${select.options.length - 1} navi</p>
                <p><strong>Valore attuale:</strong> "${select.value}"</p>
                <p><strong>Opzione selezionata:</strong> "${select.options[select.selectedIndex].textContent}"</p>
            `;
        }

        async function testImpostazioneValore() {
            log('🔧 Test impostazione valore...');
            const select = document.getElementById('test-nave-select');
            const naveId = document.getElementById('nave-id-input').value;
            const resultDiv = document.getElementById('impostazione-result');
            
            if (!naveId) {
                log('❌ Inserisci un ID nave', 'error');
                return;
            }
            
            log(`🎯 Tentativo impostazione nave ID: ${naveId}`);
            
            // Verifica che l'opzione esista
            const opzioni = Array.from(select.options);
            const opzioneTarget = opzioni.find(opt => opt.value === naveId);
            
            if (!opzioneTarget) {
                log(`❌ Opzione con ID ${naveId} non trovata`, 'error');
                resultDiv.innerHTML = `<div class="error">Opzione con ID ${naveId} non trovata</div>`;
                return;
            }
            
            log(`✅ Opzione trovata: "${opzioneTarget.textContent}"`);
            
            // Prima impostazione
            select.value = naveId;
            log(`🔍 Dopo impostazione - Valore: "${select.value}"`);
            log(`🔍 Dopo impostazione - Opzione: "${select.options[select.selectedIndex].textContent}"`);
            
            // Verifica se l'impostazione è andata a buon fine
            if (select.value === naveId) {
                log(`✅ Impostazione riuscita!`, 'success');
                resultDiv.innerHTML = `<div class="success">✅ Nave impostata correttamente: ${opzioneTarget.textContent}</div>`;
            } else {
                log(`❌ Impostazione fallita! Valore atteso: ${naveId}, valore attuale: ${select.value}`, 'error');
                resultDiv.innerHTML = `<div class="error">❌ Impostazione fallita</div>`;
                
                // Analisi dettagliata
                log('🔍 Analisi dettagliata opzioni:');
                opzioni.forEach((opt, index) => {
                    log(`   ${index}: value="${opt.value}" (tipo: ${typeof opt.value}), text="${opt.textContent}"`);
                });
            }
        }

        async function testViaggioSpecifico() {
            log('🚢 Test caricamento viaggio specifico...');
            const viaggioId = document.getElementById('viaggio-id-input').value;
            const resultDiv = document.getElementById('viaggio-result');
            
            if (!viaggioId) {
                log('❌ Inserisci un ID viaggio', 'error');
                return;
            }
            
            try {
                const response = await fetch(`/api/viaggi/${viaggioId}`);
                const data = await response.json();
                
                if (data.success) {
                    const viaggio = data.data;
                    log(`✅ Viaggio caricato: ${viaggio.viaggio}`, 'success');
                    log(`🚢 Nave ID: ${viaggio.nave_id} (${typeof viaggio.nave_id})`);
                    log(`🚢 Nome nave: ${viaggio.nome_nave}`);
                    
                    resultDiv.innerHTML = `
                        <h4>Dati Viaggio:</h4>
                        <ul>
                            <li><strong>ID:</strong> ${viaggio.id}</li>
                            <li><strong>Codice:</strong> ${viaggio.viaggio}</li>
                            <li><strong>Nave ID:</strong> ${viaggio.nave_id} (tipo: ${typeof viaggio.nave_id})</li>
                            <li><strong>Nome Nave:</strong> ${viaggio.nome_nave}</li>
                            <li><strong>Porto Gestione ID:</strong> ${viaggio.porto_gestione_id}</li>
                        </ul>
                    `;
                    
                    // Test impostazione automatica
                    await simulaDropdown();
                    const select = document.getElementById('test-nave-select');
                    
                    log(`🔧 Test impostazione automatica nave ID: ${viaggio.nave_id}`);
                    select.value = viaggio.nave_id;
                    
                    if (select.value == viaggio.nave_id) {
                        log(`✅ Impostazione automatica riuscita!`, 'success');
                    } else {
                        log(`❌ Impostazione automatica fallita! Atteso: ${viaggio.nave_id}, ottenuto: ${select.value}`, 'error');
                        
                        // Prova conversione stringa
                        select.value = String(viaggio.nave_id);
                        if (select.value == viaggio.nave_id) {
                            log(`✅ Impostazione riuscita con conversione stringa!`, 'success');
                        } else {
                            log(`❌ Anche con conversione stringa fallisce`, 'error');
                        }
                    }
                    
                } else {
                    log(`❌ Errore caricamento viaggio: ${data.error}`, 'error');
                    resultDiv.innerHTML = `<div class="error">Errore: ${data.error}</div>`;
                }
            } catch (error) {
                log(`❌ Errore connessione: ${error}`, 'error');
                resultDiv.innerHTML = `<div class="error">Errore: ${error}</div>`;
            }
        }

        // Auto-test all'avvio
        window.addEventListener('load', () => {
            log('🚀 Debug tool caricato');
            log('ℹ️ Usa i pulsanti sopra per testare i vari componenti');
        });
    </script>
</body>
</html>
