// DEBUG SCRIPT PER CONFIGURAZIONI DATABASE
// Incolla questo script nella console del browser (F12) quando sei su /dashboard/amministrazione

console.log("🔧 DEBUG CONFIGURAZIONI DATABASE");
console.log("=" * 50);

// 1. Verifica presenza campi database
function checkDatabaseFields() {
    console.log("\n1. Verifica campi database nel DOM:");
    console.log("-".repeat(40));
    
    const databaseFields = [
        'db-backup-schedule',
        'db-backup-time', 
        'db-backup-retention',
        'db-backup-path',
        'db-compress-backup',
        'db-log-cleanup',
        'db-archive-months',
        'db-optimize',
        'db-auto-vacuum',
        'db-analyze',
        'db-disk-threshold',
        'db-connection-threshold',
        'db-monitor-performance',
        'db-alert-email'
    ];
    
    let missingFields = [];
    let foundFields = [];
    
    databaseFields.forEach(fieldId => {
        const element = document.getElementById(fieldId);
        if (element) {
            foundFields.push(fieldId);
            console.log(`   ✅ ${fieldId}: trovato (${element.type || element.tagName})`);
        } else {
            missingFields.push(fieldId);
            console.log(`   ❌ ${fieldId}: NON trovato`);
        }
    });
    
    console.log(`\n   Campi trovati: ${foundFields.length}/${databaseFields.length}`);
    
    if (missingFields.length > 0) {
        console.log(`   ⚠️ Campi mancanti: ${missingFields.join(', ')}`);
        return false;
    }
    
    return true;
}

// 2. Test funzione getFieldValue
function testGetFieldValue() {
    console.log("\n2. Test funzione getFieldValue:");
    console.log("-".repeat(40));
    
    if (typeof getFieldValue !== 'function') {
        console.log("   ❌ Funzione getFieldValue NON trovata");
        return false;
    }
    
    console.log("   ✅ Funzione getFieldValue trovata");
    
    // Test su alcuni campi
    const testFields = [
        { id: 'db-backup-schedule', type: 'text' },
        { id: 'db-backup-retention', type: 'number' },
        { id: 'db-compress-backup', type: 'checkbox' }
    ];
    
    testFields.forEach(field => {
        try {
            const value = getFieldValue(field.id, field.type === 'checkbox' ? 'checkbox' : undefined);
            console.log(`   ✅ ${field.id}: "${value}" (${typeof value})`);
        } catch (error) {
            console.log(`   ❌ ${field.id}: ERRORE - ${error.message}`);
        }
    });
    
    return true;
}

// 3. Test raccolta configurazioni database
function testDatabaseCollection() {
    console.log("\n3. Test raccolta configurazioni database:");
    console.log("-".repeat(40));
    
    try {
        // Simula la raccolta come fa collectConfigurationData()
        const databaseConfig = {
            backup_schedule: getFieldValue('db-backup-schedule'),
            backup_time: getFieldValue('db-backup-time'),
            backup_retention: parseInt(getFieldValue('db-backup-retention')) || 30,
            backup_path: getFieldValue('db-backup-path'),
            compress_backup: getFieldValue('db-compress-backup', 'checkbox'),
            log_cleanup: getFieldValue('db-log-cleanup'),
            archive_months: parseInt(getFieldValue('db-archive-months')) || 12,
            optimize: getFieldValue('db-optimize'),
            auto_vacuum: getFieldValue('db-auto-vacuum', 'checkbox'),
            analyze: getFieldValue('db-analyze', 'checkbox'),
            disk_threshold: parseInt(getFieldValue('db-disk-threshold')) || 85,
            connection_threshold: parseInt(getFieldValue('db-connection-threshold')) || 80,
            monitor_performance: getFieldValue('db-monitor-performance', 'checkbox'),
            alert_email: getFieldValue('db-alert-email', 'checkbox')
        };
        
        console.log("   ✅ Configurazioni database raccolte:");
        Object.entries(databaseConfig).forEach(([key, value]) => {
            console.log(`     ${key}: ${value} (${typeof value})`);
        });
        
        console.log(`\n   Totale configurazioni: ${Object.keys(databaseConfig).length}`);
        
        return databaseConfig;
        
    } catch (error) {
        console.log(`   ❌ ERRORE raccolta: ${error.message}`);
        console.log(`   Stack: ${error.stack}`);
        return null;
    }
}

// 4. Test funzione collectConfigurationData completa
function testCollectConfigurationData() {
    console.log("\n4. Test collectConfigurationData completa:");
    console.log("-".repeat(40));
    
    if (typeof collectConfigurationData !== 'function') {
        console.log("   ❌ Funzione collectConfigurationData NON trovata");
        return null;
    }
    
    try {
        const allConfigs = collectConfigurationData();
        
        if (allConfigs && allConfigs.database) {
            console.log("   ✅ Sezione database presente in collectConfigurationData");
            console.log(`   Database configs: ${Object.keys(allConfigs.database).length} campi`);
            
            // Mostra primi 5 campi
            const dbEntries = Object.entries(allConfigs.database);
            dbEntries.slice(0, 5).forEach(([key, value]) => {
                console.log(`     ${key}: ${value}`);
            });
            
            if (dbEntries.length > 5) {
                console.log(`     ... e altri ${dbEntries.length - 5} campi`);
            }
        } else {
            console.log("   ❌ Sezione database MANCANTE in collectConfigurationData");
        }
        
        return allConfigs;
        
    } catch (error) {
        console.log(`   ❌ ERRORE collectConfigurationData: ${error.message}`);
        return null;
    }
}

// 5. Test salvataggio simulato
function testSaveSimulation(configurations) {
    console.log("\n5. Test salvataggio simulato:");
    console.log("-".repeat(40));
    
    if (!configurations || !configurations.database) {
        console.log("   ❌ Nessuna configurazione database da salvare");
        return false;
    }
    
    console.log("   📤 Simulazione chiamata API:");
    console.log("   URL: POST /admin/api/configurations");
    console.log("   Headers: Content-Type: application/json");
    
    const payload = JSON.stringify(configurations, null, 2);
    console.log(`   Payload size: ${payload.length} caratteri`);
    console.log("   Payload preview:");
    console.log(payload.substring(0, 300) + "...");
    
    return true;
}

// 6. Test salvataggio reale
async function testRealSave() {
    console.log("\n6. Test salvataggio REALE:");
    console.log("-".repeat(40));
    
    try {
        // Raccogli configurazioni
        const configurations = collectConfigurationData();
        
        if (!configurations || !configurations.database) {
            console.log("   ❌ Impossibile raccogliere configurazioni database");
            return false;
        }
        
        console.log("   📤 Invio richiesta API...");
        
        const response = await fetch('/admin/api/configurations', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(configurations)
        });
        
        console.log(`   📥 Risposta ricevuta: ${response.status} ${response.statusText}`);
        
        const data = await response.json();
        console.log("   📄 Dati risposta:", data);
        
        if (data.success) {
            console.log("   ✅ SALVATAGGIO RIUSCITO!");
        } else {
            console.log(`   ❌ SALVATAGGIO FALLITO: ${data.message}`);
        }
        
        return data.success;
        
    } catch (error) {
        console.log(`   ❌ ERRORE salvataggio: ${error.message}`);
        return false;
    }
}

// Funzione principale di debug
async function debugDatabaseConfig() {
    console.log("🚀 AVVIO DEBUG CONFIGURAZIONI DATABASE");
    console.log("=" * 60);
    
    const results = [];
    
    // Test 1: Campi DOM
    const fieldsOk = checkDatabaseFields();
    results.push(['Campi DOM', fieldsOk]);
    
    // Test 2: Funzione getFieldValue
    const getFieldOk = testGetFieldValue();
    results.push(['getFieldValue', getFieldOk]);
    
    // Test 3: Raccolta database
    const databaseConfig = testDatabaseCollection();
    results.push(['Raccolta Database', databaseConfig !== null]);
    
    // Test 4: collectConfigurationData
    const allConfigs = testCollectConfigurationData();
    results.push(['collectConfigurationData', allConfigs !== null]);
    
    // Test 5: Simulazione
    const simOk = testSaveSimulation(allConfigs);
    results.push(['Simulazione Save', simOk]);
    
    // Test 6: Salvataggio reale (opzionale)
    console.log("\n❓ Vuoi testare il salvataggio REALE? (può modificare i dati)");
    console.log("   Per testare, esegui: testRealSave()");
    
    // Riepilogo
    console.log("\n" + "=" * 60);
    console.log("📊 RIEPILOGO DEBUG");
    console.log("=" * 60);
    
    let passed = 0;
    results.forEach(([test, result]) => {
        const status = result ? "✅ PASS" : "❌ FAIL";
        console.log(`${test.padEnd(25)} ${status}`);
        if (result) passed++;
    });
    
    console.log(`\nRisultato: ${passed}/${results.length} test passati`);
    
    if (passed === results.length) {
        console.log("\n🎉 TUTTI I TEST SONO PASSATI!");
        console.log("Il problema potrebbe essere nel salvataggio reale.");
        console.log("Esegui testRealSave() per testare il salvataggio completo.");
    } else {
        console.log("\n⚠️ ALCUNI TEST HANNO FALLITO");
        console.log("Controlla i test falliti per identificare il problema.");
    }
}

// Avvia il debug automaticamente
debugDatabaseConfig();

// Esponi funzioni per test manuali
window.debugDatabaseConfig = debugDatabaseConfig;
window.testRealSave = testRealSave;
window.checkDatabaseFields = checkDatabaseFields;
window.testDatabaseCollection = testDatabaseCollection;
