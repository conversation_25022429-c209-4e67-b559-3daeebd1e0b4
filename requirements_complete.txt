# Dipendenze complete per l'applicazione SNIP con sistema 2FA SMS

# Framework web
fastapi>=0.115.0
uvicorn>=0.34.0
starlette>=0.46.0

# Database
sqlalchemy>=2.0.0
psycopg2-binary>=2.9.0
pydantic>=2.11.0
pydantic-settings>=2.10.0

# Autenticazione e sicurezza
python-jose[cryptography]>=3.5.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.20

# Template e rendering
jinja2>=3.1.0

# Generazione documenti
reportlab>=4.0.0
python-docx>=1.2.0

# Sistema 2FA SMS
requests>=2.32.0

# Utilità
python-dotenv>=1.1.0
typing-extensions>=4.14.0

# Dipendenze opzionali per provider SMS professionali
# twilio>=8.0.0
# smsapi-client>=2.0.0
