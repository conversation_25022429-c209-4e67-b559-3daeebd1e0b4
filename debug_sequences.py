#!/usr/bin/env python3
"""
Debug sequenze nel backup
"""

import gzip
from pathlib import Path

def debug_sequences():
    print("DEBUG SEQUENZE NEL BACKUP")
    print("=" * 40)
    
    # Trova ultimo backup
    backup_dir = Path("backups")
    backup_files = list(backup_dir.glob("snip_backup_*.sql.gz"))
    latest_backup = max(backup_files, key=lambda f: f.stat().st_mtime)
    
    with gzip.open(latest_backup, 'rt', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    
    # Cerca tutte le righe con CREATE SEQUENCE
    print("CREATE SEQUENCE trovate:")
    print("-" * 30)
    
    sequence_lines = []
    for i, line in enumerate(lines, 1):
        if 'CREATE SEQUENCE' in line:
            sequence_lines.append((i, line.strip()))
    
    if sequence_lines:
        for line_num, line_content in sequence_lines:
            print(f"Riga {line_num}: {line_content}")
    else:
        print("NESSUNA CREATE SEQUENCE TROVATA!")
    
    # Cerca tutte le righe con setval
    print(f"\nsetval() trovate:")
    print("-" * 20)
    
    setval_lines = []
    for i, line in enumerate(lines, 1):
        if 'setval(' in line:
            setval_lines.append((i, line.strip()))
    
    if setval_lines:
        for line_num, line_content in setval_lines:
            print(f"Riga {line_num}: {line_content}")
    else:
        print("NESSUNA setval() TROVATA!")
    
    # Cerca sezione sequenze
    print(f"\nSEZIONE SEQUENZE:")
    print("-" * 20)
    
    in_sequences = False
    sequence_section = []
    
    for i, line in enumerate(lines, 1):
        if "-- Sequences" in line:
            in_sequences = True
            sequence_section.append((i, line))
        elif in_sequences and line.strip() and not line.startswith('--'):
            sequence_section.append((i, line))
            if len(sequence_section) > 20:  # Limita output
                break
        elif in_sequences and "-- Data for Name:" in line:
            break
    
    if sequence_section:
        print("Sezione sequenze trovata:")
        for line_num, line_content in sequence_section:
            print(f"Riga {line_num}: {line_content}")
    else:
        print("SEZIONE SEQUENZE NON TROVATA!")
    
    # Statistiche generali
    print(f"\nSTATISTICHE:")
    print("-" * 15)
    print(f"CREATE SEQUENCE: {content.count('CREATE SEQUENCE')}")
    print(f"setval: {content.count('setval')}")
    print(f"AGENTE_id_user_seq: {content.count('AGENTE_id_user_seq')}")
    print(f"public.: {content.count('public.')}")

if __name__ == "__main__":
    debug_sequences()
