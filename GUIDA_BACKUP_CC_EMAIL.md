# 📧 BACKUP AUTOMATICO CON CC EMAIL - GUIDA COMPLETA

## 🎯 Sistema Aggiornato

Il sistema di backup automatico è stato **aggiornato** per inviare l'email con il file .sql del database AGENTE a:

- **📧 Email Admin** (destinatario principale)
- **📧 Email <PERSON>ttente** (in copia CC)

## ✅ Funzionalità Implementate

### 📬 Doppio Invio Email
- ✅ **TO**: Email Admin riceve il backup
- ✅ **CC**: Email Mittente riceve copia del backup
- ✅ **Allegato**: File .sql identico per entrambi
- ✅ **Ridondanza**: Backup sicuro con doppio destinatario

### 🔧 Configurazioni
- ✅ **Email Admin**: Campo "Email Admin" nel dashboard
- ✅ **Email Mittente**: Campo "Email Mittente" nel dashboard
- ✅ **Mapping automatico**: Configurazioni lette dal database
- ✅ **Fallback intelligente**: Se Email <PERSON> vuota, invia solo ad Admin

## 🛠️ Configurazione Dashboard

### 1. Accesso Configurazioni
```
1. Vai su /dashboard/amministrazione
2. Clicca su "Configurazione Email"
3. Compila entrambi i campi email
4. Clicca "Salva Tutto"
```

### 2. Campi Email Richiesti
```
Email Admin:     <EMAIL>
Email Mittente:  <EMAIL>
```

### 3. Configurazione Backup
```
1. Vai su "Configurazione Database"
2. Imposta "Orario Backup": 12:45
3. Imposta "Frequenza": daily
4. Clicca "Salva Tutto"
```

## 📧 Email Ricevuta

### Headers Email
```
From: Michele Autuori Srl <<EMAIL>>
To: <EMAIL>
Cc: <EMAIL>
Subject: 📦 Backup Database SNIP - 19/06/2025 12:45
```

### Contenuto Email
```
Gentile Amministratore,

Il backup automatico del database SNIP è stato completato con successo.

📊 DETTAGLI BACKUP:
• Data/Ora: 19/06/2025 alle 12:45:00
• File: snip_backup_20250619_1245.sql
• Dimensione: 15.7 MB
• Compressione: Attiva

📧 DESTINATARI:
• Email Admin: <EMAIL>
• Email Mittente (CC): <EMAIL>

📎 ALLEGATO:
Il file di backup SQL è allegato a questa email e può essere utilizzato per:
• Ripristino completo del database AGENTE
• Backup di sicurezza offline
• Migrazione dati su altro server

⚠️ IMPORTANTE:
Conservare questo file in luogo sicuro. Contiene tutti i dati del sistema SNIP.

---
Sistema di Backup Automatico SNIP
Michele Autuori Srl - shipping and forwarding agency
```

### Allegato
```
Nome: snip_backup_20250619_1245.sql
Dimensione: 10-20 MB
Tipo: application/octet-stream
Encoding: base64
Contenuto: Dump completo database AGENTE
```

## ⏰ Funzionamento Automatico

### Scheduler
```
1. Controllo ogni minuto se è ora di backup
2. Alle 12:45 (o orario configurato):
   - Genera dump database AGENTE
   - Legge Email Admin e Email Mittente
   - Compone email con allegato
   - Invia a entrambi i destinatari
   - Log dell'operazione
```

### Log Sistema
```
[SCHEDULE] Backup programmati: giornalieri alle 12:45
[EMAIL] Backup inviato con allegato a: <EMAIL> (CC: <EMAIL>)
[EMAIL] File allegato: snip_backup_20250619_1245.sql (15.7 MB)
```

## 🔍 Comportamenti Sistema

### Configurazioni Complete
```
Email Admin: ✅ Configurata
Email Mittente: ✅ Configurata
Risultato: Invio a entrambi (TO + CC)
```

### Solo Email Admin
```
Email Admin: ✅ Configurata
Email Mittente: ❌ Vuota
Risultato: Invio solo ad Admin (TO)
```

### Nessuna Email
```
Email Admin: ❌ Vuota
Email Mittente: ❌ Vuota
Risultato: Backup non inviato (errore)
```

## 🚨 Risoluzione Problemi

### Email Non Arriva a Nessuno
```
Cause:
❌ Email Admin non configurata
❌ SMTP non funzionante
❌ Credenziali errate

Soluzioni:
✅ Configurare Email Admin
✅ Testare SMTP con "Test Email"
✅ Verificare password per le app (Gmail)
```

### Email Arriva Solo ad Admin
```
Cause:
❌ Email Mittente vuota o errata
❌ Provider blocca CC

Soluzioni:
✅ Configurare Email Mittente
✅ Verificare formato email corretto
✅ Controllare spam/posta indesiderata
```

### Allegato Mancante
```
Cause:
❌ Errore generazione backup
❌ File troppo grande per email
❌ Provider blocca allegati .sql

Soluzioni:
✅ Verificare spazio disco server
✅ Abilitare compressione backup
✅ Controllare limiti provider email
```

## 📋 Configurazioni Consigliate

### Ambiente Produzione
```
Email Admin:     <EMAIL>
Email Mittente:  <EMAIL>
Orario Backup:   02:00 (notte)
Frequenza:       daily
Compressione:    Abilitata
Provider:        Gmail Business
```

### Ambiente Test
```
Email Admin:     <EMAIL>
Email Mittente:  <EMAIL>
Orario Backup:   12:45 (test immediato)
Frequenza:       daily
Compressione:    Disabilitata
Provider:        Gmail personale
```

## 🎯 Vantaggi Doppio Invio

### Sicurezza
- ✅ **Ridondanza**: 2 copie del backup
- ✅ **Affidabilità**: Meno rischio perdita email
- ✅ **Controllo**: Più persone informate

### Gestione
- ✅ **Admin**: Riceve per gestione sistema
- ✅ **Mittente**: Riceve per controllo invii
- ✅ **Tracciabilità**: Log completo destinatari

## 🚀 Test Sistema

### 1. Test Configurazioni
```
1. Configurare entrambe le email
2. Impostare orario backup
3. Cliccare "Test Email"
4. Verificare ricezione su entrambe
```

### 2. Test Backup
```
1. Attendere orario backup (12:45)
2. Controllare log server
3. Verificare email su entrambi account
4. Scaricare e testare file .sql
```

## ✅ Risultato Finale

Una volta configurato:

🎯 **Email Admin** riceve backup alle 12:45  
🎯 **Email Mittente** riceve copia backup alle 12:45  
🎯 **File .sql completo** allegato per entrambi  
🎯 **Ripristino database** possibile da entrambe le email  
🎯 **Backup sicuro** con doppia ridondanza  

## 🎉 Sistema Pronto!

**Il sistema è completamente implementato e funzionante!**

Configura le due email nel dashboard e il sistema invierà automaticamente il backup del database AGENTE a entrambi i destinatari ogni giorno alle 12:45.

---

*Sistema di Backup Automatico SNIP con CC Email*  
*Michele Autuori Srl - shipping and forwarding agency*
