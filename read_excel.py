#!/usr/bin/env python3
"""
Script per leggere e analizzare il file Excel gioia12.xlsx
"""

import pandas as pd
import sys
import os

def read_excel_file(file_path):
    """Legge e analizza il file Excel"""
    
    if not os.path.exists(file_path):
        print(f"❌ File non trovato: {file_path}")
        return
    
    try:
        print(f"📖 Lettura del file: {file_path}")
        print("=" * 60)
        
        # Leggi il file Excel
        # Prima proviamo a vedere tutti i fogli disponibili
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        
        print(f"📋 Fogli disponibili nel file:")
        for i, sheet in enumerate(sheet_names, 1):
            print(f"   {i}. {sheet}")
        
        print("\n" + "=" * 60)
        
        # Leggi ogni foglio
        for sheet_name in sheet_names:
            print(f"\n📄 FOGLIO: {sheet_name}")
            print("-" * 40)
            
            try:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                print(f"📊 Dimensioni: {df.shape[0]} righe x {df.shape[1]} colonne")
                
                if not df.empty:
                    print(f"\n🏷️ Colonne:")
                    for i, col in enumerate(df.columns, 1):
                        print(f"   {i}. {col}")
                    
                    print(f"\n📋 Prime 10 righe:")
                    print(df.head(10).to_string(index=True, max_cols=None))
                    
                    # Mostra informazioni sui tipi di dati
                    print(f"\n📈 Informazioni sui dati:")
                    print(df.info())
                    
                    # Mostra statistiche descrittive per colonne numeriche
                    numeric_cols = df.select_dtypes(include=['number']).columns
                    if len(numeric_cols) > 0:
                        print(f"\n📊 Statistiche colonne numeriche:")
                        print(df[numeric_cols].describe())
                    
                else:
                    print("   ⚠️ Foglio vuoto")
                    
            except Exception as e:
                print(f"   ❌ Errore lettura foglio '{sheet_name}': {e}")
        
    except Exception as e:
        print(f"❌ Errore lettura file Excel: {e}")
        print(f"💡 Assicurati di avere pandas e openpyxl installati:")
        print(f"   pip install pandas openpyxl")

def analyze_for_update():
    """Analizza il file per capire quale foglio usare per l'aggiornamento"""
    file_path = r"c:\Users\<USER>\Desktop\cline_thebest\gioia12.xlsx"

    try:
        excel_file = pd.ExcelFile(file_path)

        print("🔍 ANALISI PER AGGIORNAMENTO DATABASE")
        print("=" * 50)

        for sheet_name in excel_file.sheet_names:
            print(f"\n📄 Foglio: {sheet_name}")
            df = pd.read_excel(file_path, sheet_name=sheet_name)

            print(f"   Dimensioni: {df.shape[0]} righe x {df.shape[1]} colonne")
            print(f"   Colonne: {list(df.columns)}")

            # Mostra prime 5 righe per capire la struttura
            print(f"   Prime 5 righe:")
            for i in range(min(5, len(df))):
                row = df.iloc[i]
                print(f"     {i+1}. Colonna A: '{row.iloc[0]}' | Colonna B: '{row.iloc[1] if len(row) > 1 else 'N/A'}'")

    except Exception as e:
        print(f"❌ Errore: {e}")

def main():
    file_path = r"c:\Users\<USER>\Desktop\cline_thebest\gioia12.xlsx"
    analyze_for_update()

if __name__ == "__main__":
    main()
