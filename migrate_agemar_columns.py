#!/usr/bin/env python3
"""
Script per modificare la colonna Agemar in Agemar_Salerno e aggiungere Agemar_GioiaTauro
"""

from sqlalchemy import text
from database import SessionLocal
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_agemar_columns():
    """Migra la colonna Agemar in Agemar_Salerno e aggiunge Agemar_GioiaTauro"""
    
    db = SessionLocal()
    
    try:
        print("🔧 MIGRAZIONE COLONNE AGEMAR")
        print("=" * 50)
        
        # Passo 1: Verifica struttura attuale
        print("\n1. Verifica struttura attuale...")
        
        result = db.execute(text("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'NAVI' AND column_name LIKE '%gemar%'
            ORDER BY ordinal_position
        """))
        
        current_columns = result.fetchall()
        print("   Colonne <PERSON>mar attuali:")
        for col in current_columns:
            nullable = "NULL" if col[2] == "YES" else "NOT NULL"
            print(f"      - {col[0]}: {col[1]} {nullable}")
        
        # Passo 2: Rinomina Agemar in Agemar_Salerno
        print("\n2. Rinomina Agemar in Agemar_Salerno...")
        
        # Verifica se la colonna Agemar esiste
        agemar_exists = any(col[0] == "Agemar" for col in current_columns)
        agemar_salerno_exists = any(col[0] == "Agemar_Salerno" for col in current_columns)
        
        if agemar_exists and not agemar_salerno_exists:
            db.execute(text('''
                ALTER TABLE "NAVI" 
                RENAME COLUMN "Agemar" TO "Agemar_Salerno"
            '''))
            print("   ✅ Colonna Agemar rinominata in Agemar_Salerno")
        elif agemar_salerno_exists:
            print("   ⚠️ Colonna Agemar_Salerno già esistente")
        else:
            print("   ❌ Colonna Agemar non trovata")
        
        # Passo 3: Aggiungi colonna Agemar_GioiaTauro
        print("\n3. Aggiungi colonna Agemar_GioiaTauro...")
        
        agemar_gioia_exists = any(col[0] == "Agemar_GioiaTauro" for col in current_columns)
        
        if not agemar_gioia_exists:
            db.execute(text('''
                ALTER TABLE "NAVI" 
                ADD COLUMN "Agemar_GioiaTauro" NUMERIC(12,2)
            '''))
            print("   ✅ Colonna Agemar_GioiaTauro aggiunta")
        else:
            print("   ⚠️ Colonna Agemar_GioiaTauro già esistente")
        
        # Commit delle modifiche
        db.commit()
        print("\n4. Commit modifiche...")
        print("   ✅ Modifiche salvate nel database")
        
        # Passo 5: Verifica struttura finale
        print("\n5. Verifica struttura finale...")
        
        result = db.execute(text("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'NAVI' AND (column_name LIKE '%gemar%' OR column_name = 'TSL')
            ORDER BY ordinal_position
        """))
        
        final_columns = result.fetchall()
        print("   Colonne finali:")
        for col in final_columns:
            nullable = "NULL" if col[2] == "YES" else "NOT NULL"
            print(f"      - {col[0]}: {col[1]} {nullable}")
        
        # Passo 6: Test con alcune navi
        print("\n6. Test lettura dati...")
        
        result = db.execute(text('''
            SELECT "Nave", "Agemar_Salerno", "Agemar_GioiaTauro", "TSL"
            FROM "NAVI"
            WHERE "Nave" IS NOT NULL
            ORDER BY "Nave"
            LIMIT 5
        '''))
        
        test_navi = result.fetchall()
        print("   Prime 5 navi:")
        for nave in test_navi:
            salerno = f"{nave[1]:.2f}" if nave[1] else "NULL"
            gioia = f"{nave[2]:.2f}" if nave[2] else "NULL"
            tsl = f"{nave[3]:.2f}" if nave[3] else "NULL"
            print(f"      {nave[0]}: Salerno={salerno}, Gioia={gioia}, TSL={tsl}")
        
        print("\n🎉 MIGRAZIONE COMPLETATA CON SUCCESSO!")
        return True
        
    except Exception as e:
        print(f"\n💥 ERRORE: {str(e)}")
        db.rollback()
        return False
        
    finally:
        db.close()

def test_migration():
    """Test per verificare che la migrazione sia andata a buon fine"""
    
    db = SessionLocal()
    
    try:
        print("\n🧪 TEST MIGRAZIONE")
        print("=" * 30)
        
        # Test 1: Verifica colonne
        result = db.execute(text("""
            SELECT column_name
            FROM information_schema.columns 
            WHERE table_name = 'NAVI' AND column_name IN ('Agemar_Salerno', 'Agemar_GioiaTauro', 'TSL')
            ORDER BY column_name
        """))
        
        columns = [row[0] for row in result]
        
        expected = ['Agemar_GioiaTauro', 'Agemar_Salerno', 'TSL']
        missing = [col for col in expected if col not in columns]
        
        if not missing:
            print("✅ Tutte le colonne presenti")
        else:
            print(f"❌ Colonne mancanti: {missing}")
            return False
        
        # Test 2: Test inserimento
        print("\nTest inserimento dati...")
        
        test_data = {
            "nave": "TEST AGEMAR MIGRATION",
            "codice": "TESTAGE",
            "prefisso": "TAG",
            "agemar_salerno": 1500.50,
            "agemar_gioia": 1200.75,
            "tsl": 25000.00
        }
        
        db.execute(text('''
            INSERT INTO "NAVI" ("Nave", "Codice_Nave", "Prefisso_viaggio", "Agemar_Salerno", "Agemar_GioiaTauro", "TSL")
            VALUES (:nave, :codice, :prefisso, :agemar_salerno, :agemar_gioia, :tsl)
        '''), test_data)
        
        # Test lettura
        result = db.execute(text('''
            SELECT "Agemar_Salerno", "Agemar_GioiaTauro", "TSL"
            FROM "NAVI"
            WHERE "Codice_Nave" = :codice
        '''), {"codice": test_data["codice"]})
        
        row = result.fetchone()
        
        if row:
            print(f"✅ Dati inseriti: Salerno={row[0]}, Gioia={row[1]}, TSL={row[2]}")
        else:
            print("❌ Errore lettura dati")
            return False
        
        # Cleanup
        db.execute(text('''
            DELETE FROM "NAVI" WHERE "Codice_Nave" = :codice
        '''), {"codice": test_data["codice"]})
        
        db.commit()
        print("✅ Test completato e cleanup effettuato")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore test: {str(e)}")
        db.rollback()
        return False
        
    finally:
        db.close()

if __name__ == "__main__":
    print("🚢 MIGRAZIONE COLONNE AGEMAR")
    print("\nQuesta operazione:")
    print("1. Rinominerà 'Agemar' in 'Agemar_Salerno'")
    print("2. Aggiungerà la colonna 'Agemar_GioiaTauro'")
    print("3. Manterrà tutti i dati esistenti")
    print()
    
    risposta = input("Vuoi procedere con la migrazione? (s/N): ").lower()
    if risposta in ['s', 'si', 'y', 'yes']:
        success = migrate_agemar_columns()
        if success:
            print("\n" + "="*50)
            test_migration()
        else:
            print("\n❌ Migrazione fallita!")
    else:
        print("Migrazione annullata.")
