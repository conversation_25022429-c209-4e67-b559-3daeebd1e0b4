#!/usr/bin/env python3
"""
Script per verificare i vincoli della tabella DEPARTMENT_NOTIFICATIONS
"""

import psycopg2
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_constraints():
    """Verifica i vincoli della tabella DEPARTMENT_NOTIFICATIONS"""
    try:
        # Connessione al database
        conn = psycopg2.connect(
            host='localhost',
            database='AGENTE',
            user='re77',
            password='271077',
            connect_timeout=5
        )
        conn.autocommit = False
        cursor = conn.cursor()
        
        logger.info("✅ Connesso al database")
        
        # Verifica i vincoli della tabella
        cursor.execute("""
            SELECT conname, consrc 
            FROM pg_constraint 
            WHERE conrelid = (
                SELECT oid 
                FROM pg_class 
                WHERE relname = 'DEPARTMENT_NOTIFICATIONS'
            )
            AND contype = 'c';
        """)
        
        constraints = cursor.fetchall()
        
        logger.info("📋 Vincoli della tabella DEPARTMENT_NOTIFICATIONS:")
        for constraint_name, constraint_src in constraints:
            logger.info(f"   • {constraint_name}: {constraint_src}")
        
        # Verifica i valori accettati per notification_type
        if any('notification_type' in src for _, src in constraints):
            logger.info("\n🔍 Analisi vincolo notification_type:")
            for constraint_name, constraint_src in constraints:
                if 'notification_type' in constraint_src:
                    logger.info(f"   Vincolo: {constraint_src}")
                    
                    # Estrai i valori accettati
                    if 'IN' in constraint_src.upper():
                        start = constraint_src.upper().find('IN')
                        values_part = constraint_src[start:]
                        logger.info(f"   Valori accettati: {values_part}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore durante la verifica: {e}")
        return False
        
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def fix_notification_types():
    """Corregge i tipi di notifica per essere compatibili con i vincoli"""
    try:
        # Connessione al database
        conn = psycopg2.connect(
            host='localhost',
            database='AGENTE',
            user='re77',
            password='271077',
            connect_timeout=5
        )
        conn.autocommit = False
        cursor = conn.cursor()
        
        logger.info("🔧 Correzione tipi di notifica...")
        
        # Aggiorna il vincolo per accettare i tipi usati dal sistema
        cursor.execute("""
            ALTER TABLE "DEPARTMENT_NOTIFICATIONS" 
            DROP CONSTRAINT IF EXISTS chk_notification_type;
        """)
        
        cursor.execute("""
            ALTER TABLE "DEPARTMENT_NOTIFICATIONS" 
            ADD CONSTRAINT chk_notification_type 
            CHECK (notification_type IN ('info', 'warning', 'error', 'success', 'INFO', 'WARNING', 'ERROR', 'SUCCESS'));
        """)
        
        conn.commit()
        logger.info("✅ Vincolo notification_type aggiornato!")
        
        # Test con i nuovi valori
        test_values = ['info', 'warning', 'error', 'success', 'INFO', 'WARNING', 'ERROR', 'SUCCESS']
        
        for test_value in test_values:
            try:
                cursor.execute("""
                    INSERT INTO "DEPARTMENT_NOTIFICATIONS"
                    (title, message, notification_type, target_reparto, created_by, priority, send_email, is_active)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING id;
                """, (
                    f"Test {test_value}",
                    f"Test notifica con tipo {test_value}",
                    test_value,
                    "OPERATIVO",
                    1,
                    1,
                    False,
                    True
                ))
                
                notification_id = cursor.fetchone()[0]
                logger.info(f"✅ Test {test_value}: OK (ID: {notification_id})")
                
                # Rimuovi la notifica di test
                cursor.execute("DELETE FROM \"DEPARTMENT_NOTIFICATIONS\" WHERE id = %s", (notification_id,))
                
            except Exception as e:
                logger.error(f"❌ Test {test_value}: FALLITO - {e}")
        
        conn.commit()
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore durante la correzione: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
        
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    logger.info("🔍 VERIFICA VINCOLI TABELLA NOTIFICHE")
    logger.info("=" * 50)
    
    # Verifica i vincoli attuali
    check_constraints()
    
    logger.info("\n🔧 CORREZIONE VINCOLI")
    logger.info("=" * 30)
    
    # Correggi i vincoli
    success = fix_notification_types()
    
    if success:
        logger.info("\n🎉 CORREZIONE COMPLETATA CON SUCCESSO!")
        logger.info("I tipi di notifica sono ora compatibili con il sistema.")
    else:
        logger.error("\n❌ CORREZIONE FALLITA!")
    
    exit(0 if success else 1)
