<!DOCTYPE html>
<html>
<head>
    <title>Test Completo Porti</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🧪 Test Completo Sistema Porti SNIP</h1>
    
    <div class="test-section">
        <h3>📋 Istruzioni</h3>
        <ol>
            <li>Apri questo file nel browser</li>
            <li>Clicca sui pulsanti di test in ordine</li>
            <li>Controlla i risultati</li>
            <li>Copia i log e inviameli</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>🔧 Test 1: API ATLAS</h3>
        <button onclick="testAPIAtlas()">Test API ATLAS</button>
        <div id="atlas-result"></div>
    </div>

    <div class="test-section">
        <h3>🚢 Test 2: API Viaggio</h3>
        <input type="number" id="viaggio-id" placeholder="ID Viaggio (es. 30)" value="30">
        <button onclick="testAPIViaggio()">Test API Viaggio</button>
        <div id="viaggio-result"></div>
    </div>

    <div class="test-section">
        <h3>📝 Test 3: Creazione Viaggio</h3>
        <button onclick="testCreazioneViaggio()">Test Creazione Viaggio</button>
        <div id="creazione-result"></div>
    </div>

    <div class="test-section">
        <h3>📊 Risultati Completi</h3>
        <button onclick="copyLogs()">📋 Copia Tutti i Log</button>
        <pre id="all-logs"></pre>
    </div>

    <script>
        let allLogs = [];

        function log(message) {
            console.log(message);
            allLogs.push(new Date().toISOString() + ': ' + message);
            document.getElementById('all-logs').textContent = allLogs.join('\n');
        }

        async function testAPIAtlas() {
            log('🧪 TEST API ATLAS');
            const resultDiv = document.getElementById('atlas-result');
            
            try {
                const response = await fetch('http://localhost:8002/api/atlas?limit=5');
                log(`📡 Status: ${response.status}`);
                
                if (response.status === 200) {
                    const data = await response.json();
                    log(`✅ Success: ${data.success}`);
                    log(`📋 Porti trovati: ${data.data ? data.data.length : 0}`);
                    
                    if (data.data && data.data.length > 0) {
                        log('🎯 Primi 3 porti:');
                        data.data.slice(0, 3).forEach((porto, i) => {
                            log(`   ${i+1}. ${porto.porto} (${porto.stato}) - ID: ${porto.id_cod}`);
                        });
                        resultDiv.className = 'test-section success';
                        resultDiv.innerHTML = '<h4>✅ API ATLAS Funziona</h4><p>Trovati ' + data.data.length + ' porti</p>';
                    } else {
                        resultDiv.className = 'test-section error';
                        resultDiv.innerHTML = '<h4>❌ API ATLAS Non Restituisce Dati</h4>';
                    }
                } else {
                    log(`❌ Errore HTTP: ${response.status}`);
                    resultDiv.className = 'test-section error';
                    resultDiv.innerHTML = '<h4>❌ Errore API ATLAS</h4><p>Status: ' + response.status + '</p>';
                }
            } catch (error) {
                log(`💥 Errore: ${error.message}`);
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = '<h4>❌ Errore Connessione</h4><p>' + error.message + '</p>';
            }
        }

        async function testAPIViaggio() {
            const viaggioId = document.getElementById('viaggio-id').value;
            log(`🧪 TEST API VIAGGIO ID: ${viaggioId}`);
            const resultDiv = document.getElementById('viaggio-result');
            
            try {
                const response = await fetch(`http://localhost:8002/api/viaggi/${viaggioId}`);
                log(`📡 Status: ${response.status}`);
                
                if (response.status === 200) {
                    const data = await response.json();
                    log(`✅ Success: ${data.success}`);
                    
                    if (data.success && data.data) {
                        const viaggio = data.data;
                        log('🎯 DATI VIAGGIO:');
                        log(`   ID: ${viaggio.id}`);
                        log(`   Codice: ${viaggio.viaggio}`);
                        log(`   Porto Arrivo: ${viaggio.porto_arrivo || 'NULL'}`);
                        log(`   Porto Destinazione: ${viaggio.porto_destinazione || 'NULL'}`);
                        log(`   Nome Nave: ${viaggio.nome_nave}`);
                        
                        if (viaggio.porto_arrivo || viaggio.porto_destinazione) {
                            resultDiv.className = 'test-section success';
                            resultDiv.innerHTML = '<h4>✅ Viaggio Ha Porti Salvati</h4><p>Arrivo: ' + (viaggio.porto_arrivo || 'N/A') + '<br>Destinazione: ' + (viaggio.porto_destinazione || 'N/A') + '</p>';
                        } else {
                            resultDiv.className = 'test-section error';
                            resultDiv.innerHTML = '<h4>❌ Viaggio NON Ha Porti Salvati</h4><p>I porti non sono stati salvati nel database</p>';
                        }
                    } else {
                        resultDiv.className = 'test-section error';
                        resultDiv.innerHTML = '<h4>❌ API Non Restituisce Dati Validi</h4>';
                    }
                } else {
                    log(`❌ Errore HTTP: ${response.status}`);
                    resultDiv.className = 'test-section error';
                    resultDiv.innerHTML = '<h4>❌ Errore API Viaggio</h4><p>Status: ' + response.status + '</p>';
                }
            } catch (error) {
                log(`💥 Errore: ${error.message}`);
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = '<h4>❌ Errore Connessione</h4><p>' + error.message + '</p>';
            }
        }

        async function testCreazioneViaggio() {
            log('🧪 TEST CREAZIONE VIAGGIO');
            const resultDiv = document.getElementById('creazione-result');
            
            try {
                // Prima ottieni i dati necessari
                const atlasResponse = await fetch('http://localhost:8002/api/atlas?limit=5');
                const atlasData = await atlasResponse.json();
                
                if (!atlasData.success || !atlasData.data.length) {
                    throw new Error('Impossibile ottenere dati ATLAS');
                }

                const portoArrivo = atlasData.data[0].id_cod;
                const portoDestinazione = atlasData.data[1].id_cod;
                
                log(`🎯 Test con porti: ${portoArrivo} -> ${portoDestinazione}`);

                // Crea FormData per test
                const formData = new FormData();
                formData.append('porto_gestione_id', '1');
                formData.append('nave_id', '1');
                formData.append('viaggio', 'TEST' + Date.now());
                formData.append('data_arrivo', '2024-12-01');
                formData.append('data_partenza', '2024-12-02');
                formData.append('porto_arrivo', portoArrivo);
                formData.append('porto_destinazione', portoDestinazione);

                log('📝 Dati da inviare:');
                for (let [key, value] of formData.entries()) {
                    log(`   ${key}: "${value}"`);
                }

                // Invia richiesta
                const response = await fetch('http://localhost:8002/api/viaggi', {
                    method: 'POST',
                    body: formData
                });

                log(`📡 Status creazione: ${response.status}`);
                const data = await response.json();
                log(`✅ Risposta: ${JSON.stringify(data)}`);

                if (data.success) {
                    resultDiv.className = 'test-section success';
                    resultDiv.innerHTML = '<h4>✅ Viaggio Creato</h4><p>Ora testa se i porti sono stati salvati</p>';
                } else {
                    resultDiv.className = 'test-section error';
                    resultDiv.innerHTML = '<h4>❌ Errore Creazione</h4><p>' + (data.error || 'Errore sconosciuto') + '</p>';
                }

            } catch (error) {
                log(`💥 Errore: ${error.message}`);
                resultDiv.className = 'test-section error';
                resultDiv.innerHTML = '<h4>❌ Errore Test</h4><p>' + error.message + '</p>';
            }
        }

        function copyLogs() {
            const logs = allLogs.join('\n');
            navigator.clipboard.writeText(logs).then(() => {
                alert('📋 Log copiati negli appunti!');
            }).catch(() => {
                // Fallback per browser che non supportano clipboard API
                const textArea = document.createElement('textarea');
                textArea.value = logs;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('📋 Log copiati negli appunti!');
            });
        }

        // Auto-start
        window.onload = function() {
            log('🚀 Test System Ready');
            log('📋 Clicca sui pulsanti per eseguire i test');
        };
    </script>
</body>
</html>
