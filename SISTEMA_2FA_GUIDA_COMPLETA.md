# 🔐 Sistema 2FA Email - Guida Completa

## ✅ Sistema Implementato e Funzionante!

Il sistema di autenticazione a due fattori (2FA) tramite **EMAIL** è stato implementato con successo e testato. Ogni volta che avvii l'applicazione con `python main.py`, riceverai un'**email** con un codice di 6 cifre all'indirizzo **<EMAIL>**.

## 🚀 Come Funziona

1. **Avvio**: Esegui `python main.py`
2. **Generazione**: Il sistema genera automaticamente un codice a 6 cifre
3. **Invio Email**: Il codice viene inviato via <NAME_EMAIL>
4. **Verifica**: Ti viene richiesto di inserire il codice ricevuto
5. **Accesso**: Se il codice è corretto, l'applicazione si avvia normalmente

## 📱 Esempio di Utilizzo

```bash
$ python main.py

============================================================
🔐 SISTEMA DI SICUREZZA - VERIFICA AVVIO APPLICAZIONE
============================================================
📱 Invio codice di verifica al numero **********...
✅ SMS inviato con successo al numero ********** tramite textbelt

📝 Tentativo 1/3
🔐 Inserisci il codice a 6 cifre ricevuto via SMS: 123456
✅ CODICE CORRETTO! Avvio applicazione in corso...

🚀 Avvio applicazione autorizzato!
============================================================
[ROCKET] Avvio applicazione SNIP...
```

## 🔧 File Creati

- **`sms_config.py`** - Configurazione provider SMS
- **`.env.example`** - Template variabili d'ambiente
- **`test_2fa.py`** - Test configurazione sistema
- **`test_2fa_complete.py`** - Test flusso completo
- **`test_sms_real.py`** - Test invio SMS reale

## 🧪 Test del Sistema

### Test Configurazione (Raccomandato)
```bash
python test_2fa.py
```

### Test Flusso Completo
```bash
python test_2fa_complete.py
```

### Test SMS Reale (Invia SMS vero!)
```bash
python test_sms_real.py
```

## ⚙️ Configurazione Avanzata

### Provider SMS Supportati

1. **TextBelt** (Predefinito - Gratuito)
   - Funziona immediatamente
   - Limitato a pochi SMS al giorno
   - Perfetto per test e uso personale

2. **Twilio** (Professionale)
   - Affidabile e scalabile
   - Richiede account e configurazione
   - Ideale per uso aziendale

3. **SMS.it** (Provider Italiano)
   - Buoni prezzi per il mercato italiano
   - Richiede account e API key

### Configurazione Personalizzata

1. Copia `.env.example` in `.env`
2. Modifica i parametri desiderati:

```bash
# Provider SMS (textbelt, twilio, smsit)
SMS_PROVIDER=textbelt

# Numero amministratore
ADMIN_PHONE=**********

# Per Twilio
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_FROM_NUMBER=+**********

# Per SMS.it
SMSIT_API_KEY=your_api_key
```

## 🛡️ Sicurezza

- ✅ Codice valido solo per una sessione
- ✅ Massimo 3 tentativi di inserimento
- ✅ Timeout automatico per invio SMS (10 secondi)
- ✅ Logging completo di tutti i tentativi
- ✅ Modalità di emergenza se SMS non arriva
- ✅ Gestione robusta degli errori

## 🚨 Modalità di Emergenza

Se l'SMS non viene ricevuto:
1. Il sistema mostra il codice nei log
2. Puoi usare questo codice per accedere
3. Viene registrato un warning per sicurezza

Esempio:
```
⚠️  ATTENZIONE: Impossibile inviare SMS!
🔧 Modalità di emergenza: inserire il codice mostrato nei log
🔑 Codice di emergenza: 123456
```

## 🔧 Disabilitare Temporaneamente

Se hai problemi, puoi disabilitare il 2FA:

### Metodo 1: Variabile d'ambiente
```bash
# Nel file .env
DISABLE_2FA=true
```

### Metodo 2: Modifica codice
Commenta queste righe in `main.py`:
```python
# if not verify_startup_code():
#     print("\n🛑 Avvio applicazione interrotto per motivi di sicurezza.")
#     exit(1)
```

**⚠️ ATTENZIONE**: Disabilitare il 2FA riduce la sicurezza!

## 📊 Monitoraggio e Log

Tutti gli eventi sono registrati in `snip.log`:
- Generazione codici di verifica
- Tentativi di invio SMS (successo/errore)
- Inserimenti codice (corretti/errati)
- Accessi autorizzati/negati

Esempio log:
```
2025-07-23 09:35:36,020 - INFO - 🔑 Codice di verifica generato: 611749
2025-07-23 09:35:36,021 - INFO - ✅ SMS inviato con successo al numero **********
2025-07-23 09:35:36,022 - INFO - 🚀 Verifica 2FA completata con successo
```

## 🔄 Personalizzazioni

### Cambiare Numero di Telefono
Modifica `ADMIN_PHONE` nel file `.env` o in `sms_config.py`

### Cambiare Numero di Tentativi
Modifica `MAX_ATTEMPTS` nella funzione `verify_startup_code()` in `main.py`

### Personalizzare Messaggi SMS
Modifica il dizionario `MESSAGES` in `sms_config.py`:
```python
MESSAGES = {
    'startup_code': 'Il tuo codice SNIP: {code}',
    'security_alert': 'Tentativo accesso non autorizzato SNIP'
}
```

## 🆘 Risoluzione Problemi

### SMS Non Ricevuto
1. ✅ Verifica connessione internet
2. ✅ Controlla il numero di telefono
3. ✅ Usa modalità di emergenza con codice dai log
4. ✅ Prova provider SMS diverso

### Errori di Configurazione
1. ✅ Verifica file `sms_config.py` esiste
2. ✅ Controlla credenziali provider SMS
3. ✅ Consulta i log in `snip.log`

### Provider Non Funziona
1. ✅ Usa TextBelt (gratuito, sempre disponibile)
2. ✅ Verifica credenziali API
3. ✅ Controlla documentazione provider

## 📞 Supporto

Il sistema è robusto e gestisce automaticamente:
- Errori di rete
- Provider SMS non disponibili
- Configurazioni mancanti
- Interruzioni utente (Ctrl+C)

In caso di problemi, consulta sempre i log in `snip.log` per dettagli specifici.

## 🎯 Stato Attuale

✅ **Sistema Implementato e Testato**
✅ **Funziona con TextBelt (gratuito)**
✅ **Supporta provider professionali**
✅ **Modalità di emergenza attiva**
✅ **Logging completo**
✅ **Gestione errori robusta**

**Il sistema è pronto per l'uso immediato!**
