#!/usr/bin/env python3
"""
Script per aggiungere user_theme a tutte le route che hanno current_user
"""

import re

def fix_routes_theme():
    """Aggiunge user_theme a tutte le route che ne hanno bisogno"""
    
    # Lista delle route che devono essere aggiornate
    routes_to_fix = [
        # Notifiche
        ('/notifications', 'notifications.html'),
        ('/admin/notifiche', 'admin_notifiche.html'),
        ('/notifiche', 'user_notifiche.html'),
        
        # Contabilità
        ('/contabilita/fatture', 'contabilita/fatture.html'),
        ('/contabilita/pagamenti', 'contabilita/pagamenti.html'),
        ('/contabilita/report', 'contabilita/report.html'),
        ('/contabilita/bilanci', 'contabilita/bilanci.html'),
        ('/contabilita/costi', 'contabilita/costi.html'),
        
        # Shortsea
        ('/shortsea/rotte', 'shortsea/rotte.html'),
        ('/shortsea/spedizioni', 'shortsea/spedizioni.html'),
        ('/shortsea/report', 'shortsea/report.html'),
        
        # Operativo
        ('/operativo/porti', 'operativo/porti.html'),
        ('/operativo/navi', 'operativo/navi.html'),
        ('/operativo/armatori', 'operativo/armatori.html'),
        ('/operativo/sof/viaggio/{viaggio_id}', 'operativo/viaggio_dettaglio.html'),
        
        # Admin
        ('/admin/backup', 'admin_backup.html'),
    ]
    
    print("🔧 Route che necessitano di user_theme:")
    for route, template in routes_to_fix:
        print(f"   - {route} → {template}")
    
    print("\n📝 Modifiche da applicare manualmente:")
    print("   1. Aggiungere 'user_theme = get_user_theme(current_user)' prima del return")
    print("   2. Aggiungere '\"user_theme\": user_theme' nel context del TemplateResponse")
    print("   3. Aggiornare i template per usare class=\"theme-{{ user_theme|default('light') }}\"")
    
    print("\n🎯 Template che devono essere aggiornati:")
    templates_to_fix = [
        'templates/notifications.html',
        'templates/admin_notifiche.html', 
        'templates/user_notifiche.html',
        'templates/contabilita/fatture.html',
        'templates/contabilita/pagamenti.html',
        'templates/contabilita/report.html',
        'templates/contabilita/bilanci.html',
        'templates/contabilita/costi.html',
        'templates/shortsea/rotte.html',
        'templates/shortsea/spedizioni.html',
        'templates/shortsea/report.html',
        'templates/operativo/porti.html',
        'templates/operativo/navi.html',
        'templates/operativo/armatori.html',
        'templates/operativo/viaggio_dettaglio.html',
        'templates/admin_backup.html',
    ]
    
    for template in templates_to_fix:
        print(f"   - {template}")
    
    print("\n🚀 Prossimi passi:")
    print("   1. Aggiornare le route in main.py")
    print("   2. Aggiornare i template per usare il tema del server")
    print("   3. Aggiungere theme-manager.js ai template")
    print("   4. Testare ogni pagina per verificare il tema personalizzato")

if __name__ == "__main__":
    fix_routes_theme()
