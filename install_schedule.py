#!/usr/bin/env python3
"""
Script per installare automaticamente il modulo schedule
Risolve l'errore "No module named 'schedule'" nel backup manager
"""

import subprocess
import sys
import logging

def install_schedule():
    """Installa il modulo schedule"""
    print("🔧 Installazione modulo schedule per backup automatici...")
    
    try:
        # Installa schedule
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "schedule==1.2.0"
        ], capture_output=True, text=True, check=True)
        
        print("✅ Modulo schedule installato con successo!")
        print(f"Output: {result.stdout}")
        
        # Test import
        try:
            __import__('schedule')
            print("✅ Test import riuscito - schedule funzionante")
            return True
        except ImportError as e:
            print(f"❌ Test import fallito: {e}")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Errore installazione: {e}")
        print(f"Stderr: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ Errore imprevisto: {e}")
        return False

def check_schedule_status():
    """Controlla lo stato del modulo schedule"""
    print("🔍 Controllo stato modulo schedule...")
    
    try:
        schedule_module = __import__('schedule')
        print("✅ Modulo schedule già installato e funzionante")
        print(f"Versione: {getattr(schedule_module, '__version__', 'N/A')}")
        return True
    except ImportError:
        print("❌ Modulo schedule non installato")
        return False

def main():
    """Funzione principale"""
    print("🚀 INSTALLAZIONE MODULO SCHEDULE")
    print("=" * 50)
    
    # Controlla stato attuale
    if check_schedule_status():
        print("\n🎉 Nessuna azione necessaria - schedule già installato!")
        return True
    
    # Chiedi conferma
    print("\n❓ Vuoi installare il modulo schedule per abilitare i backup automatici?")
    print("   Questo risolverà l'errore nel backup_manager.py")
    
    response = input("Installare? (s/n): ").lower().strip()
    
    if response in ['s', 'si', 'y', 'yes']:
        success = install_schedule()
        
        if success:
            print("\n🎉 Installazione completata!")
            print("💡 Ora puoi abilitare il backup scheduler in main.py:")
            print("   backup_service = start_backup_service(settings.DATABASE_URL, enable_scheduler=True)")
        else:
            print("\n❌ Installazione fallita")
            print("💡 Prova manualmente: pip install schedule==1.2.0")
        
        return success
    else:
        print("\n⏭️ Installazione saltata")
        print("💡 I backup automatici rimarranno disabilitati")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
