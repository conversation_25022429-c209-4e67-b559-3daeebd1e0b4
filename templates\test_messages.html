<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Messaggi - SNIP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- CSS Temi Globali -->
    <link rel="stylesheet" href="{{ url_for('static', path='css/global-themes.css') }}">
    
    <style>
        body {
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            transition: all 0.3s ease;
        }
        
        .test-container {
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            padding: 30px;
            transition: all 0.3s ease;
        }
        
        /* <PERSON><PERSON> (default) */
        body.theme-maritime .test-container,
        body:not([class*="theme-"]) .test-container {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        /* Tema Scuro */
        body.theme-dark .test-container {
            background: rgba(52, 73, 94, 0.95);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: #ecf0f1;
        }
        
        /* Tema Chiaro */
        body.theme-light .test-container {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.1);
            color: #212529;
        }
        
        .btn-test {
            margin: 5px;
            padding: 10px 20px;
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .theme-selector {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        body.theme-dark .theme-selector {
            background: rgba(52, 73, 94, 0.9);
            color: #ecf0f1;
        }
    </style>
</head>
<body class="theme-{{ user_theme }}">
    <!-- Selettore tema per test -->
    <div class="theme-selector">
        <h6><i class="fas fa-palette me-2"></i>Test Temi</h6>
        <button class="btn btn-sm btn-primary" onclick="changeTheme('maritime')">Marittimo</button>
        <button class="btn btn-sm btn-dark" onclick="changeTheme('dark')">Scuro</button>
        <button class="btn btn-sm btn-light" onclick="changeTheme('light')">Chiaro</button>
    </div>

    <div class="container">
        <div class="test-container">
            <h1><i class="fas fa-vial me-2"></i>Test Messaggi SNIP</h1>
            <p class="mb-4">Testa tutti i tipi di messaggi con i diversi temi</p>
            
            <!-- Test SNIP Messages -->
            <div class="row mb-4">
                <div class="col-12">
                    <h3><i class="fas fa-star me-2"></i>SNIP Messages</h3>
                    <button class="btn btn-success btn-test" onclick="testSNIPSuccess()">
                        <i class="fas fa-check me-2"></i>Successo
                    </button>
                    <button class="btn btn-danger btn-test" onclick="testSNIPError()">
                        <i class="fas fa-times me-2"></i>Errore
                    </button>
                    <button class="btn btn-warning btn-test" onclick="testSNIPWarning()">
                        <i class="fas fa-exclamation me-2"></i>Avviso
                    </button>
                    <button class="btn btn-info btn-test" onclick="testSNIPInfo()">
                        <i class="fas fa-info me-2"></i>Info
                    </button>
                    <button class="btn btn-secondary btn-test" onclick="testSNIPConfirm()">
                        <i class="fas fa-question me-2"></i>Conferma
                    </button>
                </div>
            </div>
            
            <!-- Test Bootstrap Alerts -->
            <div class="row mb-4">
                <div class="col-12">
                    <h3><i class="fas fa-bell me-2"></i>Bootstrap Alerts</h3>
                    <button class="btn btn-success btn-test" onclick="showBootstrapAlert('success')">
                        <i class="fas fa-check me-2"></i>Alert Successo
                    </button>
                    <button class="btn btn-danger btn-test" onclick="showBootstrapAlert('danger')">
                        <i class="fas fa-times me-2"></i>Alert Errore
                    </button>
                    <button class="btn btn-warning btn-test" onclick="showBootstrapAlert('warning')">
                        <i class="fas fa-exclamation me-2"></i>Alert Avviso
                    </button>
                    <button class="btn btn-info btn-test" onclick="showBootstrapAlert('info')">
                        <i class="fas fa-info me-2"></i>Alert Info
                    </button>
                </div>
            </div>
            
            <!-- Container per alert Bootstrap -->
            <div id="alert-container"></div>
            
            <!-- Test Toast Bootstrap -->
            <div class="row mb-4">
                <div class="col-12">
                    <h3><i class="fas fa-bread-slice me-2"></i>Bootstrap Toast</h3>
                    <button class="btn btn-success btn-test" onclick="showBootstrapToast('success')">
                        <i class="fas fa-check me-2"></i>Toast Successo
                    </button>
                    <button class="btn btn-danger btn-test" onclick="showBootstrapToast('danger')">
                        <i class="fas fa-times me-2"></i>Toast Errore
                    </button>
                    <button class="btn btn-warning btn-test" onclick="showBootstrapToast('warning')">
                        <i class="fas fa-exclamation me-2"></i>Toast Avviso
                    </button>
                    <button class="btn btn-info btn-test" onclick="showBootstrapToast('info')">
                        <i class="fas fa-info me-2"></i>Toast Info
                    </button>
                </div>
            </div>

            <!-- Test Sistemi Legacy -->
            <div class="row mb-4">
                <div class="col-12">
                    <h3><i class="fas fa-history me-2"></i>Sistemi Legacy (Migrati)</h3>
                    <button class="btn btn-success btn-test" onclick="testLegacyShowSuccess()">
                        <i class="fas fa-check me-2"></i>showSuccess()
                    </button>
                    <button class="btn btn-danger btn-test" onclick="testLegacyShowError()">
                        <i class="fas fa-times me-2"></i>showError()
                    </button>
                    <button class="btn btn-warning btn-test" onclick="testLegacyShowWarning()">
                        <i class="fas fa-exclamation me-2"></i>showWarning()
                    </button>
                    <button class="btn btn-info btn-test" onclick="testLegacyShowInfo()">
                        <i class="fas fa-info me-2"></i>showInfo()
                    </button>
                    <button class="btn btn-secondary btn-test" onclick="testNativeAlert()">
                        <i class="fas fa-bell me-2"></i>alert() nativo
                    </button>
                    <button class="btn btn-secondary btn-test" onclick="testNativeConfirm()">
                        <i class="fas fa-question me-2"></i>confirm() nativo
                    </button>
                </div>
            </div>

            <!-- Test SweetAlert -->
            <div class="row mb-4">
                <div class="col-12">
                    <h3><i class="fas fa-magic me-2"></i>SweetAlert (Migrato)</h3>
                    <button class="btn btn-success btn-test" onclick="testSwalSuccess()">
                        <i class="fas fa-check me-2"></i>Swal Success
                    </button>
                    <button class="btn btn-danger btn-test" onclick="testSwalError()">
                        <i class="fas fa-times me-2"></i>Swal Error
                    </button>
                    <button class="btn btn-warning btn-test" onclick="testSwalWarning()">
                        <i class="fas fa-exclamation me-2"></i>Swal Warning
                    </button>
                    <button class="btn btn-secondary btn-test" onclick="testSwalConfirm()">
                        <i class="fas fa-question me-2"></i>Swal Confirm
                    </button>
                </div>
            </div>
            
            <!-- Container per toast -->
            <div class="toast-container position-fixed top-0 end-0 p-3" id="toast-container"></div>
            
            <div class="mt-4">
                <h4>Tema Corrente: <span id="current-theme">{{ user_theme }}</span></h4>
                <p class="text-muted">Cambia tema usando i pulsanti in alto a sinistra per testare la visibilità dei messaggi.</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/messages.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // Test SNIP Messages
        function testSNIPSuccess() {
            snipMessages.success('🎉 Test Successo!', 'Questo è un messaggio di successo con tema ' + getCurrentTheme());
        }
        
        function testSNIPError() {
            snipMessages.error('💥 Test Errore!', 'Questo è un messaggio di errore con tema ' + getCurrentTheme());
        }
        
        function testSNIPWarning() {
            snipMessages.warning('⚡ Test Avviso!', 'Questo è un messaggio di avviso con tema ' + getCurrentTheme());
        }
        
        function testSNIPInfo() {
            snipMessages.info('💫 Test Info!', 'Questo è un messaggio informativo con tema ' + getCurrentTheme());
        }
        
        function testSNIPConfirm() {
            snipMessages.confirm('🤔 Test Conferma', 'Vuoi confermare questa azione di test?', function(result) {
                if (result) {
                    snipMessages.success('✅ Confermato!', 'Hai confermato l\'azione di test');
                } else {
                    snipMessages.info('❌ Annullato', 'Hai annullato l\'azione di test');
                }
            });
        }
        
        // Test Bootstrap Alerts
        function showBootstrapAlert(type) {
            const container = document.getElementById('alert-container');
            const alertId = 'alert-' + Date.now();
            
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert" id="${alertId}">
                    <i class="fas fa-${getIconForType(type)} me-2"></i>
                    <strong>Alert ${type.charAt(0).toUpperCase() + type.slice(1)}!</strong> 
                    Questo è un alert Bootstrap di tipo ${type} con tema ${getCurrentTheme()}.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            container.insertAdjacentHTML('beforeend', alertHtml);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 5000);
        }
        
        // Test Bootstrap Toast
        function showBootstrapToast(type) {
            const container = document.getElementById('toast-container');
            const toastId = 'toast-' + Date.now();
            
            const toastHtml = `
                <div class="toast bg-${type}" role="alert" id="${toastId}">
                    <div class="toast-header">
                        <i class="fas fa-${getIconForType(type)} me-2"></i>
                        <strong class="me-auto">Toast ${type.charAt(0).toUpperCase() + type.slice(1)}</strong>
                        <small>ora</small>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body text-white">
                        Questo è un toast Bootstrap di tipo ${type} con tema ${getCurrentTheme()}.
                    </div>
                </div>
            `;
            
            container.insertAdjacentHTML('beforeend', toastHtml);
            
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement);
            toast.show();
        }
        
        // Utility functions
        function getIconForType(type) {
            const icons = {
                success: 'check',
                danger: 'times',
                warning: 'exclamation-triangle',
                info: 'info-circle'
            };
            return icons[type] || 'info-circle';
        }
        
        function getCurrentTheme() {
            const body = document.body;
            if (body.classList.contains('theme-dark')) return 'scuro';
            if (body.classList.contains('theme-light')) return 'chiaro';
            return 'marittimo';
        }
        
        function changeTheme(theme) {
            const body = document.body;
            body.classList.remove('theme-maritime', 'theme-dark', 'theme-light');
            body.classList.add('theme-' + theme);
            document.getElementById('current-theme').textContent = theme;
            console.log('🎨 Tema cambiato a:', theme);
        }

        // Test sistemi legacy
        function testLegacyShowSuccess() {
            showSuccess('Questo è un test della funzione showSuccess() migrata al sistema SNIP!');
        }

        function testLegacyShowError() {
            showError('Questo è un test della funzione showError() migrata al sistema SNIP!');
        }

        function testLegacyShowWarning() {
            showWarning('Questo è un test della funzione showWarning() migrata al sistema SNIP!');
        }

        function testLegacyShowInfo() {
            showInfo('Questo è un test della funzione showInfo() migrata al sistema SNIP!');
        }

        function testNativeAlert() {
            alert('Questo è un test di alert() nativo migrato al sistema SNIP!');
        }

        async function testNativeConfirm() {
            const result = await confirm('Questo è un test di confirm() nativo migrato. Confermi?');
            console.log('Risultato confirm:', result);
        }

        // Test SweetAlert
        function testSwalSuccess() {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'success',
                    title: 'Test SweetAlert Success',
                    text: 'Questo SweetAlert è stato migrato al sistema SNIP!'
                });
            } else {
                snipMessages.info('💫 Info', 'SweetAlert non caricato, ma la migrazione funziona comunque!');
            }
        }

        function testSwalError() {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'error',
                    title: 'Test SweetAlert Error',
                    text: 'Questo SweetAlert è stato migrato al sistema SNIP!'
                });
            } else {
                snipMessages.info('💫 Info', 'SweetAlert non caricato, ma la migrazione funziona comunque!');
            }
        }

        function testSwalWarning() {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'warning',
                    title: 'Test SweetAlert Warning',
                    text: 'Questo SweetAlert è stato migrato al sistema SNIP!'
                });
            } else {
                snipMessages.info('💫 Info', 'SweetAlert non caricato, ma la migrazione funziona comunque!');
            }
        }

        async function testSwalConfirm() {
            if (typeof Swal !== 'undefined') {
                const result = await Swal.fire({
                    title: 'Test SweetAlert Confirm',
                    text: 'Questo SweetAlert confirm è stato migrato. Confermi?',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: 'Sì, confermo!',
                    cancelButtonText: 'Annulla'
                });

                if (result.isConfirmed) {
                    snipMessages.success('✅ Confermato!', 'Hai confermato il SweetAlert migrato!');
                } else {
                    snipMessages.info('❌ Annullato', 'Hai annullato il SweetAlert migrato');
                }
            } else {
                snipMessages.info('💫 Info', 'SweetAlert non caricato, ma la migrazione funziona comunque!');
            }
        }

        console.log('🧪 Pagina test messaggi caricata - Tema corrente:', getCurrentTheme());
    </script>
</body>
</html>
