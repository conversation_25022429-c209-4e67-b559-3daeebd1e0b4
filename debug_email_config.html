<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Configurazioni Email</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .debug-output { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin-top: 20px; border-radius: 4px; }
        .debug-output pre { margin: 0; white-space: pre-wrap; }
        .error { color: #dc3545; }
        .success { color: #28a745; }
    </style>
</head>
<body>
    <h1>🔧 Debug Configurazioni Email SNIP</h1>
    <p>Questo strumento testa la raccolta dati email dal form di configurazione.</p>

    <form id="email-config-form">
        <h3>📧 Configurazioni Email</h3>
        
        <div class="form-group">
            <label for="smtp-host">Host SMTP:</label>
            <input type="text" id="smtp-host" value="smtp.gmail.com">
        </div>
        
        <div class="form-group">
            <label for="smtp-port">Porta SMTP:</label>
            <input type="number" id="smtp-port" value="587">
        </div>
        
        <div class="form-group">
            <label for="smtp-username">Username SMTP:</label>
            <input type="email" id="smtp-username" value="<EMAIL>">
        </div>
        
        <div class="form-group">
            <label for="smtp-password">Password SMTP:</label>
            <input type="password" id="smtp-password" value="test_password">
        </div>
        
        <div class="form-group">
            <label for="sender-email">Email Mittente:</label>
            <input type="email" id="sender-email" value="<EMAIL>">
        </div>
        
        <div class="form-group">
            <label for="sender-name">Nome Mittente:</label>
            <input type="text" id="sender-name" value="Michele Autuori Srl">
        </div>
        
        <div class="form-group">
            <label for="admin-email">Email Admin:</label>
            <input type="email" id="admin-email" value="<EMAIL>">
        </div>
        
        <div class="form-group">
            <label for="support-email">Email Supporto:</label>
            <input type="email" id="support-email" value="<EMAIL>">
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" id="smtp-ssl" checked> Usa SSL/TLS
            </label>
        </div>
        
        <button type="button" onclick="testCollectData()">🧪 Test Raccolta Dati</button>
        <button type="button" onclick="testValidation()">✅ Test Validazione</button>
        <button type="button" onclick="clearForm()">🗑️ Pulisci Form</button>
    </form>

    <div class="debug-output">
        <h4>📊 Output Debug</h4>
        <pre id="debug-output">Clicca su "Test Raccolta Dati" per iniziare...</pre>
    </div>

    <script>
        // Copia delle funzioni dal config-management.js
        function getFieldValue(fieldId, type = 'text') {
            const field = document.getElementById(fieldId);
            if (!field) {
                console.error(`Campo non trovato: ${fieldId}`);
                return null;
            }

            if (type === 'checkbox') {
                return field.checked;
            }
            return field.value;
        }

        function collectEmailConfigurationData() {
            return {
                smtp_host: getFieldValue('smtp-host'),
                smtp_port: parseInt(getFieldValue('smtp-port')),
                smtp_username: getFieldValue('smtp-username'),
                smtp_password: getFieldValue('smtp-password'),
                sender_email: getFieldValue('sender-email'),
                sender_name: getFieldValue('sender-name'),
                admin_email: getFieldValue('admin-email'),
                support_email: getFieldValue('support-email'),
                smtp_ssl: getFieldValue('smtp-ssl', 'checkbox')
            };
        }

        function validateEmailField(fieldId) {
            const field = document.getElementById(fieldId);
            if (!field) return { valid: false, error: `Campo ${fieldId} non trovato` };

            const email = field.value.trim();
            if (!email) return { valid: true, warning: 'Campo vuoto' };

            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            const isValid = emailRegex.test(email);

            return {
                valid: isValid,
                value: email,
                error: isValid ? null : 'Formato email non valido'
            };
        }

        function validateAllEmailFields() {
            const emailFields = ['smtp-username', 'sender-email', 'admin-email', 'support-email'];
            const results = {};
            let allValid = true;

            emailFields.forEach(fieldId => {
                const result = validateEmailField(fieldId);
                results[fieldId] = result;
                if (!result.valid && result.error) {
                    allValid = false;
                }
            });

            return { allValid, results };
        }

        function testCollectData() {
            const output = document.getElementById('debug-output');
            
            try {
                console.log('🧪 Inizio test raccolta dati...');
                
                // Test raccolta dati
                const emailData = collectEmailConfigurationData();
                
                // Verifica che tutti i campi siano stati raccolti
                const expectedFields = [
                    'smtp_host', 'smtp_port', 'smtp_username', 'smtp_password',
                    'sender_email', 'sender_name', 'admin_email', 'support_email', 'smtp_ssl'
                ];
                
                const missingFields = [];
                const collectedFields = [];
                
                expectedFields.forEach(field => {
                    if (emailData[field] !== null && emailData[field] !== undefined) {
                        collectedFields.push(`✅ ${field}: ${emailData[field]}`);
                    } else {
                        missingFields.push(`❌ ${field}: MANCANTE`);
                    }
                });
                
                let result = `🧪 TEST RACCOLTA DATI EMAIL\n`;
                result += `=================================\n\n`;
                result += `📊 Dati raccolti:\n`;
                result += JSON.stringify(emailData, null, 2) + '\n\n';
                
                result += `📋 Verifica campi:\n`;
                result += collectedFields.join('\n') + '\n';
                
                if (missingFields.length > 0) {
                    result += `\n❌ Campi mancanti:\n`;
                    result += missingFields.join('\n') + '\n';
                }
                
                result += `\n📈 Risultato: ${missingFields.length === 0 ? '✅ SUCCESSO' : '❌ ERRORI TROVATI'}`;
                
                output.textContent = result;
                output.className = missingFields.length === 0 ? 'debug-output success' : 'debug-output error';
                
            } catch (error) {
                output.textContent = `❌ ERRORE DURANTE IL TEST:\n${error.message}\n\nStack trace:\n${error.stack}`;
                output.className = 'debug-output error';
            }
        }

        function testValidation() {
            const output = document.getElementById('debug-output');
            
            try {
                console.log('✅ Inizio test validazione...');
                
                const validation = validateAllEmailFields();
                
                let result = `✅ TEST VALIDAZIONE EMAIL\n`;
                result += `============================\n\n`;
                result += `📊 Risultato generale: ${validation.allValid ? '✅ VALIDO' : '❌ ERRORI TROVATI'}\n\n`;
                
                result += `📋 Dettagli per campo:\n`;
                Object.entries(validation.results).forEach(([field, result]) => {
                    const status = result.valid ? '✅' : '❌';
                    const value = result.value || 'vuoto';
                    const message = result.error || result.warning || 'OK';
                    result += `${status} ${field}: "${value}" - ${message}\n`;
                });
                
                output.textContent = result;
                output.className = validation.allValid ? 'debug-output success' : 'debug-output error';
                
            } catch (error) {
                output.textContent = `❌ ERRORE DURANTE LA VALIDAZIONE:\n${error.message}\n\nStack trace:\n${error.stack}`;
                output.className = 'debug-output error';
            }
        }

        function clearForm() {
            const fields = ['smtp-host', 'smtp-port', 'smtp-username', 'smtp-password', 
                          'sender-email', 'sender-name', 'admin-email', 'support-email'];
            
            fields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) field.value = '';
            });
            
            const sslField = document.getElementById('smtp-ssl');
            if (sslField) sslField.checked = false;
            
            document.getElementById('debug-output').textContent = 'Form pulito. Clicca su "Test Raccolta Dati" per testare con campi vuoti.';
            document.getElementById('debug-output').className = 'debug-output';
        }

        // Test automatico al caricamento della pagina
        window.addEventListener('load', function() {
            console.log('🚀 Debug Email Config caricato');
            setTimeout(testCollectData, 500);
        });
    </script>
</body>
</html>
