<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Esempio Rigenerazione SOF - SNIP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-ship text-primary"></i>
                    Gestione SOF con Rigenerazione Automatica
                </h1>
                
                <!-- Alert informativo -->
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Novità!</strong> I SOF ora si rigenerano automaticamente quando modifichi orari o dati. 
                    Puoi anche forzare la rigenerazione usando il pulsante "Rigenera SOF".
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Esempio viaggio con SOF esistente -->
            <div class="col-md-6 mb-4">
                <div class="card" data-viaggio-id="123" data-sof-exists="true">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-check-circle"></i>
                            Viaggio ABC123 - SOF Completato
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">
                            <strong>Nave:</strong> COSTA SMERALDA<br>
                            <strong>Porto:</strong> Salerno<br>
                            <strong>Data:</strong> 15/01/2024<br>
                            <strong>Stato:</strong> <span class="badge bg-success">SOF Generato</span>
                        </p>
                        
                        <div class="d-flex gap-2">
                            <!-- Pulsante download esistente -->
                            <a href="/operativo/sof/viaggio/123/download" class="btn btn-primary btn-sm download-sof-btn">
                                <i class="fas fa-download"></i> Scarica SOF
                            </a>
                            
                            <!-- Il pulsante di rigenerazione verrà aggiunto automaticamente dal JavaScript -->
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-clock"></i>
                                SOF generato il 15/01/2024 alle 14:30
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Esempio viaggio senza SOF -->
            <div class="col-md-6 mb-4">
                <div class="card" data-viaggio-id="124">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clock"></i>
                            Viaggio DEF456 - Da Realizzare
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">
                            <strong>Nave:</strong> COSTA FORTUNA<br>
                            <strong>Porto:</strong> Gioia Tauro<br>
                            <strong>Data:</strong> 16/01/2024<br>
                            <strong>Stato:</strong> <span class="badge bg-warning">In Attesa</span>
                        </p>
                        
                        <div class="d-flex gap-2">
                            <a href="/operativo/sof/viaggio/124/download" class="btn btn-success btn-sm">
                                <i class="fas fa-plus"></i> Genera SOF
                            </a>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                SOF non ancora generato
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sezione modifica dati -->
        <div class="row mt-4">
            <div class="col-12">
                <h3>Modifica Dati Viaggio</h3>
                <p class="text-muted">
                    Quando modifichi orari o dati import/export, il SOF verrà automaticamente rigenerato al prossimo download.
                </p>
                
                <!-- Form esempio per modifica orari -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-edit"></i>
                            Modifica Orari - Viaggio ABC123
                        </h5>
                    </div>
                    <div class="card-body">
                        <form data-auto-regenerate="true" data-viaggio-id="123">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="sbe" class="form-label">SBE (Start of Business)</label>
                                    <input type="datetime-local" class="form-control" id="sbe" name="sbe" value="2024-01-15T08:00">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="arrival_pilot" class="form-label">Arrival Pilot</label>
                                    <input type="datetime-local" class="form-control" id="arrival_pilot" name="arrival_pilot" value="2024-01-15T09:30">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="all_fast" class="form-label">All Fast</label>
                                    <input type="datetime-local" class="form-control" id="all_fast" name="all_fast" value="2024-01-15T10:00">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="draft" class="form-label">Draft</label>
                                    <input type="number" class="form-control" id="draft" name="draft" value="8.5" step="0.1">
                                </div>
                            </div>
                            
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>Attenzione:</strong> Modificando questi dati, il SOF esistente verrà automaticamente rigenerato 
                                con i nuovi valori al prossimo download.
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Salva Modifiche
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sezione API Testing -->
        <div class="row mt-4">
            <div class="col-12">
                <h3>Test API</h3>
                <div class="card">
                    <div class="card-body">
                        <h5>Endpoint Disponibili</h5>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <code>GET /operativo/sof/viaggio/{viaggio_id}/download</code>
                                <span class="badge bg-info ms-2">Scarica SOF (con rigenerazione automatica)</span>
                            </li>
                            <li class="list-group-item">
                                <code>POST /operativo/sof/viaggio/{viaggio_id}/regenerate</code>
                                <span class="badge bg-warning ms-2">Forza rigenerazione SOF</span>
                            </li>
                            <li class="list-group-item">
                                <code>GET /operativo/sof/viaggio/{viaggio_id}/sof/statistics</code>
                                <span class="badge bg-secondary ms-2">Statistiche SOF</span>
                            </li>
                        </ul>
                        
                        <div class="mt-3">
                            <button class="btn btn-outline-primary btn-sm" onclick="testAPI(123)">
                                <i class="fas fa-play"></i> Test API Viaggio 123
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/sof_regeneration.js"></script>
    
    <script>
        // Funzione di test API
        async function testAPI(viaggioId) {
            console.log(`Testing API for viaggio ${viaggioId}`);
            
            try {
                // Test statistiche
                const statsResponse = await fetch(`/operativo/sof/viaggio/${viaggioId}/sof/statistics`);
                const stats = await statsResponse.json();
                console.log('Statistics:', stats);
                
                // Mostra risultato
                alert(`Statistiche viaggio ${viaggioId}:\n${JSON.stringify(stats, null, 2)}`);
                
            } catch (error) {
                console.error('Error testing API:', error);
                alert('Errore durante il test API: ' + error.message);
            }
        }
        
        // Simula submit form
        document.querySelector('form[data-auto-regenerate]').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Form salvato! In un ambiente reale, i dati verrebbero aggiornati nel database.');
        });
    </script>
</body>
</html>
