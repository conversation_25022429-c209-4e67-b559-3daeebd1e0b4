<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Funzionalità Archiviazione</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>🧪 Test Funzionalità Archiviazione</h1>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5>Test Campi Dinamici</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">Tipo Periodo</label>
                    <select class="form-select" id="tipoPeriodo" onchange="aggiornaCampiPeriodo()">
                        <option value="">Seleziona tipo periodo</option>
                        <option value="anno">Anno</option>
                        <option value="mese">Mese</option>
                        <option value="trimestre">Trimestre</option>
                        <option value="giorni">Range di giorni</option>
                    </select>
                </div>

                <div id="campiPeriodo">
                    <!-- Campi dinamici in base al tipo periodo -->
                </div>

                <button class="btn btn-warning w-100 mb-2" onclick="testArchiviazione()" id="btnArchiviaPeriodo" disabled>
                    <i class="fas fa-archive me-2"></i>
                    Test Archiviazione
                </button>
                
                <button class="btn btn-outline-info btn-sm w-100" onclick="testConnessioneAPI()" id="btnTestAPI">
                    <i class="fas fa-bug me-2"></i>
                    🧪 Test Connessione API
                </button>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5>Log Test</h5>
            </div>
            <div class="card-body">
                <div id="logOutput" style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto;">
                    Pronto per i test...\n
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Funzione per aggiungere log
        function addLog(message) {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            logOutput.textContent += `[${timestamp}] ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        // Gestione campi dinamici per tipo periodo
        function aggiornaCampiPeriodo() {
            const tipoPeriodo = document.getElementById('tipoPeriodo').value;
            const campiPeriodo = document.getElementById('campiPeriodo');
            const btnArchivia = document.getElementById('btnArchiviaPeriodo');

            addLog(`🔄 Tipo periodo selezionato: ${tipoPeriodo}`);

            campiPeriodo.innerHTML = '';
            btnArchivia.disabled = true;

            if (tipoPeriodo === 'anno') {
                campiPeriodo.innerHTML = `
                    <div class="mb-3">
                        <label class="form-label">Anno</label>
                        <select class="form-select" id="annoSelezionato" onchange="validaCampiPeriodo()">
                            <option value="">Seleziona anno</option>
                            <option value="2024">2024</option>
                            <option value="2023">2023</option>
                            <option value="2022">2022</option>
                        </select>
                    </div>
                `;
                addLog("✅ Campi anno generati");
            } else if (tipoPeriodo === 'mese') {
                campiPeriodo.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Anno</label>
                            <select class="form-select" id="annoMese" onchange="validaCampiPeriodo()">
                                <option value="">Seleziona anno</option>
                                <option value="2024">2024</option>
                                <option value="2023">2023</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Mese</label>
                            <select class="form-select" id="meseSelezionato" onchange="validaCampiPeriodo()">
                                <option value="">Seleziona mese</option>
                                <option value="1">Gennaio</option>
                                <option value="2">Febbraio</option>
                                <option value="3">Marzo</option>
                                <option value="4">Aprile</option>
                                <option value="5">Maggio</option>
                                <option value="6">Giugno</option>
                                <option value="7">Luglio</option>
                                <option value="8">Agosto</option>
                                <option value="9">Settembre</option>
                                <option value="10">Ottobre</option>
                                <option value="11">Novembre</option>
                                <option value="12">Dicembre</option>
                            </select>
                        </div>
                    </div>
                `;
                addLog("✅ Campi mese generati");
            } else if (tipoPeriodo === 'trimestre') {
                campiPeriodo.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Anno</label>
                            <select class="form-select" id="annoTrimestre" onchange="validaCampiPeriodo()">
                                <option value="">Seleziona anno</option>
                                <option value="2024">2024</option>
                                <option value="2023">2023</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Trimestre</label>
                            <select class="form-select" id="trimestreSelezionato" onchange="validaCampiPeriodo()">
                                <option value="">Seleziona trimestre</option>
                                <option value="1">Q1 (Gen-Mar)</option>
                                <option value="2">Q2 (Apr-Giu)</option>
                                <option value="3">Q3 (Lug-Set)</option>
                                <option value="4">Q4 (Ott-Dic)</option>
                            </select>
                        </div>
                    </div>
                `;
                addLog("✅ Campi trimestre generati");
            } else if (tipoPeriodo === 'giorni') {
                campiPeriodo.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Data Inizio</label>
                            <input type="date" class="form-control" id="dataInizio" onchange="validaCampiPeriodo()">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Data Fine</label>
                            <input type="date" class="form-control" id="dataFine" onchange="validaCampiPeriodo()">
                        </div>
                    </div>
                `;
                addLog("✅ Campi range giorni generati");
            }
        }

        function validaCampiPeriodo() {
            const tipoPeriodo = document.getElementById('tipoPeriodo').value;
            const btnArchivia = document.getElementById('btnArchiviaPeriodo');
            let valido = false;

            if (tipoPeriodo === 'anno') {
                valido = document.getElementById('annoSelezionato').value !== '';
            } else if (tipoPeriodo === 'mese') {
                valido = document.getElementById('annoMese').value !== '' &&
                        document.getElementById('meseSelezionato').value !== '';
            } else if (tipoPeriodo === 'trimestre') {
                valido = document.getElementById('annoTrimestre').value !== '' &&
                        document.getElementById('trimestreSelezionato').value !== '';
            } else if (tipoPeriodo === 'giorni') {
                valido = document.getElementById('dataInizio').value !== '' &&
                        document.getElementById('dataFine').value !== '';
            }

            btnArchivia.disabled = !valido;
            
            if (valido) {
                addLog("✅ Campi validati - Pulsante abilitato");
            } else {
                addLog("⚠️ Campi non completi - Pulsante disabilitato");
            }
        }

        function testArchiviazione() {
            addLog("🧪 Test archiviazione avviato");
            const tipoPeriodo = document.getElementById('tipoPeriodo').value;
            addLog(`📊 Tipo periodo: ${tipoPeriodo}`);
            
            // Simula test senza chiamare API reale
            setTimeout(() => {
                addLog("✅ Test completato (simulazione)");
            }, 1000);
        }

        async function testConnessioneAPI() {
            addLog("🧪 Test connessione API...");

            try {
                // Test endpoint normale
                addLog("📡 Test endpoint normale (con autenticazione)...");
                const response1 = await fetch('http://127.0.0.1:8003/api/operativo/sof/archivia-json', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        periodo_tipo: 'anno',
                        periodo_valore: '2024'
                    })
                });

                addLog(`📡 Response status: ${response1.status}`);
                const result1 = await response1.json();
                addLog(`📊 Response: ${JSON.stringify(result1, null, 2)}`);

                // Test endpoint di test
                addLog("\n🧪 Test endpoint di test (senza autenticazione)...");
                const response2 = await fetch('http://127.0.0.1:8003/api/operativo/sof/archivia-json/test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        periodo_tipo: 'anno',
                        periodo_valore: '2024'
                    })
                });

                addLog(`📡 Response status: ${response2.status}`);
                const result2 = await response2.json();
                addLog(`📊 Response: ${JSON.stringify(result2, null, 2)}`);

                if (response2.status === 200 && result2.success) {
                    addLog("✅ Endpoint di test funziona - La logica di archiviazione è OK!");
                }

            } catch (error) {
                addLog(`❌ Errore: ${error.message}`);
            }
        }

        // Inizializzazione
        addLog("🚀 Test JavaScript caricato");
        addLog("📋 Seleziona un tipo periodo per testare i campi dinamici");
    </script>
</body>
</html>
