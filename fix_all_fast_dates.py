#!/usr/bin/env python3
"""
Script per correggere le date malformate nel campo all_fast
"""

import sys
import os
from datetime import datetime

# Aggiungi il percorso del progetto
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_all_fast_dates():
    """Corregge le date malformate nel campo all_fast"""
    
    print("🔧 Correzione date malformate nel campo all_fast")
    print("=" * 60)
    
    try:
        from database import get_db
        from sqlalchemy import text
        
        db = next(get_db())
        
        # 1. Trova tutti i record con date malformate
        print("1️⃣ Ricerca date malformate...")
        malformed_dates = db.execute(text("""
            SELECT id, viaggio_id, all_fast
            FROM "ORARI"
            WHERE all_fast IS NOT NULL
            AND EXTRACT(YEAR FROM all_fast) < 1900
        """)).fetchall()
        
        print(f"   📊 Trovati {len(malformed_dates)} record con date malformate")
        
        if not malformed_dates:
            print("   ✅ Nessuna data malformata trovata!")
            return True
        
        # 2. Mostra le date problematiche
        print("\n2️⃣ Date problematiche trovate:")
        for record in malformed_dates:
            orari_id = record[0]
            viaggio_id = record[1]
            bad_date = record[2]
            print(f"   ID {orari_id} (Viaggio {viaggio_id}): {bad_date}")
        
        # 3. Correggi le date
        print("\n3️⃣ Correzione date...")
        fixed_count = 0
        
        for record in malformed_dates:
            orari_id = record[0]
            viaggio_id = record[1]
            bad_date = record[2]
            
            # Estrai i componenti della data
            month = bad_date.month
            day = bad_date.day
            hour = bad_date.hour
            minute = bad_date.minute
            second = bad_date.second
            
            # Crea una nuova data con anno 2025
            corrected_date = datetime(2025, month, day, hour, minute, second)
            
            # Aggiorna il record
            db.execute(text("""
                UPDATE "ORARI"
                SET all_fast = :corrected_date
                WHERE id = :orari_id
            """), {
                "corrected_date": corrected_date,
                "orari_id": orari_id
            })
            
            print(f"   ✅ ID {orari_id}: {bad_date} → {corrected_date}")
            fixed_count += 1
        
        # 4. Commit delle modifiche
        db.commit()
        print(f"\n4️⃣ Commit completato: {fixed_count} record corretti")
        
        # 5. Verifica finale
        print("\n5️⃣ Verifica finale...")
        remaining_bad = db.execute(text("""
            SELECT COUNT(*)
            FROM "ORARI"
            WHERE all_fast IS NOT NULL
            AND EXTRACT(YEAR FROM all_fast) < 1900
        """)).scalar()
        
        if remaining_bad == 0:
            print("   ✅ Tutte le date sono state corrette!")
        else:
            print(f"   ⚠️ Rimangono ancora {remaining_bad} date problematiche")
        
        # 6. Test specifico viaggio 39
        print("\n6️⃣ Test specifico viaggio 39...")
        viaggio_39_data = db.execute(text("""
            SELECT all_fast
            FROM "ORARI"
            WHERE viaggio_id = 39
        """)).fetchone()
        
        if viaggio_39_data and viaggio_39_data[0]:
            corrected_all_fast = viaggio_39_data[0]
            formatted_value = corrected_all_fast.strftime('%Y-%m-%dT%H:%M')
            print(f"   ✅ Viaggio 39 all_fast: {corrected_all_fast}")
            print(f"   📝 Formato HTML: {formatted_value}")
        else:
            print("   ❌ Viaggio 39 non ha all_fast")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore durante la correzione: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def check_other_date_fields():
    """Verifica se ci sono problemi simili in altri campi data"""
    
    print("\n" + "=" * 60)
    print("🔍 Verifica altri campi data")
    print("=" * 60)
    
    try:
        from database import get_db
        from sqlalchemy import text
        
        db = next(get_db())
        
        # Campi data da verificare
        date_fields = ['sbe', 'pilota_arrivo', 'soc', 'pilota_partenza', 'foc']
        
        for field in date_fields:
            print(f"\n📅 Verifica campo {field}...")
            
            bad_dates = db.execute(text(f"""
                SELECT COUNT(*)
                FROM "ORARI"
                WHERE {field} IS NOT NULL
                AND EXTRACT(YEAR FROM {field}) < 1900
            """)).scalar()

            if bad_dates and bad_dates > 0:
                print(f"   ⚠️ Trovate {bad_dates} date malformate in {field}")
                
                # Mostra alcuni esempi
                examples = db.execute(text(f"""
                    SELECT id, viaggio_id, {field}
                    FROM "ORARI"
                    WHERE {field} IS NOT NULL
                    AND EXTRACT(YEAR FROM {field}) < 1900
                    LIMIT 3
                """)).fetchall()
                
                for example in examples:
                    print(f"      ID {example[0]} (Viaggio {example[1]}): {example[2]}")
            else:
                print(f"   ✅ Campo {field} OK")
        
    except Exception as e:
        print(f"❌ Errore durante la verifica: {e}")

if __name__ == "__main__":
    print("🚀 Correzione date malformate campo all_fast")
    print("=" * 60)
    
    # Correzione date all_fast
    success = fix_all_fast_dates()
    
    # Verifica altri campi
    check_other_date_fields()
    
    print("\n" + "=" * 60)
    print("📊 Risultato:")
    if success:
        print("✅ Correzione completata con successo!")
        print("\n💡 Prossimi passi:")
        print("1. Ricarica la pagina /operativo/sof/viaggio/39")
        print("2. Verifica che il campo all_fast sia ora visibile")
        print("3. Controlla che la data sia corretta (2025 invece di 0202)")
    else:
        print("❌ Correzione fallita")
    
    print("\n🔗 URL di test: http://localhost:8000/operativo/sof/viaggio/39")
