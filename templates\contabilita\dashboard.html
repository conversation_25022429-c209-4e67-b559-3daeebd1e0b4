<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Contabilità - SNIP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- CSS Card Dashboard Professionali -->
    <link rel="stylesheet" href="/static/css/dashboard-cards.css">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .main-content {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <!-- Navbar principale -->
    {% include 'components/navbar.html' %}

    <div class="container main-content">
        <h1>Dashboard Reparto Contabilità</h1>
        <!-- Stat Cards Row -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card stat-card success" onclick="window.location.href='/contabilita/fatture'">
                    <div class="card-body text-center">
                        <i class="fas fa-file-invoice stat-icon"></i>
                        <div class="stat-number counter" data-target="156">0</div>
                        <div class="stat-label">Fatture Attive</div>
                        <small class="text-muted d-block mt-1">Gestisci fatture e documenti</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stat-card info" onclick="window.location.href='/contabilita/pagamenti'">
                    <div class="card-body text-center">
                        <i class="fas fa-credit-card stat-icon"></i>
                        <div class="stat-number counter" data-target="89">0</div>
                        <div class="stat-label">Pagamenti</div>
                        <small class="text-muted d-block mt-1">Gestisci transazioni</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stat-card warning" onclick="window.location.href='/contabilita/report'">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line stat-icon"></i>
                        <div class="stat-number counter" data-target="24">0</div>
                        <div class="stat-label">Report</div>
                        <small class="text-muted d-block mt-1">Analisi finanziarie</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Seconda riga di statistiche -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card stat-card primary" onclick="window.location.href='/contabilita/bilanci'">
                    <div class="card-body text-center">
                        <i class="fas fa-balance-scale stat-icon"></i>
                        <div class="stat-number counter" data-target="12">0</div>
                        <div class="stat-label">Bilanci</div>
                        <small class="text-muted d-block mt-1">Gestione bilanci</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stat-card danger" onclick="window.location.href='/contabilita/costi'">
                    <div class="card-body text-center">
                        <i class="fas fa-coins stat-icon"></i>
                        <div class="stat-number counter" data-target="67">0</div>
                        <div class="stat-label">Costi</div>
                        <small class="text-muted d-block mt-1">Controllo spese</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stat-card success">
                    <div class="card-body text-center">
                        <i class="fas fa-euro-sign stat-icon"></i>
                        <div class="stat-number counter" data-target="98.5">0</div>
                        <div class="stat-label">Efficienza %</div>
                        <small class="text-muted d-block mt-1">Performance finanziaria</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // Animazione counter per dashboard contabilità
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🎨 Inizializzazione dashboard contabilità moderna...');

        // Trova tutti gli elementi con classe counter
        const counters = document.querySelectorAll('.counter');

        // Aggiungi effetti alle card
        initializeCardEffects();

        counters.forEach(counter => {
            const target = parseFloat(counter.getAttribute('data-target')) || 0;
            console.log(`Counter trovato con target: ${target}`);

            // Anima il counter con effetti moderni
            animateCounterModern(counter, target);
        });
    });

    // Inizializza effetti moderni per le card
    function initializeCardEffects() {
        const cards = document.querySelectorAll('.stat-card');

        cards.forEach((card, index) => {
            // Animazione di entrata scaglionata
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 150);

            // Effetto hover avanzato
            card.addEventListener('mouseenter', function() {
                this.classList.add('glow');
                const icon = this.querySelector('.stat-icon');
                if (icon) {
                    icon.style.transform = 'scale(1.1) rotate(5deg)';
                }
            });

            card.addEventListener('mouseleave', function() {
                this.classList.remove('glow');
                const icon = this.querySelector('.stat-icon');
                if (icon) {
                    icon.style.transform = 'scale(1) rotate(0deg)';
                }
            });
        });
    }

    // Animazione counter moderna con effetti
    function animateCounterModern(element, target) {
        const duration = 2000;
        const start = 0;
        const startTime = performance.now();
        const isDecimal = target % 1 !== 0;

        element.classList.add('animate');

        function updateCounter(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            const easeOutCubic = 1 - Math.pow(1 - progress, 3);
            const currentValue = start + (target - start) * easeOutCubic;

            if (isDecimal) {
                element.textContent = currentValue.toFixed(1);
            } else {
                element.textContent = Math.floor(currentValue);
            }

            if (progress >= 0.9 && progress < 1) {
                element.style.transform = 'scale(1.1)';
            } else if (progress === 1) {
                element.style.transform = 'scale(1)';
                element.classList.remove('animate');

                element.style.textShadow = '0 0 20px rgba(255, 255, 255, 0.8)';
                setTimeout(() => {
                    element.style.textShadow = '0 2px 4px rgba(0, 0, 0, 0.3)';
                }, 500);
            }

            if (progress < 1) {
                requestAnimationFrame(updateCounter);
            } else {
                if (isDecimal) {
                    element.textContent = target.toFixed(1);
                } else {
                    element.textContent = target;
                }
            }
        }

        setTimeout(() => {
            requestAnimationFrame(updateCounter);
        }, 300);
    }
    </script>
</body>
</html> 