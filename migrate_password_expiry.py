#!/usr/bin/env python3
"""
Migrazione per aggiungere la colonna last_password_change alla tabella AGENTE
"""

import sys
import os
from sqlalchemy import text
from database import SessionLocal
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_last_password_change_column():
    """Aggiunge la colonna last_password_change alla tabella AGENTE se non esiste"""
    db = SessionLocal()
    
    try:
        logger.info("🔄 Inizio migrazione colonna last_password_change...")
        
        # Verifica se la colonna esiste già
        result = db.execute(text("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'AGENTE' AND column_name = 'last_password_change'
        """)).fetchone()

        if not result:
            logger.info("📝 Aggiunta colonna last_password_change alla tabella AGENTE...")
            
            # Aggiungi la colonna con valore predefinito alla data corrente
            db.execute(text("""
                ALTER TABLE "AGENTE"
                ADD COLUMN last_password_change TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            """))

            # Crea indice per performance
            try:
                db.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_agente_last_password_change
                    ON "AGENTE" (last_password_change)
                """))
                logger.info("📊 Indice idx_agente_last_password_change creato")
            except Exception as e:
                logger.warning(f"⚠️ Errore creazione indice: {e}")

            db.commit()
            logger.info("✅ Colonna last_password_change aggiunta alla tabella AGENTE")

            # Aggiorna tutti gli utenti esistenti con la data corrente
            logger.info("🔄 Aggiornamento utenti esistenti...")
            result = db.execute(text("""
                UPDATE "AGENTE"
                SET last_password_change = CURRENT_TIMESTAMP
                WHERE last_password_change IS NULL
            """))
            
            rows_updated = result.rowcount
            db.commit()
            logger.info(f"✅ Aggiornati {rows_updated} utenti esistenti con data cambio password corrente")
            
        else:
            logger.info("✅ Colonna last_password_change già presente nella tabella AGENTE")

        # Verifica finale
        logger.info("🔍 Verifica finale della migrazione...")
        
        # Controlla struttura tabella
        result = db.execute(text("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_name = 'AGENTE'
            ORDER BY ordinal_position
        """)).fetchall()
        
        logger.info("📋 Struttura attuale tabella AGENTE:")
        for row in result:
            logger.info(f"   - {row[0]} ({row[1]}) - Nullable: {row[2]} - Default: {row[3]}")
        
        # Controlla dati utenti
        result = db.execute(text("""
            SELECT 
                email, 
                last_password_change,
                CASE 
                    WHEN last_password_change IS NULL THEN 'NULL'
                    ELSE 'SET'
                END as status
            FROM "AGENTE"
            ORDER BY email
        """)).fetchall()
        
        logger.info("👥 Stato last_password_change per utenti:")
        for row in result:
            logger.info(f"   - {row[0]}: {row[2]} ({row[1]})")
        
        # Verifica indici
        result = db.execute(text("""
            SELECT indexname, indexdef
            FROM pg_indexes
            WHERE tablename = 'AGENTE' AND indexname LIKE '%password%'
        """)).fetchall()
        
        if result:
            logger.info("📊 Indici relativi a password:")
            for row in result:
                logger.info(f"   - {row[0]}: {row[1]}")
        else:
            logger.info("📊 Nessun indice relativo a password trovato")

    except Exception as e:
        logger.error(f"❌ Errore durante la migrazione: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def add_password_expiry_config():
    """Aggiunge la configurazione password_expiry_days se non esiste"""
    db = SessionLocal()
    
    try:
        logger.info("⚙️ Verifica configurazione password_expiry_days...")
        
        # Verifica se la tabella SYSTEM_CONFIG esiste
        result = db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'SYSTEM_CONFIG'
            )
        """)).scalar()
        
        if not result:
            logger.info("📝 Creazione tabella SYSTEM_CONFIG...")
            db.execute(text("""
                CREATE TABLE IF NOT EXISTS "SYSTEM_CONFIG" (
                    id SERIAL PRIMARY KEY,
                    config_key VARCHAR(255) UNIQUE NOT NULL,
                    config_value TEXT,
                    config_type VARCHAR(50) DEFAULT 'string',
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))
            db.commit()
            logger.info("✅ Tabella SYSTEM_CONFIG creata")
        
        # Verifica se la configurazione password_expiry_days esiste
        result = db.execute(text("""
            SELECT config_value FROM "SYSTEM_CONFIG"
            WHERE config_key = 'password_expiry_days'
        """)).fetchone()
        
        if not result:
            logger.info("📝 Aggiunta configurazione password_expiry_days...")
            db.execute(text("""
                INSERT INTO "SYSTEM_CONFIG" (config_key, config_value, config_type, description)
                VALUES ('password_expiry_days', '90', 'integer', 'Numero di giorni dopo i quali la password scade (0 = mai)')
            """))
            db.commit()
            logger.info("✅ Configurazione password_expiry_days aggiunta (default: 90 giorni)")
        else:
            logger.info(f"✅ Configurazione password_expiry_days già presente: {result[0]} giorni")
            
    except Exception as e:
        logger.error(f"❌ Errore configurazione: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def main():
    """Esegue la migrazione completa"""
    logger.info("🚀 Inizio migrazione sistema scadenza password...")
    
    try:
        # 1. Aggiungi colonna last_password_change
        add_last_password_change_column()
        
        # 2. Aggiungi configurazione password_expiry_days
        add_password_expiry_config()
        
        logger.info("🎉 Migrazione completata con successo!")
        logger.info("")
        logger.info("📋 RIEPILOGO:")
        logger.info("   ✅ Colonna last_password_change aggiunta alla tabella AGENTE")
        logger.info("   ✅ Indice per performance creato")
        logger.info("   ✅ Utenti esistenti aggiornati con timestamp corrente")
        logger.info("   ✅ Configurazione password_expiry_days aggiunta (90 giorni)")
        logger.info("")
        logger.info("🎛️ CONFIGURAZIONE:")
        logger.info("   - Vai a Dashboard > Amministrazione > Configurazioni > Sicurezza")
        logger.info("   - Modifica 'Scadenza Password (giorni)' secondo necessità")
        logger.info("   - 0 o negativo = password non scadono mai")
        logger.info("")
        logger.info("🧪 TEST:")
        logger.info("   - Imposta scadenza a 1 giorno per test rapido")
        logger.info("   - Modifica last_password_change di un utente test")
        logger.info("   - Prova login per verificare reindirizzamento")
        
    except Exception as e:
        logger.error(f"💥 Migrazione fallita: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
