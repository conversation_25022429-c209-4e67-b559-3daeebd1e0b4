@echo off
echo ========================================
echo   AVVIO APPLICAZIONE SNIP CON 2FA SMS
echo ========================================
echo.

REM Verifica se l'ambiente virtuale esiste
if not exist "venv_new\Scripts\python.exe" (
    echo ERRORE: Ambiente virtuale non trovato!
    echo Crea l'ambiente virtuale con: python -m venv venv_new
    pause
    exit /b 1
)

echo Uso ambiente virtuale: venv_new
echo.

REM Avvia l'applicazione con l'ambiente virtuale
echo Avvio applicazione con sistema 2FA...
echo Preparati a ricevere un SMS al numero 3805127005
echo.

venv_new\Scripts\python.exe main.py

echo.
echo Applicazione terminata.
pause
