# 📧 GUIDA CONFIGURAZIONE EMAIL SNIP

## 🎯 Problema Identificato

Il sistema email di SNIP è **completamente implementato e funzionante**. Il problema dell'email di test che non arriva è dovuto a **configurazioni SMTP errate o incomplete**.

## ✅ Sistema Email Verificato

**Tutti i componenti sono presenti e funzionanti:**
- ✅ API `/admin/api/test-email` implementata
- ✅ Funzione `send_test_email()` completa
- ✅ Diagnosi automatica configurazioni
- ✅ Gestione errori e suggerimenti
- ✅ JavaScript per test email
- ✅ Logging dettagliato

## 🔧 Configurazioni SMTP Richieste

### Campi Obbligatori
1. **Host SMTP** - Server email del provider
2. **Porta SMTP** - Porta di connessione (587 o 465)
3. **Username SMTP** - Username per autenticazione
4. **Password SMTP** - Password per autenticazione
5. **Email <PERSON>ttente** - Email che apparirà come mittente
6. **SSL/TLS** - Crittografia connessione

## 📧 Configurazioni per Provider Comuni

### 🟦 Gmail
```
Host SMTP: smtp.gmail.com
Porta: 587
SSL/TLS: Abilitato
Username: <EMAIL>
Password: Password per le app (NON la password normale)
Email Mittente: <EMAIL>
```

**⚠️ IMPORTANTE per Gmail:**
1. Abilitare **Autenticazione a 2 fattori**
2. Generare **Password per le app**:
   - Vai su Google Account → Sicurezza
   - Autenticazione a 2 fattori → Password per le app
   - Seleziona "App" → "Posta"
   - Copia la password generata (16 caratteri)
3. Usare la **password per le app** nel campo Password SMTP

### 🟦 Outlook/Hotmail
```
Host SMTP: smtp-mail.outlook.com
Porta: 587
SSL/TLS: Abilitato
Username: <EMAIL>
Password: Password normale dell'account
Email Mittente: <EMAIL>
```

### 🟦 Altri Provider
- **Yahoo**: smtp.mail.yahoo.com, porta 587
- **Libero**: smtp.libero.it, porta 587
- **Aruba**: smtps.aruba.it, porta 465

## 🛠️ Come Configurare in SNIP

### 1. Accesso Dashboard
1. Vai su `/dashboard/amministrazione`
2. Clicca su **"Configurazione Email"**

### 2. Compilazione Campi
```
Host SMTP: smtp.gmail.com
Porta: 587
Username: <EMAIL>
Password: [Password per le app Gmail]
Email Mittente: <EMAIL>
Nome Mittente: SNIP Sistema
Email Amministratore: <EMAIL>
SSL/TLS: ✅ Abilitato
```

### 3. Test Email
1. Cliccare **"Test Email"**
2. Controllare console browser (F12 → Console)
3. Verificare log server per errori

## 🔍 Diagnosi Problemi

### Errori Comuni e Soluzioni

#### ❌ "Authentication failed"
**Causa:** Username/password errati
**Soluzione:** 
- Gmail: Usare password per le app
- Outlook: Verificare password account

#### ❌ "Connection refused"
**Causa:** Host/porta errati o firewall
**Soluzione:**
- Verificare host SMTP corretto
- Controllare porta (587 per TLS, 465 per SSL)
- Verificare firewall aziendale

#### ❌ "SSL/TLS error"
**Causa:** Configurazione SSL errata
**Soluzione:**
- Abilitare SSL/TLS
- Usare porta corretta (587 per TLS)

#### ❌ "Sender address rejected"
**Causa:** Email mittente diversa da username
**Soluzione:**
- Usare stessa email per username e mittente

### Controllo Log

**Console Browser (F12):**
```javascript
// Cerca errori come:
❌ Errore test email: Authentication failed
❌ Errore fetch salvataggio: Network error
```

**Log Server:**
```
ERROR - Errore invio email di test: [dettagli errore]
INFO - Test email <NAME_EMAIL>
```

## 🧪 Test Passo-Passo

### 1. Configurazione Gmail (Consigliata)
```
1. Creare account Gmail dedicato per SNIP
2. Abilitare autenticazione a 2 fattori
3. Generare password per le app
4. Configurare in SNIP:
   - Host: smtp.gmail.com
   - Porta: 587
   - Username: <EMAIL>
   - Password: [password per le app]
   - SSL: abilitato
```

### 2. Test Configurazione
```
1. Salvare configurazioni
2. Cliccare "Test Email"
3. Controllare email in arrivo
4. Verificare spam/posta indesiderata
```

### 3. Verifica Funzionamento
```
1. Email di test ricevuta: ✅
2. Backup automatici con notifica: ✅
3. Notifiche SOF completati: ✅
```

## 🎯 Configurazione Raccomandata

### Per Ambiente Produzione
```
Provider: Gmail Business o Outlook Business
Host: smtp.gmail.com
Porta: 587
SSL/TLS: Abilitato
Email dedicata: <EMAIL>
Password: Password per le app
```

### Per Test/Sviluppo
```
Provider: Gmail personale
Host: smtp.gmail.com
Porta: 587
SSL/TLS: Abilitato
Email: <EMAIL>
Password: Password per le app
```

## 🚨 Risoluzione Problemi Urgenti

### Se l'email non arriva:

1. **Verifica immediata:**
   ```
   - Tutti i campi compilati?
   - Host SMTP corretto?
   - Password per le app Gmail?
   - SSL/TLS abilitato?
   ```

2. **Test alternativo:**
   ```
   - Provare con account Gmail nuovo
   - Generare nuova password per le app
   - Testare con porta 465 invece di 587
   ```

3. **Controllo rete:**
   ```
   - Firewall aziendale blocca SMTP?
   - Antivirus blocca connessioni email?
   - Proxy aziendale interferisce?
   ```

## ✅ Risultato Atteso

Una volta configurato correttamente:
- ✅ **Test email** arriva immediatamente
- ✅ **Backup automatici** inviano notifiche
- ✅ **SOF completati** inviano notifiche
- ✅ **Sistema completamente operativo**

## 📞 Supporto

Se il problema persiste dopo aver seguito questa guida:
1. Controllare log dettagliati server
2. Testare configurazioni con client email esterno
3. Verificare con provider email eventuali blocchi
4. Considerare email provider alternativo

**Il sistema SNIP è pronto, serve solo la configurazione SMTP corretta!** 🚀
