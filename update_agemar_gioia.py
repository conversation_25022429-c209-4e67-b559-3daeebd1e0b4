#!/usr/bin/env python3
"""
Script per aggiornare i valori Agemar_GioiaTauro nel database
leggendo dal file gioia.xls
"""

import pandas as pd
from sqlalchemy import text
from database import SessionLocal
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def read_excel_file():
    """Legge il file Excel gioia.xls"""
    try:
        # Leggi il file Excel con header alla riga 0
        df = pd.read_excel('gioia.xls', header=0)

        # Il file ha colonne: 'nave', 'stazza', 'fondo_agemar'
        # Rinomina le colonne per chiarezza
        df = df.rename(columns={
            'nave': 'nave',
            'fondo_agemar': 'agemar_gioia'
        })

        # Seleziona solo le colonne che ci interessano
        df = df[['nave', 'agemar_gioia']]

        # Rimuovi righe vuote
        df = df.dropna()

        # Pulisci i nomi delle navi
        df['nave'] = df['nave'].astype(str).str.strip()

        # Rimuovi suffissi comuni come "GT", "gt"
        df['nave'] = df['nave'].str.replace(r'\s+GT$', '', regex=True, case=False)
        df['nave'] = df['nave'].str.replace(r'\s+gt$', '', regex=True, case=False)
        df['nave'] = df['nave'].str.strip()

        # Converti agemar_gioia in numerico
        df['agemar_gioia'] = pd.to_numeric(df['agemar_gioia'], errors='coerce')

        # Rimuovi righe con valori non numerici o zero
        df = df.dropna(subset=['agemar_gioia'])
        df = df[df['agemar_gioia'] > 0]

        logger.info(f"Lette {len(df)} righe dal file Excel")
        return df

    except Exception as e:
        logger.error(f"Errore lettura file Excel: {e}")
        return None

def update_database(df):
    """Aggiorna il database con i valori Agemar_GioiaTauro"""
    db = SessionLocal()
    
    try:
        updated_count = 0
        not_found_count = 0
        error_count = 0
        
        logger.info("Inizio aggiornamento database...")
        
        for index, row in df.iterrows():
            nave_name = row['nave']
            agemar_gioia_value = row['agemar_gioia']
            
            try:
                # Cerca la nave nel database
                result = db.execute(text('''
                    SELECT "Nave", "Agemar_GioiaTauro" 
                    FROM "NAVI" 
                    WHERE LOWER("Nave") = LOWER(:nave_name)
                '''), {"nave_name": nave_name})
                
                nave_found = result.fetchone()
                
                if nave_found:
                    # Aggiorna il valore Agemar_GioiaTauro
                    db.execute(text('''
                        UPDATE "NAVI" 
                        SET "Agemar_GioiaTauro" = :agemar_gioia
                        WHERE LOWER("Nave") = LOWER(:nave_name)
                    '''), {
                        "agemar_gioia": agemar_gioia_value,
                        "nave_name": nave_name
                    })
                    
                    updated_count += 1
                    logger.info(f"✅ Aggiornata nave '{nave_name}' con Agemar Gioia Tauro: {agemar_gioia_value}")
                    
                else:
                    not_found_count += 1
                    logger.warning(f"⚠️ Nave '{nave_name}' non trovata nel database")
                    
            except Exception as e:
                error_count += 1
                logger.error(f"❌ Errore aggiornamento nave '{nave_name}': {e}")
        
        # Commit delle modifiche
        db.commit()
        
        logger.info(f"\n📊 RIEPILOGO AGGIORNAMENTO:")
        logger.info(f"✅ Navi aggiornate: {updated_count}")
        logger.info(f"⚠️ Navi non trovate: {not_found_count}")
        logger.info(f"❌ Errori: {error_count}")
        logger.info(f"📋 Totale righe processate: {len(df)}")
        
        return updated_count, not_found_count, error_count
        
    except Exception as e:
        db.rollback()
        logger.error(f"Errore generale aggiornamento database: {e}")
        return 0, 0, 1
        
    finally:
        db.close()

def verify_updates():
    """Verifica gli aggiornamenti effettuati"""
    db = SessionLocal()
    
    try:
        # Conta le navi con Agemar_GioiaTauro impostato
        result = db.execute(text('''
            SELECT COUNT(*) 
            FROM "NAVI" 
            WHERE "Agemar_GioiaTauro" IS NOT NULL
        '''))
        
        count_with_gioia = result.scalar()
        
        # Conta le navi totali
        result = db.execute(text('SELECT COUNT(*) FROM "NAVI"'))
        total_navi = result.scalar()
        
        logger.info(f"\n🔍 VERIFICA AGGIORNAMENTI:")
        logger.info(f"📊 Navi con Agemar Gioia Tauro: {count_with_gioia}")
        logger.info(f"📊 Navi totali: {total_navi}")
        percentage = (count_with_gioia/total_navi*100) if total_navi > 0 else 0
        logger.info(f"📊 Percentuale aggiornata: {percentage:.1f}%")
        
        # Mostra alcuni esempi di navi aggiornate
        result = db.execute(text('''
            SELECT "Nave", "Agemar_GioiaTauro" 
            FROM "NAVI" 
            WHERE "Agemar_GioiaTauro" IS NOT NULL 
            ORDER BY "Nave" 
            LIMIT 10
        '''))
        
        examples = result.fetchall()
        
        if examples:
            logger.info(f"\n📋 ESEMPI DI NAVI AGGIORNATE:")
            for nave in examples:
                logger.info(f"   • {nave[0]}: {nave[1]:.2f} €")
        
    except Exception as e:
        logger.error(f"Errore verifica aggiornamenti: {e}")
        
    finally:
        db.close()

def show_not_found_navi(df):
    """Mostra le navi del file Excel non trovate nel database"""
    db = SessionLocal()
    
    try:
        not_found_navi = []
        
        for index, row in df.iterrows():
            nave_name = row['nave']
            
            result = db.execute(text('''
                SELECT COUNT(*) 
                FROM "NAVI" 
                WHERE LOWER("Nave") = LOWER(:nave_name)
            '''), {"nave_name": nave_name})
            
            count = result.scalar()
            
            if count == 0:
                not_found_navi.append({
                    'nome': nave_name,
                    'agemar_gioia': row['agemar_gioia']
                })
        
        if not_found_navi:
            logger.info(f"\n❌ NAVI NON TROVATE NEL DATABASE ({len(not_found_navi)}):")
            for nave in not_found_navi[:20]:  # Mostra solo le prime 20
                logger.info(f"   • {nave['nome']}: {nave['agemar_gioia']:.2f} €")
            
            if len(not_found_navi) > 20:
                logger.info(f"   ... e altre {len(not_found_navi) - 20} navi")
        
    except Exception as e:
        logger.error(f"Errore ricerca navi non trovate: {e}")
        
    finally:
        db.close()

def main():
    """Funzione principale"""
    print("🚢 AGGIORNAMENTO AGEMAR GIOIA TAURO DA FILE EXCEL")
    print("=" * 60)
    
    # Leggi il file Excel
    df = read_excel_file()
    
    if df is None:
        print("❌ Impossibile leggere il file Excel")
        return
    
    print(f"\n📋 File Excel letto con successo:")
    print(f"   • Righe totali: {len(df)}")
    print(f"   • Prime 5 righe:")
    for idx, (_, row) in enumerate(df.head().iterrows()):
        try:
            agemar_value = float(row['agemar_gioia']) if pd.notna(row['agemar_gioia']) else 0.0
            print(f"     {idx+1}. {row['nave']}: {agemar_value:.2f} €")
        except:
            print(f"     {idx+1}. {row['nave']}: {row['agemar_gioia']} (errore formato)")
    
    # Chiedi conferma prima di procedere
    response = input(f"\n❓ Procedere con l'aggiornamento di {len(df)} navi? (s/N): ")
    
    if response.lower() != 's':
        print("❌ Operazione annullata dall'utente")
        return
    
    # Aggiorna il database
    updated, not_found, errors = update_database(df)
    
    # Verifica gli aggiornamenti
    verify_updates()
    
    # Mostra le navi non trovate
    if not_found > 0:
        show_not_found_navi(df)
    
    print(f"\n🎉 AGGIORNAMENTO COMPLETATO!")
    print(f"✅ Navi aggiornate: {updated}")
    print(f"⚠️ Navi non trovate: {not_found}")
    print(f"❌ Errori: {errors}")

if __name__ == "__main__":
    main()
